#!/bin/bash

# Test Server Configuration for iOS Universal Links
# This script validates the server-side setup for Universal Links

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="sandbox-quantumx.panindai-ichilife.co.id"
BUNDLE_ID="id.co.panindaiichilife.quantumx360"
TEAM_ID="VM5X5NX6K3"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test apple-app-site-association file
test_apple_app_site_association() {
    print_header "Testing Apple App Site Association"
    
    local url="https://$DOMAIN/.well-known/apple-app-site-association"
    
    print_info "Testing URL: $url"
    
    # Test HTTP status
    local status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    if [ "$status_code" = "200" ]; then
        print_success "HTTP Status: $status_code (OK)"
    else
        print_error "HTTP Status: $status_code (Expected: 200)"
        return 1
    fi
    
    # Test Content-Type
    local content_type=$(curl -s -I "$url" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r\n')
    if [[ "$content_type" == *"application/json"* ]]; then
        print_success "Content-Type: $content_type"
    else
        print_warning "Content-Type: $content_type (Expected: application/json)"
    fi
    
    # Test JSON validity
    local json_content=$(curl -s "$url")
    if echo "$json_content" | jq . > /dev/null 2>&1; then
        print_success "JSON is valid"
    else
        print_error "JSON is invalid"
        echo "Content received:"
        echo "$json_content"
        return 1
    fi
    
    # Test Team ID and Bundle ID
    if echo "$json_content" | grep -q "$TEAM_ID.$BUNDLE_ID"; then
        print_success "Contains correct Team ID and Bundle ID"
    else
        print_error "Missing or incorrect Team ID ($TEAM_ID) and Bundle ID ($BUNDLE_ID)"
        echo "Expected: $TEAM_ID.$BUNDLE_ID"
        echo "Content:"
        echo "$json_content" | jq .
        return 1
    fi
    
    # Test specific paths
    if echo "$json_content" | grep -q "change-password"; then
        print_success "Contains change-password path configuration"
    else
        print_warning "Missing change-password path configuration"
    fi
    
    print_info "File content:"
    echo "$json_content" | jq .
}

# Test HTTPS configuration
test_https_config() {
    print_header "Testing HTTPS Configuration"
    
    local url="https://$DOMAIN"
    
    # Test SSL certificate
    if curl -s --head "$url" > /dev/null 2>&1; then
        print_success "HTTPS connection successful"
    else
        print_error "HTTPS connection failed"
        return 1
    fi
    
    # Test SSL certificate details
    local cert_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    if [ $? -eq 0 ]; then
        print_success "SSL certificate is valid"
        print_info "Certificate details:"
        echo "$cert_info"
    else
        print_warning "Could not retrieve SSL certificate details"
    fi
}

# Test domain accessibility
test_domain_accessibility() {
    print_header "Testing Domain Accessibility"
    
    # Test DNS resolution
    if nslookup "$DOMAIN" > /dev/null 2>&1; then
        print_success "DNS resolution successful"
    else
        print_error "DNS resolution failed"
        return 1
    fi
    
    # Test ping
    if ping -c 1 "$DOMAIN" > /dev/null 2>&1; then
        print_success "Domain is reachable"
    else
        print_warning "Domain ping failed (might be blocked)"
    fi
}

# Test with different user agents
test_user_agents() {
    print_header "Testing with Different User Agents"
    
    local url="https://$DOMAIN/.well-known/apple-app-site-association"
    
    # Safari iOS
    local safari_ua="Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
    local safari_status=$(curl -s -o /dev/null -w "%{http_code}" -H "User-Agent: $safari_ua" "$url")
    if [ "$safari_status" = "200" ]; then
        print_success "Safari iOS: $safari_status"
    else
        print_error "Safari iOS: $safari_status"
    fi
    
    # Chrome iOS
    local chrome_ua="Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/94.0.4606.76 Mobile/15E148 Safari/604.1"
    local chrome_status=$(curl -s -o /dev/null -w "%{http_code}" -H "User-Agent: $chrome_ua" "$url")
    if [ "$chrome_status" = "200" ]; then
        print_success "Chrome iOS: $chrome_status"
    else
        print_error "Chrome iOS: $chrome_status"
    fi
    
    # iOS System (for Universal Links)
    local ios_system_ua="Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15"
    local ios_status=$(curl -s -o /dev/null -w "%{http_code}" -H "User-Agent: $ios_system_ua" "$url")
    if [ "$ios_status" = "200" ]; then
        print_success "iOS System: $ios_status"
    else
        print_error "iOS System: $ios_status"
    fi
}

# Test caching headers
test_caching() {
    print_header "Testing Caching Configuration"
    
    local url="https://$DOMAIN/.well-known/apple-app-site-association"
    
    local cache_control=$(curl -s -I "$url" | grep -i "cache-control" | cut -d' ' -f2- | tr -d '\r\n')
    if [ -n "$cache_control" ]; then
        print_info "Cache-Control: $cache_control"
        if [[ "$cache_control" == *"max-age"* ]]; then
            print_success "Cache-Control includes max-age"
        else
            print_warning "Cache-Control doesn't include max-age (recommended for performance)"
        fi
    else
        print_warning "No Cache-Control header found"
    fi
}

# Generate test URLs
generate_test_urls() {
    print_header "Test URLs for Manual Testing"
    
    print_info "Copy these URLs to test in different browsers and apps:"
    echo ""
    echo "🏠 Home: https://$DOMAIN/home"
    echo "👤 Profile: https://$DOMAIN/profile"
    echo "🔑 Change Password: https://$DOMAIN/change-password/test-token-123"
    echo "🔄 Reset Password: https://$DOMAIN/reset-password?token=test-token-456"
    echo "📧 Public Form: https://$DOMAIN/public/recruitment"
    echo ""
    
    print_info "Test in these browsers on iOS:"
    echo "• Safari"
    echo "• Chrome"
    echo "• Firefox"
    echo "• Edge"
    echo ""
    
    print_info "Test from these apps:"
    echo "• WhatsApp (send link to yourself)"
    echo "• iMessage"
    echo "• Email apps"
    echo "• Notes app"
}

# Main execution
main() {
    print_header "iOS Universal Links Server Configuration Test"
    echo "Domain: $DOMAIN"
    echo "Bundle ID: $BUNDLE_ID"
    echo "Team ID: $TEAM_ID"
    echo ""
    
    # Run all tests
    test_domain_accessibility
    echo ""
    
    test_https_config
    echo ""
    
    test_apple_app_site_association
    echo ""
    
    test_user_agents
    echo ""
    
    test_caching
    echo ""
    
    generate_test_urls
    
    print_header "Summary"
    print_info "If all tests pass, Universal Links should work in ALL iOS browsers!"
    print_info "Remember: Universal Links work at iOS system level, not browser level."
}

# Check dependencies
if ! command -v jq &> /dev/null; then
    print_warning "jq is not installed. JSON validation will be limited."
    print_info "Install jq with: brew install jq (macOS) or apt-get install jq (Linux)"
fi

if ! command -v openssl &> /dev/null; then
    print_warning "openssl is not installed. SSL certificate validation will be limited."
fi

# Run main function
main
