#!/bin/bash

# iOS Deeplink Debug Script for QuantumX360
# This script helps diagnose iOS deeplink issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="sandbox-quantumx.panindai-ichilife.co.id"
BUNDLE_ID="id.co.panindaiichilife.quantumx360"
TEAM_ID="VM5X5NX6K3"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if a URL is accessible
check_url() {
    local url=$1
    local description=$2
    
    if curl -s --head "$url" | head -n 1 | grep -q "200 OK"; then
        print_success "$description is accessible"
        return 0
    else
        print_error "$description is not accessible"
        return 1
    fi
}

# Function to check apple-app-site-association file
check_apple_app_site_association() {
    print_header "Checking Apple App Site Association"
    
    # Check both with and without .json extension
    local base_url="https://$DOMAIN/.well-known/apple-app-site-association"
    
    print_info "Checking $base_url"
    if curl -s "$base_url" | jq . > /dev/null 2>&1; then
        print_success "apple-app-site-association file is valid JSON"
        
        # Check if it contains the correct Team ID and Bundle ID
        local content=$(curl -s "$base_url")
        if echo "$content" | grep -q "$TEAM_ID.$BUNDLE_ID"; then
            print_success "File contains correct Team ID and Bundle ID"
        else
            print_error "File does not contain correct Team ID ($TEAM_ID) and Bundle ID ($BUNDLE_ID)"
        fi
        
        # Display the content
        print_info "File content:"
        echo "$content" | jq .
    else
        print_error "apple-app-site-association file is not valid JSON or not accessible"
    fi
    
    # Check .json version
    print_info "Checking ${base_url}.json"
    if curl -s "${base_url}.json" | jq . > /dev/null 2>&1; then
        print_warning ".json version exists but iOS prefers without extension"
    fi
}

# Function to check iOS configuration files
check_ios_config() {
    print_header "Checking iOS Configuration"
    
    # Check Info.plist
    if [ -f "ios/Runner/Info.plist" ]; then
        print_success "Info.plist found"
        
        # Check URL schemes
        if grep -q "quantumx360" ios/Runner/Info.plist; then
            print_success "Custom URL scheme 'quantumx360' found in Info.plist"
        else
            print_error "Custom URL scheme 'quantumx360' not found in Info.plist"
        fi
        
        # Check associated domains
        if grep -q "applinks:$DOMAIN" ios/Runner/Info.plist; then
            print_success "Associated domain found in Info.plist"
        else
            print_error "Associated domain not found in Info.plist"
        fi
    else
        print_error "Info.plist not found"
    fi
    
    # Check entitlements
    if [ -f "ios/Runner/Runner.entitlements" ]; then
        print_success "Runner.entitlements found"
        
        if grep -q "applinks:$DOMAIN" ios/Runner/Runner.entitlements; then
            print_success "Associated domain found in entitlements"
        else
            print_error "Associated domain not found in entitlements"
        fi
    else
        print_error "Runner.entitlements not found"
    fi
}

# Function to check deeplink parser
check_deeplink_parser() {
    print_header "Checking Deeplink Parser"
    
    if [ -f "lib/utils/deep_link_parser.dart" ]; then
        print_success "deep_link_parser.dart found"
        
        # Check if change-password is handled
        if grep -q "change-password" lib/utils/deep_link_parser.dart; then
            print_success "change-password path is handled in parser"
        else
            print_error "change-password path is not handled in parser"
        fi
        
        # Check if reset-password is handled
        if grep -q "reset-password" lib/utils/deep_link_parser.dart; then
            print_success "reset-password path is handled in parser"
        else
            print_error "reset-password path is not handled in parser"
        fi
    else
        print_error "deep_link_parser.dart not found"
    fi
}

# Function to test deeplinks
test_deeplinks() {
    print_header "Testing Deeplinks"
    
    print_info "Testing URLs that should work:"
    echo "1. https://$DOMAIN/change-password/TOKEN"
    echo "2. https://$DOMAIN/reset-password?token=TOKEN"
    echo "3. quantumx360://change-password/TOKEN"
    echo "4. quantumx360://reset-password?token=TOKEN"
    echo ""
    
    print_info "To test on iOS device:"
    echo "1. Open Safari on iOS device"
    echo "2. Navigate to the test HTML file: test_ios_deeplinks.html"
    echo "3. Tap the deeplink buttons"
    echo "4. The app should open if deeplinks are working"
    echo ""
    
    print_info "To test on iOS Simulator:"
    echo "1. Open iOS Simulator"
    echo "2. Open Safari in simulator"
    echo "3. Navigate to: https://$DOMAIN/home"
    echo "4. The app should open if deeplinks are working"
}

# Function to provide troubleshooting steps
troubleshooting_steps() {
    print_header "Troubleshooting Steps"
    
    print_info "If iOS deeplinks are not working, try these steps:"
    echo ""
    echo "1. Verify apple-app-site-association file:"
    echo "   - Must be served at https://$DOMAIN/.well-known/apple-app-site-association"
    echo "   - Must be valid JSON"
    echo "   - Must contain correct Team ID: $TEAM_ID"
    echo "   - Must contain correct Bundle ID: $BUNDLE_ID"
    echo ""
    echo "2. Check iOS app configuration:"
    echo "   - Associated Domains capability must be enabled in Xcode"
    echo "   - Entitlements file must contain applinks:$DOMAIN"
    echo "   - Info.plist must contain URL schemes and associated domains"
    echo ""
    echo "3. Test with any browser:"
    echo "   - iOS Universal Links work with ALL browsers (Safari, Chrome, Firefox, Edge)"
    echo "   - Universal Links are processed at iOS system level, not browser level"
    echo "   - Also works from other apps (WhatsApp, Email, etc.)"
    echo ""
    echo "4. Clear iOS cache:"
    echo "   - Delete and reinstall the app"
    echo "   - Reset iOS Simulator if testing on simulator"
    echo ""
    echo "5. Check iOS logs:"
    echo "   - Connect device to Xcode"
    echo "   - Open Console app"
    echo "   - Filter for 'swcd' to see Universal Links logs"
}

# Main execution
main() {
    print_header "iOS Deeplink Diagnostic Tool"
    echo "Domain: $DOMAIN"
    echo "Bundle ID: $BUNDLE_ID"
    echo "Team ID: $TEAM_ID"
    echo ""
    
    check_apple_app_site_association
    echo ""
    
    check_ios_config
    echo ""
    
    check_deeplink_parser
    echo ""
    
    test_deeplinks
    echo ""
    
    troubleshooting_steps
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_warning "jq is not installed. JSON validation will be skipped."
    print_info "Install jq with: brew install jq (macOS) or apt-get install jq (Linux)"
fi

# Run main function
main
