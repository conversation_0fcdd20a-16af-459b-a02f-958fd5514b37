import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Favorite Widget Deduplication Logic Tests', () {
    /// Helper function to simulate the deduplication logic
    List<String> removeDuplicatesFromStringList(List<String> list) {
      List<String> result = [];
      Set<String> seen = <String>{};

      for (String item in list) {
        if (!seen.contains(item)) {
          result.add(item);
          seen.add(item);
        }
      }

      return result;
    }

    test('should remove duplicates from favoriteWidgetIds', () {
      // Arrange: Create a list with duplicates
      List<String> idsWithDuplicates = ['1', '2', '1', '3', '2', '4'];

      // Act: Use the deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(
        idsWithDuplicates,
      );

      // Assert: Check that duplicates are removed
      expect(deduplicatedIds.length, 4);
      expect(deduplicatedIds, ['1', '2', '3', '4']);
      expect(deduplicatedIds.length, lessThan(idsWithDuplicates.length));
    });

    test('should preserve order when removing duplicates', () {
      // Arrange: Create a list with duplicates in specific order
      List<String> idsWithDuplicates = ['3', '1', '4', '1', '2', '3'];

      // Act: Use deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(
        idsWithDuplicates,
      );

      // Assert: Check that order is preserved (first occurrence kept)
      expect(deduplicatedIds, ['3', '1', '4', '2']);
    });

    test('should handle empty list without errors', () {
      // Arrange: Empty list
      List<String> emptyIds = [];

      // Act: Use deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(emptyIds);

      // Assert: Should remain empty
      expect(deduplicatedIds, isEmpty);
    });

    test('should handle list with no duplicates', () {
      // Arrange: List without duplicates
      List<String> uniqueIds = ['1', '2', '3', '4'];

      // Act: Use deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(uniqueIds);

      // Assert: Should remain the same
      expect(deduplicatedIds, uniqueIds);
      expect(deduplicatedIds.length, uniqueIds.length);
    });

    test('should handle single item list', () {
      // Arrange: Single item list
      List<String> singleId = ['1'];

      // Act: Use deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(singleId);

      // Assert: Should remain the same
      expect(deduplicatedIds, singleId);
      expect(deduplicatedIds.length, 1);
    });

    test('should handle list with all same items', () {
      // Arrange: List with all same items
      List<String> sameIds = ['1', '1', '1', '1'];

      // Act: Use deduplication logic
      List<String> deduplicatedIds = removeDuplicatesFromStringList(sameIds);

      // Assert: Should result in single item
      expect(deduplicatedIds, ['1']);
      expect(deduplicatedIds.length, 1);
    });
  });
}
