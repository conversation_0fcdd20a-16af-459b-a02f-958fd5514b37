import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/ktp_ocr_model.dart';
import 'package:pdl_superapp/utils/constants.dart';

void main() {
  group('OCR Notification Tests', () {
    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
    });

    tearDown(() {
      Get.reset();
    });

    test('should have correct popup constants defined', () {
      // Test that the popup constants are properly defined
      expect(kPopupInfo, equals('info'));
      expect(kPopupSuccess, equals('success'));
      expect(kPopupWarning, equals('warning'));
      expect(kPopupFailed, equals('failed'));
    });

    test('KtpOcrModel should be properly structured for OCR results', () {
      // Test that KtpOcrModel can hold OCR data properly
      final ktpData = KtpOcrModel(
        nik: '1234567890123456',
        name: 'JOHN DOE',
        placeBirth: 'JAKARTA',
        birthDay: '01-01-1990',
        gender: 'LAKI-LAKI',
        address: 'JL. TEST NO. 123',
        rt: '001',
        rw: '002',
        subDistrict: 'KELURAHAN TEST',
        district: 'KECAMATAN TEST',
        province: 'DKI JAKARTA',
        city: 'JAKARTA SELATAN',
        marital: 'KAWIN',
      );

      expect(ktpData.nik, equals('1234567890123456'));
      expect(ktpData.name, equals('JOHN DOE'));
      expect(ktpData.placeBirth, equals('JAKARTA'));
      expect(ktpData.birthDay, equals('01-01-1990'));
      expect(ktpData.gender, equals('LAKI-LAKI'));
      expect(ktpData.address, equals('JL. TEST NO. 123'));
      expect(ktpData.rt, equals('001'));
      expect(ktpData.rw, equals('002'));
      expect(ktpData.subDistrict, equals('KELURAHAN TEST'));
      expect(ktpData.district, equals('KECAMATAN TEST'));
      expect(ktpData.province, equals('DKI JAKARTA'));
      expect(ktpData.city, equals('JAKARTA SELATAN'));
      expect(ktpData.marital, equals('KAWIN'));
    });

    test('should verify OCR notification message is appropriate', () {
      // Test the notification message content
      const expectedMessage =
          'Data KTP telah terisi otomatis. Silakan periksa dan pastikan semua data sudah benar.';

      // Verify the message is informative and prompts user to double-check
      expect(expectedMessage.contains('Data KTP'), isTrue);
      expect(expectedMessage.contains('terisi otomatis'), isTrue);
      expect(expectedMessage.contains('periksa'), isTrue);
      expect(expectedMessage.contains('pastikan'), isTrue);
      expect(expectedMessage.contains('benar'), isTrue);
    });
  });
}
