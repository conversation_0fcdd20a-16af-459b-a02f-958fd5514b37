import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/services/ktp_ocr_service.dart';
import 'package:pdl_superapp/models/ktp_ocr_model.dart';

void main() {
  group('KtpOcrService Quality Check', () {
    late XFile goodQualityImage;
    late XFile poorQualityImage;

    setUpAll(() {
      // Create a good quality test image (clear with edges)
      final goodImage = img.Image(width: 200, height: 200);
      img.fill(goodImage, color: img.ColorRgb8(128, 128, 128));
      
      // Add some edges for sharpness
      for (int y = 50; y < 150; y++) {
        for (int x = 50; x < 150; x++) {
          goodImage.setPixel(x, y, img.ColorRgb8(200, 200, 200));
        }
      }
      
      final goodImageBytes = Uint8List.fromList(img.encodeJpg(goodImage));
      goodQualityImage = XFile.fromData(goodImageBytes, name: 'good.jpg');

      // Create a poor quality test image (dark and blurry)
      final poorImage = img.Image(width: 100, height: 100);
      img.fill(poorImage, color: img.ColorRgb8(30, 30, 30)); // Dark and uniform
      
      final poorImageBytes = Uint8List.fromList(img.encodeJpg(poorImage));
      poorQualityImage = XFile.fromData(poorImageBytes, name: 'poor.jpg');
    });

    group('checkImageQuality', () {
      test('should return quality result for image', () async {
        final result = await KtpOcrService.checkImageQuality(goodQualityImage);

        expect(result, isNotNull);
        expect(result.isGoodQuality, isA<bool>());
        expect(result.blurScore, isA<double>());
        expect(result.brightness, isA<double>());
        expect(result.issues, isA<List<String>>());
      });

      test('should detect good quality image', () async {
        final result = await KtpOcrService.checkImageQuality(goodQualityImage);

        expect(result.isGoodQuality, isTrue);
        expect(result.issues, isEmpty);
      });

      test('should detect poor quality image', () async {
        final result = await KtpOcrService.checkImageQuality(poorQualityImage);

        expect(result.isGoodQuality, isFalse);
        expect(result.issues, isNotEmpty);
      });
    });

    group('extractKtp with quality check', () {
      test('should return quality issues for poor quality image', () async {
        final result = await KtpOcrService.extractKtp(poorQualityImage);

        expect(result, isNotNull);
        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, isNotEmpty);
        expect(result.qualityIssuesMessage, isNotEmpty);
        
        // OCR fields should be empty when quality is poor
        expect(result.nik, isNull);
        expect(result.name, isNull);
      });

      test('should process OCR for good quality image', () async {
        final result = await KtpOcrService.extractKtp(goodQualityImage);

        expect(result, isNotNull);
        // Note: Since this is a test image without actual KTP text,
        // we don't expect meaningful OCR results, but quality should pass
        expect(result.hasQualityIssues, isFalse);
        expect(result.qualityIssues, isNull);
      });

      test('should skip quality check when requested', () async {
        final result = await KtpOcrService.extractKtp(
          poorQualityImage,
          skipQualityCheck: true,
        );

        expect(result, isNotNull);
        expect(result.hasQualityIssues, isFalse);
        expect(result.qualityIssues, isNull);
        // OCR should be attempted even with poor quality
      });
    });

    group('KtpOcrModel quality methods', () {
      test('should correctly identify quality issues', () {
        const modelWithIssues = KtpOcrModel(
          qualityIssues: ['Foto terlalu buram', 'Foto terlalu gelap'],
        );

        const modelWithoutIssues = KtpOcrModel(
          nik: '1234567890123456',
          name: 'Test Name',
        );

        const modelWithEmptyIssues = KtpOcrModel(
          qualityIssues: [],
        );

        expect(modelWithIssues.hasQualityIssues, isTrue);
        expect(modelWithIssues.qualityIssuesMessage, 
               equals('Foto terlalu buram, Foto terlalu gelap'));

        expect(modelWithoutIssues.hasQualityIssues, isFalse);
        expect(modelWithoutIssues.qualityIssuesMessage, isEmpty);

        expect(modelWithEmptyIssues.hasQualityIssues, isFalse);
        expect(modelWithEmptyIssues.qualityIssuesMessage, isEmpty);
      });

      test('should handle single quality issue', () {
        const model = KtpOcrModel(
          qualityIssues: ['Foto terlalu buram'],
        );

        expect(model.hasQualityIssues, isTrue);
        expect(model.qualityIssuesMessage, equals('Foto terlalu buram'));
      });

      test('should handle null quality issues', () {
        const model = KtpOcrModel(
          nik: '1234567890123456',
          qualityIssues: null,
        );

        expect(model.hasQualityIssues, isFalse);
        expect(model.qualityIssuesMessage, isEmpty);
      });
    });

    group('Integration scenarios', () {
      test('should handle typical blur scenario', () async {
        // Create a blurry image
        final blurryImage = img.Image(width: 150, height: 150);
        img.fill(blurryImage, color: img.ColorRgb8(100, 100, 100));
        
        final blurryImageBytes = Uint8List.fromList(img.encodeJpg(blurryImage));
        final blurryFile = XFile.fromData(blurryImageBytes, name: 'blurry.jpg');

        final result = await KtpOcrService.extractKtp(blurryFile);

        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, contains('Foto terlalu buram'));
      });

      test('should handle typical dark scenario', () async {
        // Create a dark image with some edges
        final darkImage = img.Image(width: 150, height: 150);
        img.fill(darkImage, color: img.ColorRgb8(20, 20, 20));
        
        // Add some edges to avoid blur detection
        for (int y = 50; y < 100; y++) {
          for (int x = 50; x < 100; x++) {
            darkImage.setPixel(x, y, img.ColorRgb8(40, 40, 40));
          }
        }
        
        final darkImageBytes = Uint8List.fromList(img.encodeJpg(darkImage));
        final darkFile = XFile.fromData(darkImageBytes, name: 'dark.jpg');

        final result = await KtpOcrService.extractKtp(darkFile);

        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, contains('Foto terlalu gelap'));
      });

      test('should handle typical bright scenario', () async {
        // Create a bright image with some edges
        final brightImage = img.Image(width: 150, height: 150);
        img.fill(brightImage, color: img.ColorRgb8(240, 240, 240));
        
        // Add some edges to avoid blur detection
        for (int y = 50; y < 100; y++) {
          for (int x = 50; x < 100; x++) {
            brightImage.setPixel(x, y, img.ColorRgb8(220, 220, 220));
          }
        }
        
        final brightImageBytes = Uint8List.fromList(img.encodeJpg(brightImage));
        final brightFile = XFile.fromData(brightImageBytes, name: 'bright.jpg');

        final result = await KtpOcrService.extractKtp(brightFile);

        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, contains('Foto terlalu terang'));
      });

      test('should handle optimal quality scenario', () async {
        // Create an optimal quality image
        final optimalImage = img.Image(width: 200, height: 200);
        img.fill(optimalImage, color: img.ColorRgb8(120, 120, 120));
        
        // Add clear edges and patterns
        for (int y = 0; y < 200; y += 20) {
          for (int x = 0; x < 200; x++) {
            optimalImage.setPixel(x, y, img.ColorRgb8(160, 160, 160));
          }
        }
        
        for (int x = 0; x < 200; x += 20) {
          for (int y = 0; y < 200; y++) {
            optimalImage.setPixel(x, y, img.ColorRgb8(160, 160, 160));
          }
        }
        
        final optimalImageBytes = Uint8List.fromList(img.encodeJpg(optimalImage));
        final optimalFile = XFile.fromData(optimalImageBytes, name: 'optimal.jpg');

        final result = await KtpOcrService.extractKtp(optimalFile);

        expect(result.hasQualityIssues, isFalse);
        expect(result.qualityIssues, isNull);
      });
    });

    group('Error handling', () {
      test('should handle invalid image file gracefully', () async {
        final invalidBytes = Uint8List.fromList([1, 2, 3, 4, 5]);
        final invalidFile = XFile.fromData(invalidBytes, name: 'invalid.jpg');

        final result = await KtpOcrService.extractKtp(invalidFile);

        expect(result, isNotNull);
        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, isNotEmpty);
      });

      test('should handle empty image file gracefully', () async {
        final emptyBytes = Uint8List(0);
        final emptyFile = XFile.fromData(emptyBytes, name: 'empty.jpg');

        final result = await KtpOcrService.extractKtp(emptyFile);

        expect(result, isNotNull);
        expect(result.hasQualityIssues, isTrue);
        expect(result.qualityIssues, isNotEmpty);
      });
    });
  });
}
