#!/bin/bash

# Deep Link Testing Script for QuantumX-360
# Usage: ./scripts/test_deeplinks.sh [android|ios] [dev|prod]

PACKAGE_NAME="id.co.panindaiichilife.quantumx360"
WEB_DOMAIN="https://sandbox-quantumx.panindai-ichilife.co.id"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test Android deep links
test_android_deeplinks() {
    local flavor=$1
    local scheme=""
    
    if [ "$flavor" = "dev" ]; then
        scheme="quantumx360dev"
    else
        scheme="quantumx360"
    fi
    
    print_info "Testing Android deep links for $flavor flavor..."
    
    # Check if device is connected
    if ! adb devices | grep -q "device$"; then
        print_error "No Android device connected. Please connect a device or start an emulator."
        return 1
    fi
    
    # Test web scheme
    print_info "Testing web scheme: $WEB_DOMAIN/home"
    adb shell am start -W -a android.intent.action.VIEW -d "$WEB_DOMAIN/home" $PACKAGE_NAME
    sleep 2
    
    # Test custom scheme - basic routes
    local routes=("home" "profile" "keagenan" "recruitment" "inbox" "notification")
    
    for route in "${routes[@]}"; do
        print_info "Testing custom scheme: $scheme://$route"
        adb shell am start -W -a android.intent.action.VIEW -d "$scheme://$route" $PACKAGE_NAME
        sleep 2
    done
    
    # Test with parameters
    print_info "Testing reset password with token"
    adb shell am start -W -a android.intent.action.VIEW -d "$scheme://reset-password?token=test123" $PACKAGE_NAME
    sleep 2
    
    print_info "Testing public recruitment with parameters"
    adb shell am start -W -a android.intent.action.VIEW -d "$scheme://public/recruitment?recruiter=123&candidate=456" $PACKAGE_NAME
    sleep 2
    
    print_success "Android deep link testing completed for $flavor flavor"
}

# Function to test iOS deep links
test_ios_deeplinks() {
    local flavor=$1
    local scheme=""
    
    if [ "$flavor" = "dev" ]; then
        scheme="quantumx360dev"
    else
        scheme="quantumx360"
    fi
    
    print_info "Testing iOS deep links for $flavor flavor..."
    
    # Check if iOS Simulator is available
    if ! xcrun simctl list devices | grep -q "Booted"; then
        print_warning "No iOS Simulator is currently booted."
        print_info "Please open iOS Simulator and boot a device, then test manually:"
        print_info "1. Open Safari in iOS Simulator"
        print_info "2. Navigate to: $WEB_DOMAIN/home"
        print_info "3. Test custom schemes: $scheme://home"
        return 1
    fi
    
    # Test custom scheme using xcrun simctl
    print_info "Testing custom scheme: $scheme://home"
    xcrun simctl openurl booted "$scheme://home"
    sleep 2
    
    print_info "Testing web scheme: $WEB_DOMAIN/home"
    xcrun simctl openurl booted "$WEB_DOMAIN/home"
    sleep 2
    
    print_success "iOS deep link testing completed for $flavor flavor"
}

# Function to validate configuration
validate_config() {
    print_info "Validating deep link configuration..."
    
    # Check Android manifest files
    if [ -f "android/app/src/main/AndroidManifest.xml" ]; then
        if grep -q "sandbox-quantumx.panindai-ichilife.co.id" android/app/src/main/AndroidManifest.xml; then
            print_success "Android main manifest configured correctly"
        else
            print_error "Android main manifest missing web domain configuration"
        fi
    else
        print_error "Android main manifest not found"
    fi
    
    # Check iOS Info.plist
    if [ -f "ios/Runner/Info.plist" ]; then
        if grep -q "quantumx360" ios/Runner/Info.plist; then
            print_success "iOS Info.plist configured correctly"
        else
            print_error "iOS Info.plist missing custom scheme configuration"
        fi
    else
        print_error "iOS Info.plist not found"
    fi
    
    # Check .well-known files
    if [ -f ".well-known/assetlinks.json" ]; then
        print_success "Android assetlinks.json found"
    else
        print_error "Android assetlinks.json not found"
    fi
    
    if [ -f ".well-known/apple-app-site-association.json" ]; then
        print_success "iOS apple-app-site-association.json found"
    else
        print_error "iOS apple-app-site-association.json not found"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [android|ios|validate] [dev|prod]"
    echo ""
    echo "Commands:"
    echo "  android [dev|prod]  - Test Android deep links"
    echo "  ios [dev|prod]      - Test iOS deep links"
    echo "  validate            - Validate configuration files"
    echo ""
    echo "Examples:"
    echo "  $0 android dev      - Test Android deep links for development flavor"
    echo "  $0 ios prod         - Test iOS deep links for production flavor"
    echo "  $0 validate         - Validate all configuration files"
}

# Main script logic
case "$1" in
    "android")
        if [ -z "$2" ]; then
            print_error "Please specify flavor: dev or prod"
            show_usage
            exit 1
        fi
        test_android_deeplinks "$2"
        ;;
    "ios")
        if [ -z "$2" ]; then
            print_error "Please specify flavor: dev or prod"
            show_usage
            exit 1
        fi
        test_ios_deeplinks "$2"
        ;;
    "validate")
        validate_config
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
