<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="pdl_superapp">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />

  <title>QuantumX-360</title>
  <link rel="manifest" href="manifest.json">

  <!-- cropperjs -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.css" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.2/cropper.min.js"></script>

  <!-- Firebase SDK -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-analytics-compat.js"></script>

  <!-- Firebase Configuration -->
  <script src="firebase-config.js"></script>

</head>

<body>
  <script src="flutter_bootstrap.js" async></script>

  <script>
    // Register service worker untuk Firebase Messaging
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/firebase-messaging-sw.js')
        .then(function (registration) {
          console.log('Firebase SW registered: ', registration);
        })
        .catch(function (registrationError) {
          console.log('Firebase SW registration failed: ', registrationError);
        });
    }

    // Listen untuk message dari service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', function (event) {
        console.log('Message from SW:', event.data);

        if (event.data && event.data.type === 'NOTIFICATION_CLICK') {
          // Handle navigation dari service worker
          const data = event.data.data;
          const url = event.data.url;

          // Kirim message ke Flutter app jika diperlukan
          if (window.flutter_inappwebview && window.flutter_inappwebview.callHandler) {
            window.flutter_inappwebview.callHandler('notificationClicked', {
              data: data,
              url: url
            });
          }
        }
      });
    }
  </script>
</body>

</html>