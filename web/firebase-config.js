// Firebase Configuration for Web
// Replace these values with your actual Firebase project configuration
// You can find these values in Firebase Console > Project Settings > General > Your apps > Web app

const firebaseConfig = {
  apiKey: "AIzaSyCddT0WPwnGMKrIr7FfxToJkMd4VUvLidk",
  authDomain: "pdl-superapps-uat.firebaseapp.com",
  projectId: "pdl-superapps-uat",
  storageBucket: "pdl-superapps-uat.firebasestorage.app",
  messagingSenderId: "646424487625",
  appId: "1:646424487625:web:5f22af34812af26446cf18",
  measurementId: "G-150B919228"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Analytics
if (firebase.analytics) {
  const analytics = firebase.analytics();
  console.log('Firebase Analytics initialized for web');

  // Enable debug mode in development
  if (window.location.hostname === 'localhost' || window.location.search.includes('debug_mode=1')) {
    console.log('Firebase Analytics debug mode enabled');
    console.log('Check Firebase Console > Analytics > DebugView for real-time events');
    console.log('Screen names will use route-based names, not app title');
  }
} else {
  console.warn('Firebase Analytics not available');
}

// Initialize other Firebase services if needed
if (firebase.messaging && 'serviceWorker' in navigator) {
  // Firebase Messaging is already handled in index.html
  console.log('Firebase Messaging available');
}

// Export config for debugging purposes
window.firebaseConfig = firebaseConfig;
