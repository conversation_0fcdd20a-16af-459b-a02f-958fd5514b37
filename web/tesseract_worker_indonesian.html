<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Tesseract OCR Worker - Indonesian KTP</title>
    <script src="https://unpkg.com/tesseract.js@v4.1.1/dist/tesseract.min.js"></script>
</head>
<body>
    <script>
        console.log('🇮🇩 Indonesian KTP Tesseract OCR Worker starting...');
        
        let worker = null;
        let currentLanguage = null;

        async function createTesseractWorker() {
            try {
                console.log('🚀 Creating Tesseract worker for Indonesian KTP...');
                
                if (typeof Tesseract === 'undefined') {
                    throw new Error('Tesseract.js not available');
                }

                // Create worker with enhanced logging
                const newWorker = await Tesseract.createWorker({
                    logger: m => {
                        console.log(`Tesseract [${m.status}]:`, m.progress ? Math.round(m.progress * 100) + '%' : '');
                        
                        // Send detailed progress to parent
                        if (window.parent && window.parent.postMessage) {
                            window.parent.postMessage({
                                type: 'tesseract_progress',
                                data: {
                                    status: m.status,
                                    progress: m.progress,
                                    language: currentLanguage
                                }
                            }, '*');
                        }
                    },
                    errorHandler: err => {
                        console.error('Tesseract worker error:', err);
                    }
                });

                // Language loading strategy: Indonesian first, English fallback
                const languages = [
                    { code: 'ind', name: 'Indonesian', primary: true },
                    { code: 'eng', name: 'English', primary: false }
                ];

                let lastError = null;
                
                for (const lang of languages) {
                    try {
                        console.log(`🌐 Loading ${lang.name} (${lang.code}) language pack...`);
                        currentLanguage = lang.code;
                        
                        // Load language with timeout
                        const loadPromise = newWorker.loadLanguage(lang.code);
                        const timeoutPromise = new Promise((_, reject) => 
                            setTimeout(() => reject(new Error(`Timeout loading ${lang.code}`)), 30000)
                        );
                        
                        await Promise.race([loadPromise, timeoutPromise]);
                        console.log(`✅ ${lang.name} language pack loaded successfully`);
                        
                        // Initialize with the loaded language
                        console.log(`🔧 Initializing Tesseract with ${lang.name}...`);
                        await newWorker.initialize(lang.code);
                        console.log(`✅ Tesseract initialized with ${lang.name} language`);
                        
                        // Configure for Indonesian KTP if using Indonesian
                        if (lang.code === 'ind') {
                            console.log('🇮🇩 Applying Indonesian KTP optimizations...');
                            // Note: Avoiding setParameters to prevent SetVariable errors
                            // Using default settings which work well for Indonesian text
                        }
                        
                        return newWorker;
                        
                    } catch (langError) {
                        console.warn(`❌ Failed to load ${lang.name} (${lang.code}):`, langError);
                        lastError = langError;
                        
                        if (lang.primary) {
                            console.log('🔄 Indonesian failed, trying English fallback...');
                        }
                        continue;
                    }
                }

                // If we get here, all languages failed
                throw new Error(`Failed to load any language. Last error: ${lastError?.message}`);
                
            } catch (error) {
                console.error('❌ Failed to create Tesseract worker:', error);
                throw error;
            }
        }

        async function performOCR(base64Image) {
            let tempWorker = null;
            try {
                console.log('🔍 Starting Indonesian KTP OCR process...');
                
                // Create fresh worker for each request
                tempWorker = await createTesseractWorker();
                
                // Prepare image data
                const imageData = 'data:image/jpeg;base64,' + base64Image;
                console.log(`📸 Processing image with ${currentLanguage} language...`);
                
                // Perform OCR with current language
                const startTime = Date.now();
                const result = await tempWorker.recognize(imageData);
                const processingTime = Date.now() - startTime;
                
                const text = result.data.text || '';
                const confidence = result.data.confidence || 0;
                
                console.log('✅ Indonesian KTP OCR completed successfully');
                console.log(`📝 Text length: ${text.length} characters`);
                console.log(`🎯 Confidence: ${Math.round(confidence)}%`);
                console.log(`⏱️ Processing time: ${processingTime}ms`);
                console.log(`🌐 Language used: ${currentLanguage}`);
                
                // Log sample of extracted text for debugging
                if (text.length > 0) {
                    const sample = text.substring(0, 100).replace(/\n/g, ' ');
                    console.log(`📄 Text sample: "${sample}${text.length > 100 ? '...' : ''}"`);
                }
                
                return {
                    success: true,
                    text: text,
                    confidence: Math.round(confidence),
                    language: currentLanguage,
                    processingTime: processingTime
                };
                
            } catch (error) {
                console.error('❌ Indonesian KTP OCR failed:', error);
                return {
                    success: false,
                    error: error.message,
                    text: '',
                    language: currentLanguage
                };
            } finally {
                // Always clean up worker
                if (tempWorker) {
                    try {
                        console.log('🧹 Cleaning up Tesseract worker...');
                        await tempWorker.terminate();
                    } catch (e) {
                        console.warn('Warning: Error terminating worker:', e);
                    }
                }
            }
        }

        // Listen for OCR requests
        window.addEventListener('message', async function(event) {
            if (event.data && event.data.type === 'perform_ocr') {
                console.log('📨 Received Indonesian KTP OCR request');
                
                const base64Image = event.data.image;
                if (!base64Image) {
                    console.error('❌ No image data received');
                    return;
                }
                
                const result = await performOCR(base64Image);
                
                // Send result back to parent
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'ocr_result',
                        data: result
                    }, '*');
                    console.log('📤 Indonesian KTP OCR result sent to parent');
                }
            }
        });

        // Test Indonesian language availability on load
        window.addEventListener('load', function() {
            console.log('🌐 Indonesian KTP worker loaded');
            
            setTimeout(async () => {
                if (typeof Tesseract === 'undefined') {
                    console.error('❌ Tesseract.js not available');
                    if (window.parent && window.parent.postMessage) {
                        window.parent.postMessage({
                            type: 'tesseract_error',
                            data: { error: 'Tesseract.js library not available' }
                        }, '*');
                    }
                } else {
                    console.log('✅ Tesseract.js is available');
                    console.log('📦 Version:', Tesseract.version || 'Unknown');
                    console.log('🇮🇩 Ready for Indonesian KTP OCR processing');
                    
                    // Test language availability (optional)
                    try {
                        console.log('🧪 Testing Indonesian language availability...');
                        const testWorker = await Tesseract.createWorker();
                        await testWorker.loadLanguage('ind');
                        await testWorker.terminate();
                        console.log('✅ Indonesian language pack is accessible');
                    } catch (e) {
                        console.warn('⚠️ Indonesian language pack test failed, will fallback to English:', e.message);
                    }
                }
            }, 1000);
        });

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            console.log('🚪 Indonesian KTP worker page unloading...');
        });
    </script>
</body>
</html>
