<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Tesseract OCR Worker - Simple Version</title>
    <script src="https://unpkg.com/tesseract.js@v4.1.1/dist/tesseract.min.js"></script>
</head>

<body>
    <script>
        console.log('🌐 Simple Tesseract OCR Worker starting...');

        let worker = null;
        let isInitialized = false;

        async function initializeTesseract() {
            try {
                console.log('🚀 Initializing Tesseract.js...');

                // Check if Tesseract is available
                if (typeof Tesseract === 'undefined') {
                    throw new Error('Tesseract.js library not available');
                }

                // Create worker with minimal configuration to avoid SetVariable errors
                worker = await Tesseract.createWorker({
                    logger: m => {
                        console.log('Tesseract:', m);
                        if (window.parent && window.parent.postMessage) {
                            window.parent.postMessage({
                                type: 'tesseract_progress',
                                data: m
                            }, '*');
                        }
                    },
                    errorHandler: err => {
                        console.error('Tesseract worker error:', err);
                    }
                });

                // Load English language
                await worker.loadLanguage('eng');
                await worker.initialize('eng');

                console.log('✅ Tesseract.js initialized successfully with English');
                isInitialized = true;
                return true;
            } catch (error) {
                console.error('❌ Error initializing Tesseract:', error);

                // Clean up failed worker
                if (worker) {
                    try {
                        await worker.terminate();
                    } catch (e) {
                        console.warn('Error terminating failed worker:', e);
                    }
                    worker = null;
                }

                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'tesseract_error',
                        data: { error: error.message }
                    }, '*');
                }
                return false;
            }
        }

        async function performOCR(base64Image) {
            try {
                if (!isInitialized || !worker) {
                    console.log('🔄 Tesseract not initialized, initializing now...');
                    const initialized = await initializeTesseract();
                    if (!initialized) {
                        throw new Error('Failed to initialize Tesseract');
                    }
                }

                console.log('🔍 Starting OCR recognition...');

                // Validate worker before use
                if (!worker) {
                    throw new Error('Worker is null');
                }

                // Simple OCR without setParameters to avoid SetVariable errors
                const imageData = 'data:image/jpeg;base64,' + base64Image;

                // Use basic recognize without custom parameters
                const result = await worker.recognize(imageData, {
                    rectangle: { top: 0, left: 0, width: 0, height: 0 }
                });

                const { text, confidence } = result.data;

                console.log('✅ OCR recognition completed');
                console.log('📝 Extracted text:', text);
                console.log('🎯 Confidence:', confidence);

                return {
                    success: true,
                    text: text || '',
                    confidence: Math.round(confidence || 0)
                };
            } catch (error) {
                console.error('❌ OCR recognition failed:', error);

                // Reset initialization flag on error
                isInitialized = false;
                if (worker) {
                    try {
                        await worker.terminate();
                    } catch (e) {
                        console.warn('Error terminating worker after OCR failure:', e);
                    }
                    worker = null;
                }

                return {
                    success: false,
                    error: error.message,
                    text: ''
                };
            }
        }

        // Listen for messages from Flutter
        window.addEventListener('message', async function (event) {
            if (event.data && event.data.type === 'perform_ocr') {
                const base64Image = event.data.image;
                console.log('📨 Received OCR request from Flutter');

                const result = await performOCR(base64Image);

                // Send result back to Flutter
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'ocr_result',
                        data: result
                    }, '*');
                }
            }
        });

        // Initialize when page loads
        window.addEventListener('load', function () {
            console.log('🌐 Simple Tesseract OCR Worker loaded');

            // Check if Tesseract is available
            if (typeof Tesseract === 'undefined') {
                console.error('❌ Tesseract.js not loaded');
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'tesseract_error',
                        data: { error: 'Tesseract.js library not available' }
                    }, '*');
                }
                return;
            }

            console.log('✅ Tesseract.js library is available');
            // Don't initialize immediately, wait for first OCR request
        });

        // Cleanup when page unloads
        window.addEventListener('beforeunload', async function () {
            if (worker) {
                console.log('🧹 Cleaning up Tesseract worker...');
                try {
                    await worker.terminate();
                } catch (e) {
                    console.warn('Error terminating worker:', e);
                }
            }
        });
    </script>
</body>

</html>