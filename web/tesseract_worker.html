<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Tesseract OCR Worker for Indonesian KTP</title>
    <script>
        // Load Tesseract.js with fallback CDNs
        function loadTesseract() {
            return new Promise((resolve, reject) => {
                const cdnUrls = [
                    'https://unpkg.com/tesseract.js@v4.1.1/dist/tesseract.min.js',
                    'https://cdn.jsdelivr.net/npm/tesseract.js@4.1.1/dist/tesseract.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/tesseract.js/4.1.1/tesseract.min.js'
                ];

                let currentIndex = 0;

                function tryLoadScript() {
                    if (currentIndex >= cdnUrls.length) {
                        reject(new Error('All CDN sources failed to load Tesseract.js'));
                        return;
                    }

                    const script = document.createElement('script');
                    script.src = cdnUrls[currentIndex];

                    script.onload = () => {
                        console.log(`✅ Tesseract.js loaded from: ${cdnUrls[currentIndex]}`);
                        resolve();
                    };

                    script.onerror = () => {
                        console.warn(`❌ Failed to load from: ${cdnUrls[currentIndex]}`);
                        currentIndex++;
                        tryLoadScript();
                    };

                    document.head.appendChild(script);
                }

                tryLoadScript();
            });
        }

        // Load Tesseract.js when page loads
        console.log('🌐 Starting Tesseract.js loading process...');
        loadTesseract()
            .then(() => {
                console.log('✅ Tesseract.js loading completed successfully');
            })
            .catch(error => {
                console.error('❌ Failed to load Tesseract.js:', error);
                // Send error to Flutter
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'tesseract_error',
                        data: { error: 'Failed to load Tesseract.js library: ' + error.message }
                    }, '*');
                }
            });
    </script>
</head>

<body>
    <script>
        // Initialize Tesseract worker with Indonesian language support
        let worker = null;

        async function initializeTesseract() {
            try {
                // Check if Tesseract is available
                if (typeof Tesseract === 'undefined') {
                    throw new Error('Tesseract.js library not loaded');
                }

                console.log('🚀 Initializing Tesseract.js with Indonesian language support...');

                // First try with Indonesian language, fallback to English if fails
                let language = 'ind';
                let retryCount = 0;
                const maxRetries = 2;

                while (retryCount < maxRetries) {
                    try {
                        console.log(`Attempt ${retryCount + 1}: Trying with language '${language}'`);

                        worker = await Tesseract.createWorker(language, 1, {
                            logger: m => {
                                console.log('Tesseract:', m);
                                // Send progress updates to Flutter
                                if (window.parent && window.parent.postMessage) {
                                    window.parent.postMessage({
                                        type: 'tesseract_progress',
                                        data: m
                                    }, '*');
                                }
                            },
                            // Add error handling for worker creation
                            errorHandler: err => {
                                console.error('Tesseract worker error:', err);
                            }
                        });

                        // Configure Tesseract for Indonesian KTP recognition with enhanced settings
                        await worker.setParameters({
                            'tessedit_pageseg_mode': '6', // Uniform block of text - optimal for ID cards
                            'tessedit_char_whitelist': 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .,:-/()[]',
                            'preserve_interword_spaces': '1',
                            'tessedit_ocr_engine_mode': '1', // Neural nets LSTM engine only
                            'classify_bln_numeric_mode': '1', // Better number recognition
                            'textord_really_old_xheight': '1', // Better text line detection
                            'textord_min_linesize': '2.5', // Minimum line size for better detection
                            'enable_new_segsearch': '1', // Enable new segmentation search
                        });

                        console.log(`✅ Tesseract.js initialized successfully with language '${language}'`);
                        return true;

                    } catch (langError) {
                        console.warn(`❌ Failed to initialize with language '${language}':`, langError);

                        // Clean up failed worker
                        if (worker) {
                            try {
                                await worker.terminate();
                            } catch (e) {
                                console.warn('Error terminating failed worker:', e);
                            }
                            worker = null;
                        }

                        retryCount++;

                        // Fallback to English if Indonesian fails
                        if (language === 'ind' && retryCount < maxRetries) {
                            console.log('🔄 Falling back to English language...');
                            language = 'eng';
                        }
                    }
                }

                throw new Error(`Failed to initialize Tesseract after ${maxRetries} attempts`);

            } catch (error) {
                console.error('❌ Error initializing Tesseract:', error);

                // Send error to Flutter
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'tesseract_error',
                        data: { error: error.message }
                    }, '*');
                }

                return false;
            }
        }

        // Preprocess image for better KTP OCR accuracy
        function preprocessImageForKTP(canvas, ctx) {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Convert to grayscale and enhance contrast for better OCR
            for (let i = 0; i < data.length; i += 4) {
                // Convert to grayscale using luminance formula
                const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);

                // Enhance contrast for text recognition
                const enhanced = gray > 128 ? Math.min(255, gray * 1.2) : Math.max(0, gray * 0.8);

                data[i] = enhanced;     // Red
                data[i + 1] = enhanced; // Green
                data[i + 2] = enhanced; // Blue
                // Alpha channel remains unchanged
            }

            ctx.putImageData(imageData, 0, 0);
            return canvas.toDataURL('image/png');
        }

        async function performOCR(base64Image) {
            try {
                if (!worker) {
                    const initialized = await initializeTesseract();
                    if (!initialized) {
                        throw new Error('Failed to initialize Tesseract');
                    }
                }

                console.log('🔍 Starting OCR recognition for Indonesian KTP...');

                // Create image element for preprocessing
                const img = new Image();
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // Load and preprocess image
                const preprocessedImage = await new Promise((resolve, reject) => {
                    img.onload = function () {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0);

                        // Apply KTP-specific preprocessing
                        const processedDataUrl = preprocessImageForKTP(canvas, ctx);
                        resolve(processedDataUrl);
                    };
                    img.onerror = reject;
                    img.src = 'data:image/jpeg;base64,' + base64Image;
                });

                console.log('🖼️ Image preprocessing completed');

                // Perform OCR with Indonesian language on preprocessed image
                const { data: { text, confidence } } = await worker.recognize(preprocessedImage);

                console.log('✅ OCR recognition completed');
                console.log('📝 Extracted text:', text);
                console.log('🎯 Confidence:', confidence);

                return {
                    success: true,
                    text: text,
                    confidence: Math.round(confidence)
                };
            } catch (error) {
                console.error('❌ OCR recognition failed:', error);
                return {
                    success: false,
                    error: error.message,
                    text: ''
                };
            }
        }

        // Listen for messages from Flutter
        window.addEventListener('message', async function (event) {
            if (event.data && event.data.type === 'perform_ocr') {
                const base64Image = event.data.image;
                console.log('📨 Received OCR request from Flutter');

                const result = await performOCR(base64Image);

                // Send result back to Flutter
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'ocr_result',
                        data: result
                    }, '*');
                }
            }
        });

        // Initialize Tesseract when page loads and library is ready
        window.addEventListener('load', async function () {
            console.log('🌐 Tesseract OCR Worker loaded');

            // Wait for Tesseract.js to be loaded
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds max wait

            while (typeof Tesseract === 'undefined' && attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (typeof Tesseract === 'undefined') {
                console.error('❌ Tesseract.js failed to load after 5 seconds');
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'tesseract_error',
                        data: { error: 'Tesseract.js library failed to load' }
                    }, '*');
                }
                return;
            }

            console.log('✅ Tesseract.js library is ready');
            initializeTesseract();
        });

        // Cleanup when page unloads
        window.addEventListener('beforeunload', async function () {
            if (worker) {
                console.log('🧹 Cleaning up Tesseract worker...');
                await worker.terminate();
            }
        });
    </script>
</body>

</html>