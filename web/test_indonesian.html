<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Indonesian KTP OCR</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #e6ffe6; border-color: #99ff99; }
        .error { background-color: #ffe6e6; border-color: #ff9999; }
        .info { background-color: #e6f3ff; border-color: #99ccff; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="file"] { margin: 10px 0; }
        iframe { display: none; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>🇮🇩 Test Indonesian KTP OCR with ind.traineddata</h1>
    
    <div class="container info">
        <h3>Indonesian Language Pack Testing</h3>
        <p>This page tests Tesseract.js with Indonesian trained data (ind.traineddata) for KTP recognition.</p>
        
        <button onclick="testLanguageAvailability()">Test Indonesian Language</button>
        <button onclick="testWithIndonesianText()">Test Indonesian Text</button>
        <button onclick="testWithKTPSample()">Test KTP Sample</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <br><br>
        <input type="file" id="imageInput" accept="image/*" onchange="handleImageUpload(event)">
        <button onclick="testWithUploadedImage()" id="testBtn" disabled>Test with Real KTP Image</button>
    </div>

    <div class="container">
        <h3>📊 OCR Statistics</h3>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="languageUsed">-</div>
                <div class="stat-label">Language Used</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="confidence">-</div>
                <div class="stat-label">Confidence %</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="processingTime">-</div>
                <div class="stat-label">Processing Time (ms)</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="textLength">-</div>
                <div class="stat-label">Text Length</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📋 Test Results & Logs</h3>
        <div id="log"></div>
    </div>

    <!-- Hidden iframe for testing Indonesian worker -->
    <iframe id="tesseractWorker" src="tesseract_worker_indonesian.html"></iframe>

    <script>
        let selectedImageBase64 = null;
        let workerReady = false;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            // Reset stats
            document.getElementById('languageUsed').textContent = '-';
            document.getElementById('confidence').textContent = '-';
            document.getElementById('processingTime').textContent = '-';
            document.getElementById('textLength').textContent = '-';
        }

        function updateStats(result) {
            if (result.success) {
                document.getElementById('languageUsed').textContent = result.language || '-';
                document.getElementById('confidence').textContent = result.confidence || '-';
                document.getElementById('processingTime').textContent = result.processingTime || '-';
                document.getElementById('textLength').textContent = result.text ? result.text.length : '-';
            }
        }

        // Listen for messages from Indonesian worker
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'ocr_result') {
                const result = event.data.data;
                updateStats(result);
                
                if (result.success) {
                    log(`✅ Indonesian OCR completed successfully!`, 'success');
                    log(`🌐 Language used: ${result.language}`, 'success');
                    log(`🎯 Confidence: ${result.confidence}%`);
                    log(`⏱️ Processing time: ${result.processingTime}ms`);
                    log(`📝 Text length: ${result.text.length} characters`);
                    
                    if (result.text.length > 0) {
                        const preview = result.text.substring(0, 200).replace(/\n/g, ' ');
                        log(`📄 Text preview: "${preview}${result.text.length > 200 ? '...' : ''}"`);
                        
                        // Check for Indonesian KTP keywords
                        const ktpKeywords = ['NIK', 'Nama', 'Tempat', 'Lahir', 'Kelamin', 'Alamat', 'RT/RW', 'Agama', 'Perkawinan', 'Pekerjaan', 'Kewarganegaraan'];
                        const foundKeywords = ktpKeywords.filter(keyword => result.text.toUpperCase().includes(keyword));
                        if (foundKeywords.length > 0) {
                            log(`🇮🇩 KTP keywords detected: ${foundKeywords.join(', ')}`, 'success');
                        }
                    }
                } else {
                    log(`❌ Indonesian OCR failed: ${result.error}`, 'error');
                    log(`🌐 Language attempted: ${result.language || 'unknown'}`);
                }
            } else if (event.data && event.data.type === 'tesseract_error') {
                log(`❌ Tesseract error: ${event.data.data.error}`, 'error');
            } else if (event.data && event.data.type === 'tesseract_progress') {
                const progress = event.data.data;
                log(`📊 Progress [${progress.language}]: ${progress.status} ${progress.progress ? Math.round(progress.progress * 100) + '%' : ''}`);
            }
        });

        function testLanguageAvailability() {
            log('🧪 Testing Indonesian language availability...');
            
            const iframe = document.getElementById('tesseractWorker');
            if (!iframe.contentWindow) {
                log('❌ Worker iframe not ready', 'error');
                return;
            }
            
            // Create simple test with Indonesian text
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 80;
            const ctx = canvas.getContext('2d');
            
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'black';
            ctx.font = '20px Arial';
            ctx.fillText('REPUBLIK INDONESIA', 50, 40);
            
            const imageData = canvas.toDataURL('image/png');
            const base64Image = imageData.split(',')[1];
            
            log('📸 Created test image with "REPUBLIK INDONESIA"');
            log('📤 Sending to Indonesian worker...');
            
            iframe.contentWindow.postMessage({
                type: 'perform_ocr',
                image: base64Image
            }, '*');
        }

        function testWithIndonesianText() {
            log('🇮🇩 Testing with Indonesian text sample...');
            
            const iframe = document.getElementById('tesseractWorker');
            if (!iframe.contentWindow) {
                log('❌ Worker iframe not ready', 'error');
                return;
            }
            
            const canvas = document.createElement('canvas');
            canvas.width = 500;
            canvas.height = 120;
            const ctx = canvas.getContext('2d');
            
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('NIK : 3171234567890123', 20, 25);
            ctx.fillText('Nama : BUDI SANTOSO', 20, 45);
            ctx.fillText('Jenis Kelamin : LAKI-LAKI', 20, 65);
            ctx.fillText('Agama : ISLAM', 20, 85);
            ctx.fillText('Kewarganegaraan : WNI', 20, 105);
            
            const imageData = canvas.toDataURL('image/png');
            const base64Image = imageData.split(',')[1];
            
            log('📸 Created Indonesian KTP-style text sample');
            log('📤 Sending to Indonesian worker...');
            
            iframe.contentWindow.postMessage({
                type: 'perform_ocr',
                image: base64Image
            }, '*');
        }

        function testWithKTPSample() {
            log('🆔 Testing with comprehensive KTP sample...');
            
            const iframe = document.getElementById('tesseractWorker');
            if (!iframe.contentWindow) {
                log('❌ Worker iframe not ready', 'error');
                return;
            }
            
            const canvas = document.createElement('canvas');
            canvas.width = 600;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'black';
            ctx.font = '12px Arial';
            
            const ktpText = [
                'REPUBLIK INDONESIA',
                'PROVINSI DKI JAKARTA',
                'KOTA JAKARTA SELATAN',
                '',
                'NIK : 3171234567890123',
                'Nama : BUDI SANTOSO',
                'Tempat/Tgl Lahir : JAKARTA, 15-08-1985',
                'Jenis Kelamin : LAKI-LAKI',
                'Alamat : JL. SUDIRMAN NO. 45',
                'RT/RW : 003/005',
                'Agama : ISLAM',
                'Status Perkawinan : KAWIN',
                'Pekerjaan : PEGAWAI SWASTA',
                'Kewarganegaraan : WNI'
            ];
            
            ktpText.forEach((line, index) => {
                if (line) ctx.fillText(line, 20, 20 + (index * 14));
            });
            
            const imageData = canvas.toDataURL('image/png');
            const base64Image = imageData.split(',')[1];
            
            log('📸 Created comprehensive KTP sample');
            log('📤 Sending to Indonesian worker...');
            
            iframe.contentWindow.postMessage({
                type: 'perform_ocr',
                image: base64Image
            }, '*');
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            log(`📸 Real KTP image selected: ${file.name} (${Math.round(file.size / 1024)}KB)`);
            
            const reader = new FileReader();
            reader.onload = function(e) {
                selectedImageBase64 = e.target.result.split(',')[1];
                document.getElementById('testBtn').disabled = false;
                log('✅ Image loaded and ready for Indonesian OCR');
            };
            reader.readAsDataURL(file);
        }

        function testWithUploadedImage() {
            if (!selectedImageBase64) {
                log('❌ No image selected', 'error');
                return;
            }
            
            log('🔄 Processing real KTP image with Indonesian OCR...');
            
            const iframe = document.getElementById('tesseractWorker');
            if (!iframe.contentWindow) {
                log('❌ Worker iframe not ready', 'error');
                return;
            }
            
            log('📤 Sending real KTP image to Indonesian worker...');
            
            iframe.contentWindow.postMessage({
                type: 'perform_ocr',
                image: selectedImageBase64
            }, '*');
        }

        // Initialize when page loads
        window.addEventListener('load', function() {
            log('🌐 Indonesian KTP OCR test page loaded');
            log('⏳ Waiting for Indonesian worker to initialize...');
            
            setTimeout(() => {
                log('✅ Ready to test Indonesian OCR! Click buttons above to start.');
                log('🇮🇩 This will attempt to use ind.traineddata first, fallback to eng.traineddata if needed.');
                workerReady = true;
            }, 3000);
        });
    </script>
</body>
</html>
