<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Tesseract OCR Worker - Minimal Version</title>
    <script src="https://unpkg.com/tesseract.js@v4.1.1/dist/tesseract.min.js"></script>
</head>

<body>
    <script>
        console.log('🌐 Minimal Tesseract OCR Worker starting...');

        let worker = null;

        async function createTesseractWorker() {
            try {
                console.log('🚀 Creating Tesseract worker...');

                if (typeof Tesseract === 'undefined') {
                    throw new Error('Tesseract.js not available');
                }

                // Create worker with minimal configuration
                const newWorker = await Tesseract.createWorker({
                    logger: m => console.log('Tesseract:', m.status, m.progress ? Math.round(m.progress * 100) + '%' : '')
                });

                // Try Indonesian first, fallback to English if fails
                let language = 'ind';
                let attempts = 0;
                const maxAttempts = 2;

                while (attempts < maxAttempts) {
                    try {
                        console.log(`🌐 Attempting to load language: ${language}`);

                        // Load and initialize language
                        await newWorker.loadLanguage(language);
                        await newWorker.initialize(language);

                        console.log(`✅ Tesseract worker created successfully with ${language} language`);
                        return newWorker;

                    } catch (langError) {
                        console.warn(`❌ Failed to load ${language} language:`, langError);
                        attempts++;

                        if (language === 'ind' && attempts < maxAttempts) {
                            console.log('🔄 Falling back to English language...');
                            language = 'eng';
                        } else {
                            throw langError;
                        }
                    }
                }

                throw new Error('Failed to initialize worker with any language');

            } catch (error) {
                console.error('❌ Failed to create Tesseract worker:', error);
                throw error;
            }
        }

        async function performOCR(base64Image) {
            let tempWorker = null;
            try {
                console.log('🔍 Starting OCR process...');

                // Create fresh worker for each request to avoid state issues
                tempWorker = await createTesseractWorker();

                // Prepare image data
                const imageData = 'data:image/jpeg;base64,' + base64Image;
                console.log('📸 Processing image...');

                // Perform OCR with minimal options
                const result = await tempWorker.recognize(imageData);

                const text = result.data.text || '';
                const confidence = result.data.confidence || 0;

                console.log('✅ OCR completed successfully');
                console.log('📝 Text length:', text.length);
                console.log('🎯 Confidence:', Math.round(confidence));

                return {
                    success: true,
                    text: text,
                    confidence: Math.round(confidence)
                };

            } catch (error) {
                console.error('❌ OCR failed:', error);
                return {
                    success: false,
                    error: error.message,
                    text: ''
                };
            } finally {
                // Always clean up worker
                if (tempWorker) {
                    try {
                        console.log('🧹 Cleaning up worker...');
                        await tempWorker.terminate();
                    } catch (e) {
                        console.warn('Warning: Error terminating worker:', e);
                    }
                }
            }
        }

        // Listen for OCR requests
        window.addEventListener('message', async function (event) {
            if (event.data && event.data.type === 'perform_ocr') {
                console.log('📨 Received OCR request');

                const base64Image = event.data.image;
                if (!base64Image) {
                    console.error('❌ No image data received');
                    return;
                }

                const result = await performOCR(base64Image);

                // Send result back to parent
                if (window.parent && window.parent.postMessage) {
                    window.parent.postMessage({
                        type: 'ocr_result',
                        data: result
                    }, '*');
                    console.log('📤 Result sent to parent');
                }
            }
        });

        // Test Tesseract availability on load
        window.addEventListener('load', function () {
            console.log('🌐 Minimal worker loaded');

            setTimeout(() => {
                if (typeof Tesseract === 'undefined') {
                    console.error('❌ Tesseract.js not available');
                    if (window.parent && window.parent.postMessage) {
                        window.parent.postMessage({
                            type: 'tesseract_error',
                            data: { error: 'Tesseract.js library not available' }
                        }, '*');
                    }
                } else {
                    console.log('✅ Tesseract.js is available');
                    console.log('📦 Version:', Tesseract.version || 'Unknown');
                }
            }, 500);
        });

        // Handle page unload
        window.addEventListener('beforeunload', function () {
            console.log('🚪 Worker page unloading...');
        });
    </script>
</body>

</html>