// Firebase Messaging Service Worker untuk Web Push Notifications
// File ini harus berada di root folder web/

importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js');

// Konfigurasi Firebase (sesuaikan dengan firebase_options_dev.dart)
const firebaseConfig = {
  apiKey: 'AIzaSyCddT0WPwnGMKrIr7FfxToJkMd4VUvLidk',
  appId: '1:646424487625:web:6b6999c02c7c229b46cf18',
  messagingSenderId: '646424487625',
  projectId: 'pdl-superapps-uat',
  authDomain: 'pdl-superapps-uat.firebaseapp.com',
  storageBucket: 'pdl-superapps-uat.firebasestorage.app',
};

// Inisialisasi Firebase
firebase.initializeApp(firebaseConfig);

// Dapatkan instance Firebase Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  
  // Customize notification
  const notificationTitle = payload.notification?.title || 'PDL SuperApp';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: '/icons/Icon-192.png',
    badge: '/icons/Icon-192.png',
    tag: 'pdl-notification',
    data: payload.data,
    actions: [
      {
        action: 'open',
        title: 'Open App'
      }
    ]
  };

  // Tampilkan notifikasi
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');
  
  event.notification.close();
  
  // Handle action click
  if (event.action === 'open' || !event.action) {
    // Buka atau fokus ke app
    event.waitUntil(
      clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
        const data = event.notification.data;
        let url = '/';
        
        // Tentukan URL berdasarkan data notifikasi
        if (data && data.screen) {
          switch (data.screen.toLowerCase()) {
            case 'home':
              url = '/home';
              break;
            case 'inbox':
              url = '/inbox';
              break;
            case 'profile':
              url = '/profile';
              break;
            case 'detail':
              if (data.id) {
                url = `/detail?id=${data.id}`;
              }
              break;
            default:
              url = '/';
              break;
          }
        } else if (data && data.route) {
          url = data.route;
        }
        
        // Cari window yang sudah terbuka
        for (let i = 0; i < clientList.length; i++) {
          const client = clientList[i];
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            client.postMessage({
              type: 'NOTIFICATION_CLICK',
              data: data,
              url: url
            });
            return client.focus();
          }
        }
        
        // Jika tidak ada window yang terbuka, buka yang baru
        if (clients.openWindow) {
          return clients.openWindow(self.location.origin + url);
        }
      })
    );
  }
});

// Handle push event (opsional, untuk custom handling)
self.addEventListener('push', function(event) {
  console.log('[firebase-messaging-sw.js] Push received.');
  
  if (event.data) {
    const payload = event.data.json();
    console.log('[firebase-messaging-sw.js] Push data: ', payload);
    
    // Custom logic untuk push event jika diperlukan
  }
});

// Handle message event dari main thread
self.addEventListener('message', function(event) {
  console.log('[firebase-messaging-sw.js] Message received: ', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

console.log('[firebase-messaging-sw.js] Service Worker loaded successfully');
