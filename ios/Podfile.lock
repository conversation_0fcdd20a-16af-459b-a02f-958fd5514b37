PODS:
  - app_links (0.0.2):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - cloud_firestore (5.6.5):
    - Firebase/Firestore (= 11.8.0)
    - firebase_core
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.8.0):
    - Firebase/Core
  - Firebase/Auth (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.8.0)
  - Firebase/Core (11.8.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.8.0)
  - Firebase/CoreOnly (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - Firebase/Database (11.8.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.8.0)
  - Firebase/Firestore (11.8.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 11.8.0)
  - Firebase/Messaging (11.8.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.8.0)
  - firebase_analytics (11.4.4):
    - Firebase/Analytics (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_auth (5.5.1):
    - Firebase/Auth (= 11.8.0)
    - firebase_core
    - Flutter
  - firebase_core (3.12.1):
    - Firebase/CoreOnly (= 11.8.0)
    - Flutter
  - firebase_messaging (15.2.4):
    - Firebase/Messaging (= 11.8.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.8.0):
    - FirebaseAnalytics/AdIdSupport (= 11.8.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.13.0)
  - FirebaseAuth (11.8.1):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseCoreExtension (~> 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 100.0)
  - FirebaseAuthInterop (11.13.0)
  - FirebaseCore (11.8.0):
    - FirebaseCoreInternal (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.8.0):
    - FirebaseCore (~> 11.8.0)
  - FirebaseCoreInternal (11.8.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseDatabase (11.8.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.8.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - leveldb-library (~> 1.22)
  - FirebaseFirestore (11.8.0):
    - FirebaseFirestoreBinary (= 11.8.0)
  - FirebaseFirestoreAbseilBinary (1.2024011602.0)
  - FirebaseFirestoreBinary (11.8.0):
    - FirebaseCore (= 11.8.0)
    - FirebaseCoreExtension (= 11.8.0)
    - FirebaseFirestoreInternalBinary (= 11.8.0)
    - FirebaseSharedSwift (= 11.8.0)
  - FirebaseFirestoreGRPCBoringSSLBinary (1.65.1)
  - FirebaseFirestoreGRPCCoreBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCBoringSSLBinary (= 1.65.1)
  - FirebaseFirestoreGRPCCPPBinary (1.65.1):
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCoreBinary (= 1.65.1)
  - FirebaseFirestoreInternalBinary (11.8.0):
    - FirebaseCore (= 11.8.0)
    - FirebaseFirestoreAbseilBinary (= 1.2024011602.0)
    - FirebaseFirestoreGRPCCPPBinary (= 1.65.1)
    - leveldb-library (~> 1.22)
    - nanopb (~> 3.30910.0)
  - FirebaseInstallations (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.8.0):
    - FirebaseCore (~> 11.8.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - FirebaseSharedSwift (11.8.0)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_mlkit_commons (0.11.0):
    - Flutter
    - MLKitVision
  - google_mlkit_object_detection (0.15.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/ObjectDetection (~> 7.0.0)
    - GoogleMLKit/ObjectDetectionCustom (~> 7.0.0)
  - google_mlkit_text_recognition (0.15.0):
    - Flutter
    - google_mlkit_commons
    - GoogleMLKit/TextRecognition (~> 7.0.0)
  - GoogleAppMeasurement (11.8.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.8.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.8.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.8.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleMLKit/ObjectDetection (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitObjectDetection (~> 6.0.0)
  - GoogleMLKit/ObjectDetectionCustom (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitObjectDetectionCustom (~> 6.0.0)
  - GoogleMLKit/TextRecognition (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitTextRecognition (~> 5.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - leveldb-library (1.22.6)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - MLImage (1.0.0-beta6)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitImageLabelingCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitObjectDetection (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitObjectDetectionCommon (8.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitObjectDetectionCustom (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
    - MLKitVisionKit (~> 9.0)
  - MLKitTextRecognition (5.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitTextRecognitionCommon (= 4.0.0)
    - MLKitVision (~> 8.0)
  - MLKitTextRecognitionCommon (4.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - MLKitVisionKit (9.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitImageLabelingCommon (~> 8.0)
    - MLKitObjectDetectionCommon (~> 8.0)
    - MLKitVision (~> 8.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - "no_screenshot (0.0.1+4)":
    - Flutter
    - ScreenProtectorKit (~> 1.3.1)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - RecaptchaInterop (100.0.0)
  - restart_app (0.0.1):
    - Flutter
  - ScreenProtectorKit (1.3.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Firebase/Database
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - FirebaseFirestore (from `https://github.com/invertase/firestore-ios-sdk-frameworks.git`, tag `11.8.0`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_mlkit_commons (from `.symlinks/plugins/google_mlkit_commons/ios`)
  - google_mlkit_object_detection (from `.symlinks/plugins/google_mlkit_object_detection/ios`)
  - google_mlkit_text_recognition (from `.symlinks/plugins/google_mlkit_text_recognition/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - local_auth_darwin (from `.symlinks/plugins/local_auth_darwin/darwin`)
  - no_screenshot (from `.symlinks/plugins/no_screenshot/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - restart_app (from `.symlinks/plugins/restart_app/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseFirestoreAbseilBinary
    - FirebaseFirestoreBinary
    - FirebaseFirestoreGRPCBoringSSLBinary
    - FirebaseFirestoreGRPCCoreBinary
    - FirebaseFirestoreGRPCCPPBinary
    - FirebaseFirestoreInternalBinary
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - leveldb-library
    - MLImage
    - MLKitCommon
    - MLKitImageLabelingCommon
    - MLKitObjectDetection
    - MLKitObjectDetectionCommon
    - MLKitObjectDetectionCustom
    - MLKitTextRecognition
    - MLKitTextRecognitionCommon
    - MLKitVision
    - MLKitVisionKit
    - nanopb
    - OrderedSet
    - PromisesObjC
    - ReachabilitySwift
    - RecaptchaInterop
    - ScreenProtectorKit
    - TOCropViewController

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.8.0
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_mlkit_commons:
    :path: ".symlinks/plugins/google_mlkit_commons/ios"
  google_mlkit_object_detection:
    :path: ".symlinks/plugins/google_mlkit_object_detection/ios"
  google_mlkit_text_recognition:
    :path: ".symlinks/plugins/google_mlkit_text_recognition/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  local_auth_darwin:
    :path: ".symlinks/plugins/local_auth_darwin/darwin"
  no_screenshot:
    :path: ".symlinks/plugins/no_screenshot/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  restart_app:
    :path: ".symlinks/plugins/restart_app/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

CHECKOUT OPTIONS:
  FirebaseFirestore:
    :git: https://github.com/invertase/firestore-ios-sdk-frameworks.git
    :tag: 11.8.0

SPEC CHECKSUMS:
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  cloud_firestore: e61acbf808607d2c88ee32b00cd3aec027b38b3c
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  Firebase: d80354ed7f6df5f9aca55e9eb47cc4b634735eaf
  firebase_analytics: 4e93dbe66872104d28ae9750fec1800e1fd66858
  firebase_auth: 9ebbd83276bf977dea1c74dfc199acf9d2a2f42f
  firebase_core: 8d552814f6c01ccde5d88939fced4ec26f2f5510
  firebase_messaging: 8b96a4f09841c15a16b96973ef5c3dcfc1a064e4
  FirebaseAnalytics: 4fd42def128146e24e480e89f310e3d8534ea42b
  FirebaseAppCheckInterop: 72066489c209823649a997132bcd9269bc33a4bb
  FirebaseAuth: ad59a1a7b161e75f74c39f70179d2482d40e2737
  FirebaseAuthInterop: 4fa327ec3c551a80a6929561f83af80b1dd44937
  FirebaseCore: 861681f012101000fba3c2a321ed00181d257591
  FirebaseCoreExtension: 3d3f2017a00d06e09ab4ebe065391b0bb642565e
  FirebaseCoreInternal: df24ce5af28864660ecbd13596fc8dd3a8c34629
  FirebaseDatabase: f86d3da3a67e41b551a84a8c5b081d0f86a5a896
  FirebaseFirestore: c3c9dfb2e2ab96bd74a42804bbefb3a7edd32c88
  FirebaseFirestoreAbseilBinary: fa2ebd2ed02cadef5382e4f7c93f1b265c812c85
  FirebaseFirestoreBinary: 072a7121be7fbe601733a039c5ed80936ffb1e9b
  FirebaseFirestoreGRPCBoringSSLBinary: d86ebbe2adc8d15d7ebf305fff7d6358385327f8
  FirebaseFirestoreGRPCCoreBinary: 472bd808e1886a5efb2fd03dd09b98d34641a335
  FirebaseFirestoreGRPCCPPBinary: db76d83d2b7517623f8426ed7f7a17bad2478084
  FirebaseFirestoreInternalBinary: 4695c807651ee9775867026ea85712b3442995f9
  FirebaseInstallations: 6c963bd2a86aca0481eef4f48f5a4df783ae5917
  FirebaseMessaging: 487b634ccdf6f7b7ff180fdcb2a9935490f764e8
  FirebaseSharedSwift: 672954eac7b141d6954fab9a32d45d6b1d922df8
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  google_mlkit_commons: 2abe6a70e1824e431d16a51085cb475b672c8aab
  google_mlkit_object_detection: beb1039b8e004515639f5df958701fc50aba55e4
  google_mlkit_text_recognition: ec2122ec89bfe0d7200763336a6e4ef44810674c
  GoogleAppMeasurement: fc0817122bd4d4189164f85374e06773b9561896
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_cropper: c4326ea50132b1e1564499e5d32a84f01fb03537
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  local_auth_darwin: 553ce4f9b16d3fdfeafce9cf042e7c9f77c1c391
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitImageLabelingCommon: 23f0c9037c2c6433784db594bd1bb87173172fe9
  MLKitObjectDetection: a5f066aa6e90f134fc1ac47940e95381b7eb5257
  MLKitObjectDetectionCommon: 0198709a728984e3b6fac98a5fa53a8042880336
  MLKitObjectDetectionCustom: b243512ccac75c98399c272e213b53413a255c07
  MLKitTextRecognition: 3b41f3ff084a79afb214408d25d2068d77ab322c
  MLKitTextRecognitionCommon: cd44577a8c506fc6bba065096de03bec0d01a213
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  MLKitVisionKit: 8a7abd5f11aeb1add2942a694c2685eca422a849
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  no_screenshot: 6d183496405a3ab709a67a54e5cd0f639e94729e
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  restart_app: 9cda5378aacc5000e3f66ee76a9201534e7d3ecf
  ScreenProtectorKit: 83a6281b02c7a5902ee6eac4f5045f674e902ae4
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: 9ff7ee26971fd7b8ae50806c18e33d288821ea37

COCOAPODS: 1.16.2
