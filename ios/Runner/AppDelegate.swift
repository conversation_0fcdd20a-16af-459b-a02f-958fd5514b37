import Flutter
import UIKit
import FirebaseCore
import UserNotifications

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Initialize Firebase with offline persistence
    if FirebaseApp.app() == nil {
        FirebaseApp.configure()
    }

    // Configure notification categories for iOS
    configureNotificationCategories()

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle Universal Links when app is already running
  override func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {

    // Check if this is a Universal Link
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb,
       let url = userActivity.webpageURL {
      print("📱 Universal Link received in AppDelegate: \(url.absoluteString)")

      // Let the app_links plugin handle the URL
      // The plugin will automatically process this through DeepLinkParser
      return super.application(application, continue: userActivity, restorationHandler: restorationHandler)
    }

    return super.application(application, continue: userActivity, restorationHandler: restorationHandler)
  }

  private func configureNotificationCategories() {
    let openAction = UNNotificationAction(
      identifier: "OPEN_ACTION",
      title: "Open",
      options: [.foreground]
    )

    let dismissAction = UNNotificationAction(
      identifier: "DISMISS_ACTION",
      title: "Dismiss",
      options: []
    )

    let generalCategory = UNNotificationCategory(
      identifier: "GENERAL_CATEGORY",
      actions: [openAction, dismissAction],
      intentIdentifiers: [],
      options: []
    )

    UNUserNotificationCenter.current().setNotificationCategories([generalCategory])
  }
}
