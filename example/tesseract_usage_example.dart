import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/services/tesseract_web_ocr_service.dart';
import 'package:pdl_superapp/models/ktp_ocr_model.dart';

/// Example widget demonstrating Tesseract OCR usage for Indonesian KTP
class TesseractOcrExample extends StatefulWidget {
  const TesseractOcrExample({super.key});

  @override
  State<TesseractOcrExample> createState() => _TesseractOcrExampleState();
}

class _TesseractOcrExampleState extends State<TesseractOcrExample> {
  final ImagePicker _picker = ImagePicker();
  KtpOcrModel? _extractedData;
  bool _isProcessing = false;
  String? _errorMessage;

  /// Pick image and perform OCR
  Future<void> _pickImageAndExtractKtp() async {
    try {
      setState(() {
        _isProcessing = true;
        _errorMessage = null;
        _extractedData = null;
      });

      // Pick image from gallery or camera
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image == null) {
        setState(() {
          _isProcessing = false;
        });
        return;
      }

      if (kDebugMode) {
        print('📸 Image selected: ${image.name}');
        print('📏 Image size: ${await image.length()} bytes');
      }

      // Extract KTP data using Tesseract
      final ktpData = await TesseractWebOcrService.extractKtpWithTesseract(
        image,
      );

      setState(() {
        _extractedData = ktpData;
        _isProcessing = false;
      });

      if (kDebugMode) {
        print('✅ KTP extraction completed');
        _logExtractedData(ktpData);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error extracting KTP data: $e';
        _isProcessing = false;
      });

      if (kDebugMode) {
        print('❌ Error in KTP extraction: $e');
      }
    }
  }

  /// Log extracted data for debugging
  void _logExtractedData(KtpOcrModel data) {
    // print('=== Extracted KTP Data ===');
    // print('NIK: ${data.nik ?? "Not found"}');
    // print('Name: ${data.name ?? "Not found"}');
    // print('Place of Birth: ${data.placeBirth ?? "Not found"}');
    // print('Birth Date: ${data.birthDay ?? "Not found"}');
    // print('Gender: ${data.gender ?? "Not found"}');
    // print('Address: ${data.address ?? "Not found"}');
    // print('RT/RW: ${data.rt ?? "Not found"}/${data.rw ?? "Not found"}');
    // print('Sub District: ${data.subDistrict ?? "Not found"}');
    // print('District: ${data.district ?? "Not found"}');
    // print('Province: ${data.province ?? "Not found"}');
    // print('City: ${data.city ?? "Not found"}');
    // print('Religion: ${data.religion ?? "Not found"}');
    // print('Marital Status: ${data.marital ?? "Not found"}');
    // print('Occupation: ${data.occupation ?? "Not found"}');
    // print('Nationality: ${data.nationality ?? "Not found"}');
    // print('Valid Until: ${data.validUntil ?? "Not found"}');
    // print('========================');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tesseract KTP OCR Example'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Instructions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Indonesian KTP OCR with Tesseract.js',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      kIsWeb
                          ? '🌐 Running on Web - Using Tesseract.js with Indonesian trained data'
                          : '📱 Running on Mobile - Using native OCR service',
                      style: TextStyle(
                        color: kIsWeb ? Colors.green : Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Select a clear image of Indonesian KTP for best results.',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Pick Image Button
            ElevatedButton.icon(
              onPressed: _isProcessing ? null : _pickImageAndExtractKtp,
              icon:
                  _isProcessing
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.camera_alt),
              label: Text(_isProcessing ? 'Processing...' : 'Select KTP Image'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),

            const SizedBox(height: 16),

            // Error Message
            if (_errorMessage != null)
              Card(
                color: Colors.red.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // Extracted Data
            if (_extractedData != null)
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Extracted KTP Data',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                _buildDataRow('NIK', _extractedData!.nik),
                                _buildDataRow('Name', _extractedData!.name),
                                _buildDataRow(
                                  'Place of Birth',
                                  _extractedData!.placeBirth,
                                ),
                                _buildDataRow(
                                  'Birth Date',
                                  _extractedData!.birthDay,
                                ),
                                _buildDataRow('Gender', _extractedData!.gender),
                                _buildDataRow(
                                  'Address',
                                  _extractedData!.address,
                                ),
                                _buildDataRow('RT', _extractedData!.rt),
                                _buildDataRow('RW', _extractedData!.rw),
                                _buildDataRow(
                                  'Sub District',
                                  _extractedData!.subDistrict,
                                ),
                                _buildDataRow(
                                  'District',
                                  _extractedData!.district,
                                ),
                                _buildDataRow(
                                  'Province',
                                  _extractedData!.province,
                                ),
                                _buildDataRow('City', _extractedData!.city),
                                _buildDataRow(
                                  'Religion',
                                  _extractedData!.religion,
                                ),
                                _buildDataRow(
                                  'Marital Status',
                                  _extractedData!.marital,
                                ),
                                _buildDataRow(
                                  'Occupation',
                                  _extractedData!.occupation,
                                ),
                                _buildDataRow(
                                  'Nationality',
                                  _extractedData!.nationality,
                                ),
                                _buildDataRow(
                                  'Valid Until',
                                  _extractedData!.validUntil,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'Not found',
              style: TextStyle(
                color: value != null ? Colors.black : Colors.grey,
                fontStyle: value != null ? FontStyle.normal : FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
