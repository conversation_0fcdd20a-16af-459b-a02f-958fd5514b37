# 🔔 Firebase Cloud Messaging (FCM) Setup Guide

Panduan lengkap untuk mengimplementasikan Firebase Cloud Messaging di aplikasi Flutter dengan GetX.

## 📋 Fitur yang Diimplementasikan

✅ **Multi-platform Support**: Android, iOS, dan Web  
✅ **State Management**: Menggunakan GetX  
✅ **Notification States**: Foreground, Background, dan Terminated  
✅ **Local Notifications**: Untuk foreground notifications  
✅ **Navigation**: Otomatis navigasi berdasarkan payload  
✅ **Topic Subscription**: Subscribe/unsubscribe ke topic tertentu  
✅ **Permission Handling**: Request permission otomatis  

## 🚀 Instalasi Dependencies

Dependencies sudah ditambahkan ke `pubspec.yaml`:

```yaml
dependencies:
  firebase_messaging: ^15.1.6
  firebase_messaging_web: ^3.9.6
  flutter_local_notifications: ^18.0.1
```

Jalankan:
```bash
flutter pub get
```

## ⚙️ Konfigurasi Platform

### 🤖 Android Setup

**1. Permissions** (sudah dikonfigurasi di `android/app/src/main/AndroidManifest.xml`):
```xml
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
<uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
```

**2. Services** (sudah dikonfigurasi):
```xml
<service android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService" android:exported="false">
  <intent-filter>
    <action android:name="com.google.firebase.MESSAGING_EVENT" />
  </intent-filter>
</service>
```

### 🍎 iOS Setup

**1. Permissions** (sudah dikonfigurasi di `ios/Runner/Info.plist`):
```xml
<key>NSUserNotificationsUsageDescription</key>
<string>This app needs notification access to send you important updates and alerts.</string>
```

**2. Background Modes** (sudah ada):
```xml
<key>UIBackgroundModes</key>
<array>
  <string>fetch</string>
  <string>remote-notification</string>
</array>
```

### 🌐 Web Setup

**1. Firebase SDK** (sudah ditambahkan ke `web/index.html`):
```html
<script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>
```

**2. Service Worker** (sudah dibuat `web/firebase-messaging-sw.js`):
- Menangani background notifications
- Custom notification display
- Navigation handling

## 🔧 Penggunaan

### Inisialisasi Service

Service sudah diinisialisasi otomatis di `main.dart`:

```dart
// Initialize Notification Service
Get.put(NotificationService(), permanent: true);
```

### Mengakses Service

```dart
// Dapatkan instance service
final notificationService = NotificationService.instance;

// Cek status permission
bool isGranted = notificationService.isPermissionGranted;

// Dapatkan FCM token
String token = notificationService.fcmToken;

// Subscribe ke topic
await notificationService.subscribeToTopic('general');

// Unsubscribe dari topic
await notificationService.unsubscribeFromTopic('general');
```

### Listen Notification Events

```dart
// Listen stream notifikasi
NotificationService.instance.notificationStream.listen((message) {
  print('Notification received: ${message.notification?.title}');
});
```

## 📱 Testing Notifikasi

### 1. Menggunakan Halaman Test

Akses halaman test yang sudah dibuat:
```dart
Get.to(() => NotificationTestPage());
```

Halaman ini menampilkan:
- Status permission
- FCM token (bisa di-copy)
- Subscribe/unsubscribe topic
- Clear notifications

### 2. Firebase Console

1. Buka [Firebase Console](https://console.firebase.google.com)
2. Pilih project Anda
3. Masuk ke **Cloud Messaging**
4. Klik **Send your first message**
5. Isi title dan body
6. Pilih target (token atau topic)
7. **Advanced options** → **Custom data** untuk payload

### 3. Postman/cURL

**Endpoint**: `https://fcm.googleapis.com/fcm/send`

**Headers**:
```
Authorization: key=YOUR_SERVER_KEY
Content-Type: application/json
```

**Body untuk Token**:
```json
{
  "to": "FCM_TOKEN_HERE",
  "notification": {
    "title": "Test Notification",
    "body": "This is a test message"
  },
  "data": {
    "screen": "home",
    "id": "123"
  }
}
```

**Body untuk Topic**:
```json
{
  "to": "/topics/general",
  "notification": {
    "title": "Topic Notification",
    "body": "Message for all subscribers"
  },
  "data": {
    "route": "/inbox",
    "arguments": {
      "id": "456"
    }
  }
}
```

## 🧭 Navigation Payload

Service mendukung 2 format payload untuk navigasi:

### Format 1: Screen-based
```json
{
  "screen": "home",     // home, inbox, profile, detail
  "id": "123"          // optional parameter
}
```

### Format 2: Route-based
```json
{
  "route": "/inbox",
  "arguments": {
    "id": "456",
    "type": "message"
  }
}
```

## 🔍 Debugging

### Log Messages

Service menggunakan `LoggerService` untuk logging:
- Permission status
- Token generation/refresh
- Message received events
- Navigation attempts
- Error handling

### Common Issues

**1. Token tidak muncul**:
- Pastikan Firebase sudah diinisialisasi
- Cek permission granted
- Restart aplikasi

**2. Notifikasi tidak muncul di foreground**:
- Local notifications hanya untuk mobile
- Web menggunakan browser notifications

**3. Navigation tidak bekerja**:
- Pastikan payload format benar
- Cek route sudah terdaftar di GetX
- Lihat log untuk error messages

**4. Web notifications tidak bekerja**:
- Pastikan service worker terdaftar
- Cek browser support
- Test di HTTPS (required untuk web push)

## 📊 Monitoring

### FCM Analytics

Firebase Console menyediakan analytics untuk:
- Delivery rates
- Open rates
- Conversion tracking
- A/B testing

### Custom Analytics

Tambahkan tracking di service:
```dart
void _handleForegroundMessage(RemoteMessage message) {
  // Custom analytics tracking
  analytics.logEvent('notification_received', {
    'title': message.notification?.title,
    'source': 'foreground'
  });
}
```

## 🔒 Security Best Practices

1. **Server Key**: Jangan expose di client-side code
2. **Token Management**: Refresh token secara berkala
3. **Payload Validation**: Validasi data sebelum navigasi
4. **Permission**: Request permission dengan context yang jelas
5. **Topic Security**: Gunakan topic rules di Firebase Console

## 🚀 Production Checklist

- [ ] Test di semua platform (Android, iOS, Web)
- [ ] Test semua states (foreground, background, terminated)
- [ ] Verify navigation dengan berbagai payload
- [ ] Test permission handling
- [ ] Setup Firebase Console rules
- [ ] Configure server key security
- [ ] Test topic subscriptions
- [ ] Verify analytics tracking
- [ ] Test dengan real server key (bukan development)
- [ ] Performance testing dengan banyak notifications

## 📞 Support

Jika ada masalah dengan implementasi FCM:

1. Cek log di `LoggerService`
2. Test dengan Firebase Console terlebih dahulu
3. Verify konfigurasi platform-specific
4. Pastikan Firebase project setup benar
5. Test dengan simple payload dulu sebelum complex navigation

---

**Happy Coding! 🎉**
