import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/item_action_menu.dart';
import 'package:pdl_superapp/components/login/login_header.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/flavors.dart';
import 'package:pdl_superapp/utils/analytics_utils.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

// ignore: must_be_immutable
class BaseDetailPage extends StatefulWidget {
  final Widget child;
  final String title;
  final Function()? bottomAction;
  final String? bottomText;
  final BaseControllers controller;
  bool? backEnabled;
  final Function() onRefresh;
  bool? isActionActive;
  final List<Widget>? actions;
  final Widget? bottomWidget;
  final Function()? onBack;
  final ScrollController? scrollController;
  final double? width;
  final ScrollPhysics? scrollPhysics;
  RxBool? isBottomShow = false.obs;
  final RxBool? isBottomActionEnabled;

  BaseDetailPage({
    super.key,
    required this.child,
    required this.title,
    required this.controller,
    required this.onRefresh,
    this.backEnabled,
    this.bottomAction,
    this.bottomText,
    this.isActionActive,
    this.bottomWidget,
    this.onBack,
    this.actions,
    this.scrollController,
    this.width,
    this.scrollPhysics,
    this.isBottomShow,
    this.isBottomActionEnabled,
  });

  @override
  State<BaseDetailPage> createState() => _BaseDetailPageState();
}

class _BaseDetailPageState extends State<BaseDetailPage> {
  late ScrollController defScrollController;
  bool isShow = true;

  // final networkController = Get.put(NetworkController());
  late NetworkController networkController;

  void getNetworkController() async {
    if (!kIsWeb) {
      try {
        networkController = Get.find<NetworkController>();
      } catch (e) {
        // Fallback jika belum diinisialisasi
        networkController = Get.put(NetworkController());
      }
    }
  }

  @override
  void initState() {
    super.initState();
    getNetworkController();
    if (widget.scrollController != null) {
      defScrollController = widget.scrollController!;
    } else {
      defScrollController = ScrollController();
    }
    defScrollController.addListener(() {
      setState(() {
        isShow = defScrollController.offset < 75;
      });
    });

    // Set web title for web platform
    _setWebTitle();

    // Track page view for analytics
    _trackPageView();
  }

  /// Set web page title based on page title or fallback to app name
  void _setWebTitle() {
    if (kIsWeb) {
      String webTitle = widget.title.isNotEmpty ? widget.title : F.title;
      // Set the application switcher description which affects the browser tab title
      SystemChrome.setApplicationSwitcherDescription(
        ApplicationSwitcherDescription(
          label: webTitle,
          primaryColor: 0xFF000000,
        ),
      );
    }
  }

  /// Track page view for analytics
  void _trackPageView() {
    // Use Future.microtask to avoid blocking the UI
    Future.microtask(() async {
      try {
        await AnalyticsUtils.trackPageView(
          pageClass: widget.runtimeType.toString(),
          parameters: {
            'page_title': widget.title.isNotEmpty ? widget.title : 'untitled',
            'has_back_button': widget.backEnabled == true ? 'true' : 'false',
            'has_bottom_action': widget.bottomAction != null ? 'true' : 'false',
            'has_bottom_widget': widget.bottomWidget != null ? 'true' : 'false',
            'page_type': 'detail_page',
          },
        );
      } catch (e) {
        // Silently handle analytics errors to not affect user experience
      }
    });
  }

  /// Smart back navigation that handles both history-based and manual navigation
  void _handleBackNavigation() {
    if (widget.onBack != null) {
      widget.onBack!();
      return;
    }

    // Try to go back using history first
    if (Navigator.canPop(context)) {
      Get.back();
    } else {
      // If no history, manually navigate to parent route
      String currentRoute = Get.currentRoute;
      String parentRoute = _getParentRoute(currentRoute);
      if (parentRoute.isNotEmpty) {
        Get.offNamed(parentRoute);
      } else {
        // Fallback to home if no parent route can be determined
        Get.offNamed('/main');
      }
    }
  }

  /// Extract parent route by removing the last path segment
  String _getParentRoute(String currentRoute) {
    if (currentRoute.isEmpty || currentRoute == '/') return '';

    // Remove query parameters if any
    String route = currentRoute.split('?').first;

    // Split by '/' and remove empty strings
    List<String> segments =
        route.split('/').where((s) => s.isNotEmpty).toList();

    if (segments.isEmpty) return '';

    // Remove the last segment (which could be an ID or detail page)
    segments.removeLast();

    if (segments.isEmpty) return '/main';

    return '/${segments.join('/')}';
  }

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      bottomNavigationBar:
          widget.bottomAction != null || widget.bottomWidget != null
              ? Container(
                width: Get.width,
                height: 70,

                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(149, 157, 165, 0.2),
                      blurRadius: 24,
                      spreadRadius: 0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child:
                      widget.bottomWidget ??
                      (widget.isBottomActionEnabled != null
                          ? Obx(
                            () => PdlButton(
                              controller: widget.controller,
                              onPressed:
                                  widget.isBottomActionEnabled!.value
                                      ? widget.bottomAction
                                      : null,
                              title: widget.bottomText ?? 'button_save'.tr,
                            ),
                          )
                          : PdlButton(
                            controller: widget.controller,
                            onPressed: widget.bottomAction,
                            title: widget.bottomText ?? 'button_save'.tr,
                          )),
                ),
              )
              : null,
      body: isWideScreen ? _layoutWeb(context) : _layoutMobile(context),
    );
  }

  Widget _layoutMobile(context) {
    return SafeArea(
      bottom: true,
      top: false,
      child: Stack(
        children: [
          LoginHeader(
            child: Container(
              width: Get.width,
              height: 200,
              padding: EdgeInsets.symmetric(horizontal: paddingMedium),
              margin: EdgeInsets.only(top: 50),
              alignment: Alignment.topCenter,
              child: Row(
                children: [
                  GestureDetector(
                    onTap: _handleBackNavigation,
                    child: Icon(Icons.arrow_back, color: Colors.white),
                  ),
                  if (widget.isActionActive == true)
                    SizedBox(width: paddingSmall),
                  Expanded(
                    child: Text(
                      widget.title,
                      textAlign:
                          widget.isActionActive == true
                              ? null
                              : TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (widget.isActionActive == true)
                    Row(
                      children: [
                        if (widget.actions != null &&
                            widget.actions!.isNotEmpty)
                          for (final widget in widget.actions!) widget,

                        //NOTE: just for example
                        if (widget.actions == null ||
                            widget.actions!.isEmpty) ...[
                          ItemActionMenu(),
                          SizedBox(width: paddingSmall),
                          ItemActionMenu(),
                        ],
                      ],
                    ),
                  if (widget.isActionActive != true)
                    Icon(Icons.arrow_back, color: Colors.transparent),
                ],
              ),
            ),
          ),
          SizedBox(
            width: Get.width,
            height: Get.height,
            child: Column(
              children: [
                SizedBox(height: 100),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: () async => widget.onRefresh(),
                    child: Container(
                      height: Get.height,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius:
                            isShow
                                ? BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                )
                                : null,
                      ),
                      child: SingleChildScrollView(
                        controller:
                            widget.scrollController ?? defScrollController,
                        physics:
                            widget.scrollPhysics ??
                            AlwaysScrollableScrollPhysics(),
                        child: getChild(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(width: Get.width, height: Get.height),
        ],
      ),
    );
  }

  Widget _layoutWeb(context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Utils.cachedImageWrapper(
            'image/img-bg-web.png',
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: paddingLarge,
                  horizontal: paddingLarge,
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap:
                          widget.backEnabled == true
                              ? _handleBackNavigation
                              : null,
                      child: Icon(
                        Icons.arrow_back,
                        color:
                            widget.backEnabled == true
                                ? Colors.white
                                : Colors.transparent,
                      ),
                    ),
                    Expanded(
                      child: SizedBox(
                        width: Get.width / 1.5,
                        child: Text(
                          widget.title,
                          textAlign: TextAlign.center,
                          style: Theme.of(
                            context,
                          ).textTheme.bodyLarge?.copyWith(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                    Icon(Icons.arrow_back, color: Colors.transparent),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  color: Theme.of(context).colorScheme.surface,
                  width: Get.width,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(
                          width: widget.width ?? Get.width / 1.05,
                          child: getChild(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  getChild() {
    return widget.child;
    // return Obx(() {
    //   if (controller?.isConnected.value ?? true) {
    //     return widget.child;
    //   } else {
    //     return SizedBox(
    //       width: Get.width,
    //       child: Column(
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         mainAxisSize: MainAxisSize.min,
    //         children: [
    //           SizedBox(
    //             height: Get.height * .3,
    //             width: Get.width,
    //             child: SvgPicture.asset(
    //               'assets/icon/illustration-empty-no-connection.svg',
    //               fit: BoxFit.contain,
    //             ),
    //           ),
    //           Text(
    //             'title_internet_disconnected_str'.tr,
    //             style: Theme.of(context).textTheme.titleMedium?.copyWith(
    //               fontWeight: FontWeight.w700,
    //               fontSize: 20,
    //             ),
    //             textAlign: TextAlign.center,
    //           ),
    //           SizedBox(height: paddingMedium),
    //           Text(
    //             'subtitle_internet_disconnected_str'.tr,
    //             style: Theme.of(context).textTheme.bodyMedium,
    //             textAlign: TextAlign.center,
    //           ),
    //           SizedBox(height: paddingMedium),
    //           SizedBox(
    //             width: Get.width * .9,
    //             child: Container(
    //               padding: EdgeInsets.only(top: paddingLarge),
    //               width: Get.width,
    //               child: FilledButton(
    //                 onPressed: () {
    //                   kIsWeb ? null : controller?.retry();
    //                 },
    //                 child: Text('retry_str'.tr),
    //               ),
    //             ),
    //           ),
    //         ],
    //       ),
    //     );
    //   }
    // });
  }
}
