import 'dart:convert';
import 'dart:developer';
// import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/network_manager.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

class BaseApi extends GetConnect {
  // late SharedPreferences prefs;
  @override
  void onInit() async {
    httpClient.baseUrl = baseUrl;
    // Reduced timeout for faster offline fallback
    httpClient.timeout = const Duration(seconds: 60);
    // prefs = await SharedPreferences.getInstance();

    setupRequestModifier();
  }

  setupRequestModifier({bool? isFile}) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Additional headers

    httpClient.addRequestModifier((Request request) {
      String token = prefs.getString(kStorageToken) ?? '';
      String deviceId = prefs.getString(kDeviceId) ?? '';

      // Add Authorization header if token exists
      if (token.isNotEmpty) {
        request.headers['Authorization'] = 'Bearer $token';
      }

      // For web platform, be more conservative with headers
      if (kIsWeb) {
        // Only set essential headers for web
        if (deviceId.isNotEmpty) {
          request.headers['device_id'] = deviceId;
        }
        // Let the browser handle Content-Type and Accept automatically
      } else {
        // For mobile platforms, set all headers as before
        request.headers['Content-Type'] = 'application/json';
        request.headers['Accept'] = 'application/json';
        request.headers['device_id'] = deviceId;
        request.headers['Access-Control-Allow-Origin'] = '*';
        request.headers['Access-Control-Allow-Methods'] =
            'GET, POST, PUT, DELETE, OPTIONS';
        request.headers['Access-Control-Allow-Headers'] =
            'Origin, Content-Type, X-Auth-Token, Authorization';
      }

      _print(doPrint: false, data: token);
      _print(doPrint: false, data: request.headers);
      _print(doPrint: false, data: request.files);
      return request;
    });
  }

  /// Check internet connection
  Future<bool> _checkInternetConnection() async {
    // If platform is web, always return true (assume connected)
    if (kIsWeb) {
      return true;
    }

    try {
      final networkManager = Get.find<NetworkManager>();
      final networkController = Get.find<NetworkController>();
      if (!networkController.isConnected.value) return false;
      // Jika sudah offline, langsung return false
      // if (!networkManager.isOnline) return false;

      // Cek kualitas koneksi hanya jika online
      // await networkController.connectionChecker();
      return await networkManager.checkConnectionQuality();
    } catch (e) {
      try {
        Get.find<LoggerService>().log('here Error finding NetworkManager: $e');
      } catch (_) {
        log('here Error finding NetworkManager: $e'); // Fallback to direct log
      }
      // Fallback ke pengecekan basic jika NetworkManager tidak tersedia
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    }
  }

  /// BASE FUNCTIONS
  /// Fetch data from API with offline cache support
  Future<void> apiFetch({
    required String url,
    required BaseControllers controller,
    int code = 0,
    bool debug = false,
  }) async {
    // Add retry mechanism for iOS after Shorebird updates
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 1000);

    while (retryCount < maxRetries) {
      try {
        setupRequestModifier();

        // Ensure ConfigReader is initialized before making API calls
        try {
          if (ConfigReader.getBaseUrl().isEmpty) {
            await ConfigReader.initialize();
          }
        } catch (e) {
          Get.find<LoggerService>().log(
            'ConfigReader initialization error: $e',
          );
          if (retryCount < maxRetries - 1) {
            retryCount++;
            await Future.delayed(retryDelay);
            continue;
          }
          rethrow;
        }

        // If we reach here, break the retry loop
        break;
      } catch (e) {
        retryCount++;
        Get.find<LoggerService>().log(
          'API fetch attempt $retryCount failed: $e',
        );

        if (retryCount >= maxRetries) {
          // Final attempt failed, proceed with original logic
          Get.find<LoggerService>().log(
            'All retry attempts failed, proceeding with original logic',
          );
          break;
        }

        await Future.delayed(retryDelay);
      }
    }

    try {
      // Check internet connection first
      final hasConnection = await _checkInternetConnection();

      // Inisialisasi FirestoreServices terlebih dahulu, baik online maupun offline
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
        Get.find<LoggerService>().log("FirestoreServices ditemukan");
      } catch (e) {
        Get.find<LoggerService>().log(
          "FirestoreServices tidak ditemukan, membuat instance baru",
        );
        firestoreServices = Get.put(FirestoreServices());
      }

      // Progressive loading: Try cache first for faster response
      Get.find<LoggerService>().log(
        "Mencoba mengambil data dari cache untuk progressive loading: $url",
      );

      if (!hasConnection) {
        // No internet connection - use cache only
        Get.find<LoggerService>().log("here Tidak ada koneksi internet");

        final cachedData = await firestoreServices.getApiCallData(url: url);

        if (cachedData != null) {
          // Use cached data if available
          Get.find<LoggerService>().log(
            "here Berhasil menggunakan data dari cache",
          );
          Get.find<LoggerService>().log(
            "here Tipe data cache: ${cachedData.runtimeType}",
          );

          return controller.loadSuccess(
            requestCode: code,
            response: cachedData,
            statusCode: 200, // Assume success status code
          );
        } else {
          // No cached data available
          Get.find<LoggerService>().log(
            "here Tidak ada data cache yang tersedia untuk URL: $url",
          );
          return controller.loadError(kNoCachedDataMsg);
        }
      }

      _print(doPrint: debug, data: url);

      // Use different approach for web platform to avoid header issues
      if (kIsWeb) {
        final webResponse = await _webApiFetch(url: url, debug: debug);
        if (webResponse['success'] == false) {
          if (webResponse['statusCode'] == 401) {
            Utils.setLoggedOut();
          }
          // Create a mock response object for compatibility
          final mockResponse = Response<dynamic>(
            body: webResponse['body'],
            statusCode: webResponse['statusCode'] ?? 500,
            statusText: _getStatusText(webResponse['statusCode'] ?? 500),
          );
          return controller.loadFailed(
            requestCode: code,
            response: mockResponse,
          );
        }

        // Store successful API response in Firestore for offline access
        // Get.find<LoggerService>().log(
        //   "Menyimpan respons API ke Firestore untuk akses offline: $url",
        // );
        await _storeApiCallData(
          url: url,
          responseData: webResponse['body'] ?? {},
        );

        return controller.loadSuccess(
          requestCode: code,
          response: webResponse['body'] ?? {},
          statusCode: webResponse['statusCode'] ?? 200,
        );
      }
      final response = await get(url);

      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        return controller.loadFailed(requestCode: code, response: response);
      }

      // Store successful API response in Firestore for offline access
      await _storeApiCallData(url: url, responseData: response.body ?? {});
      Get.find<LoggerService>().log(
        "Data berhasil disimpan ke Firestore untuk URL: $url",
      );

      // Always show fresh data when API call succeeds
      final freshData = response.body ?? {};

      // Always update UI with fresh data from API
      return controller.loadSuccess(
        requestCode: code,
        response: freshData,
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());

      // Jika terjadi error (misalnya timeout), coba ambil dari cache
      try {
        Get.find<LoggerService>().log(
          "Terjadi error saat fetch API, mencoba mengambil dari cache: $e",
        );

        FirestoreServices firestoreServices;
        try {
          firestoreServices = Get.find<FirestoreServices>();
        } catch (_) {
          firestoreServices = Get.put(FirestoreServices());
        }

        final cachedData = await firestoreServices.getApiCallData(url: url);

        if (cachedData != null) {
          Get.find<LoggerService>().log(
            "Berhasil menggunakan data dari cache setelah error",
          );
          return controller.loadSuccess(
            requestCode: code,
            response: cachedData,
            statusCode: 200,
          );
        }
      } catch (cacheError) {
        Get.find<LoggerService>().log(
          "Gagal mengambil data dari cache setelah error: $cacheError",
        );
      }

      return controller.loadError(e);
    }
  }

  Future<void> apiPatch({
    required String url,
    required BaseControllers controller,
    required Map data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      _print(doPrint: debug, data: url);
      _print(doPrint: debug, data: data);

      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      String deviceId = prefs.getString(kDeviceId) ?? '';

      // Construct full URL if it's a relative URL
      String fullUrl = url;
      if (!url.startsWith('http')) {
        String baseUrl = ConfigReader.getBaseUrl();
        fullUrl = '$baseUrl$url';
      }

      // Prepare headers
      Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/html',
        'device_id': deviceId,
      };

      if (token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }

      // Don't set CORS headers for web platform
      // Web browsers handle CORS automatically and setting these headers manually can cause issues
      // Make the PATCH request using http library
      final response = await http
          .patch(Uri.parse(fullUrl), headers: headers, body: json.encode(data))
          .timeout(const Duration(seconds: 25))
          .onError((error, stackTrace) {
            return http.Response(
              jsonEncode({
                'error_description': 'subtitle_internet_disconnected_str'.tr,
              }),
              408,
            );
          });

      _print(doPrint: debug, data: 'Status code : ${response.statusCode}');
      _print(doPrint: debug, data: 'Response headers: ${response.headers}');
      _print(doPrint: debug, data: 'Response body: ${response.body}');

      // Parse response based on content type
      dynamic responseBody;
      String? contentType = response.headers['content-type'];

      if (contentType != null && contentType.contains('text/html')) {
        // Handle HTML response
        responseBody = {
          'html_content': response.body,
          'content_type': 'text/html',
        };
        _print(doPrint: debug, data: 'Received HTML response');
      } else {
        // Try to parse as JSON, fallback to raw text if it fails
        try {
          responseBody = json.decode(response.body);
        } catch (e) {
          responseBody = {
            'raw_content': response.body,
            'content_type': contentType ?? 'unknown',
          };
          _print(
            doPrint: debug,
            data: 'Failed to parse JSON, using raw content: $e',
          );
        }
      }

      if (response.statusCode < 200 || response.statusCode >= 300) {
        _print(doPrint: debug, data: 'Error : $url');
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }

        // Create a mock response object for compatibility
        final mockResponse = Response<dynamic>(
          body: responseBody,
          statusCode: response.statusCode,
          statusText: _getStatusText(response.statusCode),
        );

        return controller.loadFailed(requestCode: code, response: mockResponse);
      }

      // We don't store PATCH responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: responseBody ?? {},
        statusCode: response.statusCode,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError(e);
    }
  }

  Future<void> apiPut({
    required String url,
    required BaseControllers controller,
    required Map data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      // final hasConnection = await _checkInternetConnection();

      // if (!hasConnection) {
      //   // No internet connection
      //   Get.find<LoggerService>().log("tidak ada koneksi");
      //   return controller.loadError(
      //     "No internet connection. Cannot perform update operation offline.",
      //   );
      // }

      _print(doPrint: debug, data: url);
      _print(doPrint: debug, data: data);
      final response = await put(url, data);
      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? response.body ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store PATCH responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError(e);
    }
  }

  Future<void> apiPost({
    required String url,
    required BaseControllers controller,
    required var data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      _print(doPrint: debug, data: url);
      _print(doPrint: debug, data: data);

      // Use different approach for web platform to avoid header issues
      if (kIsWeb) {
        return await _webApiPost(
          url: url,
          controller: controller,
          data: data,
          code: code,
          debug: debug,
        );
      }

      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        Get.find<LoggerService>().log("here Tidak ada koneksi internet");
        return controller.loadError(
          "No internet connection. Cannot perform operation offline.",
        );
      }

      // Use GetConnect for mobile platforms
      final response = await post(
        url,
        data,
      ).timeout(const Duration(seconds: 8));

      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        controller.setLoading(false);
        if (response.statusCode == 401) {
          if (code != kReqLogin) {
            Utils.setLoggedOut();
          } else {
            return controller.loadFailed(requestCode: code, response: response);
          }
        }

        String errMessage = '';
        try {
          if (response.body['error_description'] != null) {
            errMessage = response.body['error_description'].toString();
          } else if (response.body['message'] != null) {
            errMessage = response.body['message'].toString();
          } else {
            errMessage = response.body.toString();
          }
        } catch (e) {
          errMessage = '${response.statusCode} - ${response.statusText}';
        }
        if (errMessage.contains('Failed host lookup')) {
          errMessage =
              '${'title_disconected_str'.tr}. ${'subtitle_internet_disconnected_str'.tr}';
        }
        Utils.popup(body: errMessage, type: kPopupFailed);

        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store POST responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError('here $e');
    }
  }

  Future<void> apiDelete({
    required String url,
    required BaseControllers controller,
    required Map<String, dynamic> data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // Check internet connection first
      // final hasConnection = await _checkInternetConnection();

      // if (!hasConnection) {
      //   // No internet connection
      //   Get.find<LoggerService>().log("tidak ada koneksi");
      //   return controller.loadError(
      //     "No internet connection. Cannot perform delete operation offline.",
      //   );
      // }

      _print(doPrint: debug, data: url);
      final response = await delete(url, query: data);

      _print(doPrint: debug, data: response.body ?? {});

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Error : $url');
        if (response.statusCode == 401) {
          Utils.setLoggedOut();
        }
        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? response.body ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        return controller.loadFailed(requestCode: code, response: response);
      }

      // We don't store DELETE responses in Firestore

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log(e.toString());
      return controller.loadError(e);
    }
  }

  void _print({bool doPrint = false, dynamic data}) {
    if (doPrint) {
      try {
        try {
          Get.find<LoggerService>().log(json.encode(data));
        } catch (_) {
          log(json.encode(data)); // Fallback to direct log
        }
      } catch (e) {
        try {
          Get.find<LoggerService>().log(data.toString());
        } catch (_) {
          log(data.toString()); // Fallback to direct log
        }
      }
    }
  }

  /// Store API call data in Firestore
  Future<void> _storeApiCallData({
    required String url,
    required dynamic responseData,
  }) async {
    try {
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
      } catch (e) {
        Get.find<LoggerService>().log(
          'FirestoreServices tidak ditemukan, membuat instance baru',
        );
        firestoreServices = Get.put(FirestoreServices());
      }

      // Verifikasi bahwa responseData tidak null
      if (responseData == null) {
        Get.find<LoggerService>().log(
          'Warning: Attempting to store null responseData for URL: $url',
        );
        return;
      }

      // Log tipe data yang akan disimpan
      Get.find<LoggerService>().log(
        'Storing data of type ${responseData.runtimeType} for URL: $url',
      );

      // Simpan data ke Firestore
      await firestoreServices.storeApiCallData(
        url: url,
        responseData: responseData,
      );

      // Verifikasi bahwa data telah disimpan dengan mencoba mengambilnya kembali
      try {
        final verifyData = await firestoreServices.getApiCallData(url: url);
        if (verifyData != null) {
          Get.find<LoggerService>().log(
            'Verified data was stored successfully for URL: $url',
          );
        } else {
          Get.find<LoggerService>().log(
            'Warning: Could not verify data was stored for URL: $url',
          );
        }
      } catch (verifyError) {
        Get.find<LoggerService>().log(
          'Error verifying stored data: $verifyError',
        );
      }
    } catch (e) {
      Get.find<LoggerService>().log('Error storing API call data: $e');
    }
  }

  /// Web-specific API GET method using http package to avoid header issues
  Future<Map<String, dynamic>> _webApiFetch({
    required String url,
    bool debug = false,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      String deviceId = prefs.getString(kDeviceId) ?? '';

      if (url.contains('/public')) {
        token = '';
      }

      // Construct full URL if it's a relative URL
      String fullUrl = url;
      if (!url.startsWith('http')) {
        String baseUrl = ConfigReader.getBaseUrl();
        fullUrl = '$baseUrl$url';
      }

      // Prepare headers - minimal set for web
      Map<String, String> headers = {'Accept': 'application/json'};

      if (token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }

      if (deviceId.isNotEmpty) {
        headers['device_id'] = deviceId;
      }

      // Make the GET request using http library
      final response = await http
          .get(Uri.parse(fullUrl), headers: headers)
          .timeout(const Duration(seconds: 60));

      _print(
        doPrint: debug,
        data: 'Web GET Status code: ${response.statusCode}',
      );
      _print(doPrint: false, data: 'Web GET Response body: ${response.body}');

      // Parse response
      dynamic responseBody;
      try {
        responseBody = json.decode(response.body);
      } catch (e) {
        responseBody = {'raw_content': response.body};
        _print(doPrint: debug, data: 'Failed to parse JSON: $e');
      }

      if (response.statusCode < 200 || response.statusCode >= 300) {
        return {
          'success': false,
          'statusCode': response.statusCode,
          'body': responseBody,
        };
      }

      return {
        'success': true,
        'statusCode': response.statusCode,
        'body': responseBody,
      };
    } catch (e) {
      Get.find<LoggerService>().log('Web API GET Error: $e');
      return {
        'success': false,
        'statusCode': 500,
        'body': {'error': 'Web API Error: $e'},
      };
    }
  }

  /// Web-specific API POST method using http package to avoid header issues
  Future<void> _webApiPost({
    required String url,
    required BaseControllers controller,
    required var data,
    int code = 1,
    bool debug = false,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      String deviceId = prefs.getString(kDeviceId) ?? '';

      if (url.contains('/public')) {
        token = '';
      }

      // Construct full URL if it's a relative URL
      String fullUrl = url;
      if (!url.startsWith('http')) {
        String baseUrl = ConfigReader.getBaseUrl();
        fullUrl = '$baseUrl$url';
      }

      // Prepare headers - minimal set for web
      Map<String, String> headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (token.isNotEmpty) {
        headers['Authorization'] = 'Bearer $token';
      }

      if (deviceId.isNotEmpty) {
        headers['device_id'] = deviceId;
      }

      // Make the POST request using http library
      final response = await http
          .post(Uri.parse(fullUrl), headers: headers, body: json.encode(data))
          .timeout(const Duration(seconds: 8));

      _print(
        doPrint: debug,
        data: 'Web POST Status code: ${response.statusCode}',
      );
      _print(doPrint: debug, data: 'Web POST Response body: ${response.body}');

      // Parse response
      dynamic responseBody;
      try {
        responseBody = json.decode(response.body);
      } catch (e) {
        responseBody = {'raw_content': response.body};
        _print(doPrint: debug, data: 'Failed to parse JSON: $e');
      }

      if (response.statusCode < 200 || response.statusCode >= 300) {
        _print(doPrint: debug, data: 'Web POST Error: $url');

        if (response.statusCode == 401) {
          if (code != kReqLogin) {
            Utils.setLoggedOut();
          } else {
            // Create a mock response object for compatibility
            final mockResponse = Response<dynamic>(
              body: responseBody,
              statusCode: response.statusCode,
              statusText: _getStatusText(response.statusCode),
            );
            return controller.loadFailed(
              requestCode: code,
              response: mockResponse,
            );
          }
        }

        String errorMessage = '-';

        if (responseBody is Map) {
          if (responseBody['validation'] != null &&
              responseBody['validation'] is Map) {
            final validation = responseBody['validation'] as Map;
            final List<String> validationMessages = [];

            // Extract validation messages
            validation.forEach((field, error) {
              if (error is Map && error['message'] != null) {
                validationMessages.add(error['message'].toString());
              }
            });

            if (validationMessages.isNotEmpty) {
              errorMessage = validationMessages.join('\n');
            }
          } else if (responseBody['message'] != null) {
            errorMessage = responseBody['message'].toString();
          } else if (responseBody['error_description'] != null) {
            errorMessage = responseBody['error_description'].toString();
          }
        } else {
          errorMessage = responseBody.toString();
        }

        Get.snackbar(
          'Failed',
          errorMessage,
          margin: EdgeInsets.only(bottom: paddingMedium),
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );

        // Create a mock response object for compatibility
        final mockResponse = Response<dynamic>(
          body: responseBody,
          statusCode: response.statusCode,
          statusText: _getStatusText(response.statusCode),
        );

        return controller.loadFailed(requestCode: code, response: mockResponse);
      }

      return controller.loadSuccess(
        requestCode: code,
        response: responseBody ?? {},
        statusCode: response.statusCode,
      );
    } catch (e) {
      Get.find<LoggerService>().log('Web API POST Error: $e');
      return controller.loadError('Web API Error: $e');
    }
  }

  /// Helper method to get status text from status code
  String _getStatusText(int statusCode) {
    switch (statusCode) {
      case 200:
        return 'OK';
      case 201:
        return 'Created';
      case 400:
        return 'Bad Request';
      case 401:
        return 'Unauthorized';
      case 403:
        return 'Forbidden';
      case 404:
        return 'Not Found';
      case 500:
        return 'Internal Server Error';
      default:
        return 'Unknown';
    }
  }
}
