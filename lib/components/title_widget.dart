import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class TitleWidget extends StatelessWidget {
  final String title;
  final Widget? action;
  final TextStyle? textStyle;
  const TitleWidget({
    super.key,
    required this.title,
    this.action,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            title,
            style:
                textStyle ??
                Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                  color:
                      Get.isDarkMode
                          ? kColorTextTersier
                          : kColorTextTersierLight,
                ),
          ),
        ),
        action ?? Container(),
      ],
    );
  }
}

class TitleSecondary extends StatelessWidget {
  final String title;
  const TitleSecondary({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
    );
  }
}
