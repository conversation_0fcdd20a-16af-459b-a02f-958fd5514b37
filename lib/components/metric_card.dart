import 'package:flutter/material.dart';
import '../widgets/gradient_progress_indicator.dart';
import '../utils/constants.dart';

class MetricCard extends StatelessWidget {
  final String title;
  final String target;
  final String actual;
  final double progress;
  final Gradient gradient;

  const MetricCard({
    super.key,
    required this.title,
    required this.target,
    required this.actual,
    required this.progress,
    required this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: paddingSmall,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: paddingSmall),
            child: Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: paddingSmall),
            child: GradientLinearProgressIndicator(
              value: progress > 1 ? 1 : progress,
              minHeight: 8,
              backgroundColor: Colors.grey[200],
              gradient: gradient,
            ),
          ),
          _buildMetricRow(
            'Target',
            target,
            textAlign: TextAlign.right,
            spacing: paddingSmall,
          ),
          _buildMetricRow(
            'Aktual',
            actual,
            isBold: true,
            textAlign: TextAlign.right,
            spacing: paddingSmall,
            gradient: gradient,
          ),
          _buildMetricRow('Kekurangan', 'Rp0', textAlign: TextAlign.right),
        ],
      ),
    );
  }

  Widget _buildMetricRow(
    String label,
    String value, {
    bool isBold = false,
    TextAlign textAlign = TextAlign.left,
    double spacing = 0,
    Gradient? gradient,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: spacing),
      child: Row(
        spacing: paddingSmall,
        children: [
          if (gradient != null)
            Container(
              decoration: BoxDecoration(
                gradient: gradient,
                borderRadius: BorderRadius.circular(paddingExtraSmall),
              ),
              height: 20,
              width: 20,
            ),
          Expanded(flex: 2, child: Text(label)),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: textAlign,
            ),
          ),
        ],
      ),
    );
  }
}
