import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ItemActionMenu extends StatelessWidget {
  const ItemActionMenu({
    super.key,
    this.child,
    this.width = 38,
    this.height = 38,
    this.onTap,
  });
  final Widget? child;
  final double width;
  final double height;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: kColorBgLight.withValues(alpha: 0.35),
          borderRadius: BorderRadius.circular(35),
        ),
        child: Center(child: child),
      ),
    );
  }
}
