import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlRadioButton extends StatelessWidget {
  final int index;
  final int selectedIndex;
  final String? title;
  final Widget? widget;
  final Function() onTap;
  const PdlRadioButton({
    super.key,
    required this.index,
    required this.selectedIndex,
    required this.onTap,
    this.title,
    this.widget,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        spacing: paddingSmall,
        children: [
          index == selectedIndex
              ? Icon(Icons.radio_button_checked, color: kColorGlobalBlue)
              : Icon(
                Icons.radio_button_unchecked,
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
          widget ??
              Text(
                title ?? 'title',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight:
                      index == selectedIndex
                          ? FontWeight.w700
                          : FontWeight.w400,
                ),
              ),
        ],
      ),
    );
  }
}
