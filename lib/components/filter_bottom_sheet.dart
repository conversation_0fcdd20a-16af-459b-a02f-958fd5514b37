import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class FilterBottomSheet extends StatefulWidget {
  final Map<String, dynamic>? initialFilters;
  final String userLevel;

  const FilterBottomSheet({
    super.key,
    this.initialFilters,
    required this.userLevel,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late String _selectedView;
  late List<String> _selectedStatus;

  @override
  void initState() {
    super.initState();
    // Initialize with provided filters or defaults
    _selectedView = widget.initialFilters?['view'] ?? 'Individu';
    _selectedStatus = List<String>.from(widget.initialFilters?['status'] ?? []);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? kColorBgDark : kColorBgLight,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Filter',
                style: TextStyle(fontSize: 22.0, fontWeight: FontWeight.w600),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 28.0),
                onPressed: () => Navigator.pop(context),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          if (widget.userLevel != kLevelBP) ...roleNonBP(),
          // Status checkboxes
          _buildStatusCheckbox('berlangsung'),
          const Divider(height: 1.0),
          _buildStatusCheckbox('disetujui'),
          const Divider(height: 1.0),
          _buildStatusCheckbox('ditolak'),

          const SizedBox(height: 36.0),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedView = 'Individu';
                      _selectedStatus = [];
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: Text(
                    'reset_str'.tr,
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16.0),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Only return filters that have values
                    final filters = <String, dynamic>{};
                    filters['view'] = _selectedView;

                    if (_selectedStatus.isNotEmpty) {
                      filters['status'] = _selectedStatus;
                    }

                    Navigator.pop(context, filters);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0099E1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: Text(
                    'apply_str'.tr,
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16.0),
        ],
      ),
    );
  }

  Widget _buildViewToggleButton(String title) {
    final isSelected = _selectedView == title;

    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedView = title;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12.0),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? Colors.white.withValues(alpha: 0.5)
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(
              color: isSelected ? Colors.grey.shade400 : Colors.grey.shade300,
              width: 1.0,
            ),
          ),
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                color: isSelected ? Colors.black87 : Colors.grey.shade700,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> roleNonBP() {
    return [
      const SizedBox(height: 32.0),
      // View section
      const Text(
        'View',
        style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w600),
      ),
      const SizedBox(height: 16.0),

      // View toggle buttons
      Row(
        children: [
          _buildViewToggleButton('Individu'),
          const SizedBox(width: 8.0),
          _buildViewToggleButton('Team'),
        ],
      ),
      const SizedBox(height: 32.0),

      // Status section
      const Text(
        'Status',
        style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w600),
      ),
      const SizedBox(height: 16.0),
    ];
  }

  Widget _buildStatusCheckbox(String title) {
    final isSelected = _selectedStatus.contains(title);

    return InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedStatus.remove(title);
          } else {
            _selectedStatus.add(title);
          }
        });
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Row(
          children: [
            // Container(
            //   width: 24.0,
            //   height: 24.0,
            //   decoration: BoxDecoration(
            //     shape: BoxShape.circle,
            //     border: Border.all(
            //       color: isSelected ? Colors.blue : Colors.grey.shade400,
            //       width: 2.0,
            //     ),
            //     color:
            //         isSelected
            //             ? Colors.blue.withValues(alpha: 0.1)
            //             : Colors.transparent,
            //   ),
            //   child:
            //       isSelected
            //           ? Center(
            //             child: Container(
            //               width: 12.0,
            //               height: 12.0,
            //               decoration: const BoxDecoration(
            //                 shape: BoxShape.circle,
            //                 color: Colors.blue,
            //               ),
            //             ),
            //           )
            //           : null,
            // ),
            SizedBox(
              width: 24,
              height: 24,
              child: Checkbox(
                fillColor: WidgetStatePropertyAll(
                  isSelected ? Colors.blue : Colors.transparent,
                ),
                checkColor: Colors.white,
                value: isSelected,
                onChanged: null,
              ),
            ),
            const SizedBox(width: 16.0),
            Text("${title}_str".tr, style: TextStyle(fontSize: 16.0)),
          ],
        ),
      ),
    );
  }
}
