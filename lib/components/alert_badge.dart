import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AlertBadge extends StatelessWidget {
  const AlertBadge({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(Routes.NOTIFICATION_PAGE),
      child: CircleAvatar(
        backgroundColor: kColorBgLight.withValues(alpha: 0.35),
        child: Utils.cachedSvgWrapper(
          'icon/notifikasi.svg',
          color: kColorBgLight,
        ),
      ),
    );
  }
}
