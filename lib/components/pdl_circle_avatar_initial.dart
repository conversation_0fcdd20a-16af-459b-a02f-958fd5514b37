import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';

class PdlCircleAvatarInitial extends StatelessWidget {
  final String name;
  final double size;
  final Color backgroundColor;
  final Color textColor;

  const PdlCircleAvatarInitial({
    super.key,
    required this.name,
    this.size = 48,
    this.backgroundColor = Colors.white,
    this.textColor = Colors.grey,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: size / 2,
      backgroundColor: backgroundColor,
      child: Text(
        name.getInitials(),
        style: TextStyle(
          color: textColor,
          fontSize: size / 3,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
