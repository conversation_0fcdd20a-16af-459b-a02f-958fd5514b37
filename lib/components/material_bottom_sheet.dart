import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/utils/constants.dart';

void showModalBottomSheetMaterial(
  BuildContext context, {
  required Widget child,
  bool isDimissable = true,
  String? title,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    enableDrag: isDimissable,
    isDismissible: isDimissable,
    builder:
        (context) => StatefulBuilder(
          builder: (context, setState) {
            return SafeArea(
              child: Wrap(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: paddingMedium,
                      right: paddingMedium,
                      top: paddingMedium,
                      bottom: MediaQuery.of(context).viewInsets.bottom,
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TitleWidget(
                                title: title ?? 'choose_menu_str'.tr,
                              ),
                            ),
                            GestureDetector(
                              onTap: () => Get.back(),
                              child: Icon(Icons.close),
                            ),
                          ],
                        ),
                        child,
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
  );
}
