import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlAccordion extends StatefulWidget {
  final Widget title;
  final Widget? header;
  final Widget? widgets;
  final bool? defaultShow;

  const PdlAccordion({
    super.key,
    required this.title,
    this.header,
    this.widgets,
    this.defaultShow,
  });
  @override
  PdlAccordionState createState() => PdlAccordionState();
}

class PdlAccordionState extends State<PdlAccordion> {
  bool _showContent = false;

  @override
  void initState() {
    super.initState();
    _showContent = widget.defaultShow ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _showContent = !_showContent;
              });
            },
            child: Container(
              color: Colors.transparent,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  widget.header ?? Container(),
                  const SizedBox(height: paddingExtraSmall),
                  Row(
                    children: [
                      Expanded(child: widget.title),
                      Row(
                        children: [
                          Icon(
                            (_showContent
                                ? Icons.expand_less
                                : Icons.expand_more),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          _showContent ? widget.widgets ?? Container() : Container(),
        ],
      ),
    );
  }
}
