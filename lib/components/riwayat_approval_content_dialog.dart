import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/vertical_dash_line.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RiwayatDialogContent extends StatelessWidget {
  final List<ApprovalDetail> approvalDetails;
  const RiwayatDialogContent({required this.approvalDetails, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: Get.height / 1.5, minHeight: 200),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: paddingMedium),
            for (int i = 0; i < approvalDetails.length; i++)
              IntrinsicHeight(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 40,
                      child: Column(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: kColorPaninBlue,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: kColorGlobalBlue100,
                                width: 3,
                              ),
                            ),
                          ),
                          Expanded(child: VerticalDashLine()),
                        ],
                      ),
                    ),
                    // Content card
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: paddingSmall),
                        child: Card(
                          color: Colors.transparent,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(radiusSmall),
                            side: BorderSide(
                              color:
                                  Get.isDarkMode
                                      ? kColorBorderDark
                                      : kColorBorderLight,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(paddingMedium),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      Utils.formatDate(
                                        approvalDetails[i].createdAt ?? '',
                                        format: 'dd/MM/yyyy HH:mm',
                                      ),
                                    ),
                                    Utils.getApprovalStatus(
                                      context,
                                      (approvalDetails[i].approvalStatus ??
                                                  '') ==
                                              'TERTUNDA'
                                          ? 'DIKEMBALIKAN'
                                          : approvalDetails[i].approvalStatus ??
                                              '',
                                    ),
                                  ],
                                ),
                                const SizedBox(height: paddingSmall),
                                Text(
                                  approvalDetails[i].actionBy?.name ?? '-',
                                  style:
                                      Theme.of(context).textTheme.titleMedium,
                                ),
                                const SizedBox(height: paddingSmall),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text('Catatan : '),
                                    const SizedBox(width: paddingExtraSmall),
                                    Expanded(
                                      child: Text(
                                        approvalDetails[i].remarks ?? '-',
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            SizedBox(height: paddingMedium),
          ],
        ),
      ),
    );
  }
}
