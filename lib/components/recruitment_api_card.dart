import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentApiCard extends StatelessWidget {
  final RecruitmentApiModel recruitment;
  final bool? fromKeagenan;
  final Function()? onTap;

  const RecruitmentApiCard({
    super.key,
    required this.recruitment,
    this.onTap,
    this.fromKeagenan = false,
  });

  @override
  Widget build(BuildContext context) {
    // Ambil nama dari recruitment atau tampilkan placeholder jika kosong
    final String name =
        recruitment.fullName?.isNotEmpty == true
            ? recruitment.fullName!
            : 'Kandidat';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        color: Colors.transparent,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child:
                      recruitment.passPhoto != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: Utils.cachedImageWrapper(
                              recruitment.passPhoto!,
                              isFullUrl: true,
                              fit: BoxFit.cover,
                              width: 60,
                            ),
                          )
                          : Text(
                            Utils.getInitials(name),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (fromKeagenan == true) Text('Rekrut'),
                            Text(
                              '${fromKeagenan == true ? '${recruitment.positionLevel} ' : ''}$name',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),

                            RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: recruitment.branch?.branchName ?? '-',
                                  ),
                                  if (recruitment.agentCode != null)
                                    TextSpan(text: ' - '),
                                  if (recruitment.agentCode != null)
                                    TextSpan(
                                      text: recruitment.agentCode ?? '-',
                                    ),
                                ],
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium?.copyWith(
                                  color:
                                      Get.isDarkMode
                                          ? kColorTextTersier
                                          : kColorTextTersierLight,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            SizedBox(height: paddingSmall),
                            Utils.getTrxStatus(
                              context,
                              (recruitment.trxStatus ?? '') == 'TERTUNDA'
                                  ? 'DIKEMBALIKAN'
                                  : recruitment.trxStatus ?? '',
                            ),
                            // Utils.getApprovalStatus(
                            //   context,
                            //   (recruitment.approvalHeader?.approvalStatus ??
                            //               'DRAFT') ==
                            //           'TERTUNDA'
                            //       ? 'DIKEMBALIKAN'
                            //       : recruitment
                            //               .approvalHeader
                            //               ?.approvalStatus ??
                            //           'DRAFT',
                            // ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor:
                            Get.isDarkMode
                                ? kColorGlobalBgDarkBlue
                                : kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
