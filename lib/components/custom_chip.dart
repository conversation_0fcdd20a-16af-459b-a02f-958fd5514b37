import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

enum ChipType { success, danger, warning, info, targetless }

class CustomChip extends StatelessWidget {
  final ChipType type;
  final String text;

  const CustomChip({super.key, required this.type, required this.text});

  @override
  Widget build(BuildContext context) {
    Color color;
    switch (type) {
      case ChipType.success:
        color = chipColorSuccess;
        break;
      case ChipType.danger:
        color = chipColorDanger;
        break;
      case ChipType.info:
        color = chipColorInfo;
        break;
      case ChipType.warning:
        color = chipColorWarning;
        break;
      case ChipType.targetless:
        color = Get.isDarkMode ? chipColorTargetFailDark : chipColorTargetFail;
        break;
    }
    return Container(
      decoration: BoxDecoration(
        color:
            type == ChipType.targetless
                ? color.withAlpha(20)
                : Get.isDarkMode
                ? lightenColor(color, 0.95)
                : color.withAlpha(20),
        borderRadius: BorderRadius.circular(
          paddingSmall * 2 + kDefaultFontSize,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: paddingSmall,
          horizontal: paddingSmall,
        ),
        child: Text(
            textAlign: TextAlign.center,
            text, style: TextStyle(color: color)),
      ),
    );
  }
}

Color lightenColor(Color color, [double amount = 0.1]) {
  return Color.lerp(color, Colors.white, amount)!;
}
