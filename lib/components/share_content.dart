import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/constants.dart';

class ShareContent extends StatelessWidget {
  final String url;
  final Function() onSuccess;
  final String? text;
  const ShareContent({
    super.key,
    required this.url,
    required this.onSuccess,
    this.text,
  });

  @override
  Widget build(BuildContext context) {
    String encodedUrl = Uri.encodeComponent(url);

    // final emailUri = Uri(
    //   scheme: 'mailto',
    //   path: '', // leave empty for no receiver
    //   queryParameters: {
    //     'subject': 'Recruitment Form Link',
    //     'body': text == null ? url : '$text\n\n$url',
    //   },
    // );
    final email = '';
    final subject = 'Recruitment Form Link';
    final body = text == null ? url : '$text\n\n$url';

    final emailUri = Uri.parse(
      'mailto:$email?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
    );
    return SizedBox(
      width: Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          Row(
            children: [
              _iconButton(
                context,
                title: 'WhatsApp',
                iconUrl: 'assets/icon/ic-wa.png',
                color: kColorWhatsapp,
                onTap: () {
                  launchUrl(
                    Uri.parse(
                      text == null
                          ? "https://api.whatsapp.com/send?text=$encodedUrl"
                          : "https://api.whatsapp.com/send?text=$text\n\n$encodedUrl",
                    ),
                    mode: LaunchMode.externalApplication,
                  );
                  Get.back();
                  onSuccess();
                },
              ),
              SizedBox(width: paddingMedium),
              _iconButton(
                context,
                title: 'Mail',
                // iconUrl: 'assets/icon/ic-wa.png',
                onTap: () {
                  launchUrl(emailUri);
                  Get.back();
                  onSuccess();
                },
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
          Container(
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorTextTersier : kLine,
              borderRadius: BorderRadius.circular(radiusMedium),
            ),
            padding: EdgeInsets.all(paddingSmall),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    url,
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: paddingSmall),
                PdlButton(
                  title: 'copy_str'.tr,
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: url));
                    Get.back();
                    Get.snackbar(
                      'Berhasil',
                      'Link berhasil disalin',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                    onSuccess();
                  },
                  radius: 50,
                ),
              ],
            ),
          ),
          SizedBox(height: paddingMedium),
          Divider(),
        ],
      ),
    );
  }

  GestureDetector _iconButton(
    BuildContext context, {
    required String title,
    String? iconUrl,
    Color? color,
    required Function() onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        child: Column(
          children: [
            Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                color: color ?? kColorTextTersier,
                borderRadius: BorderRadius.circular(45),
              ),
              padding: EdgeInsets.all(10),
              child:
                  iconUrl != null
                      ? Image.asset(iconUrl)
                      : Icon(Icons.mail_rounded, color: kColorBgLight),
            ),
            SizedBox(height: paddingSmall),
            Text(title),
          ],
        ),
      ),
    );
  }
}
