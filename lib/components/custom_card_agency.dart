import 'package:flutter/material.dart';
import 'package:get/get_core/get_core.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:pdl_superapp/utils/constants.dart';

class CustomCardAgencyWidget extends StatelessWidget {
  final String title;
  final Color titleColor;
  final Color startColor;
  final Color endColor;
  final String selected;

  const CustomCardAgencyWidget({
    super.key,
    required this.title,
    this.titleColor = Colors.black,
    required this.selected,
    this.startColor = Colors.purple,
    this.endColor = const Color(0xFFE0E0E0),
  });

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 8,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(10),
          boxShadow:
              title == selected
                  ? null
                  : [
                    BoxShadow(
                      color:
                          Get.isDarkMode
                              ? kColorBorderLight
                              : kColorBorderDark.withValues(alpha: 0.15),
                      blurRadius: 10,
                      offset: Offset(0, 1),
                    ),
                  ],
          border:
              title == selected
                  ? Border.all(color: kColorBorderLight, width: 2)
                  : null,
        ),

        clipBehavior: Clip.hardEdge,
        // padding: const EdgeInsets.all(24),
        child: Stack(
          children: [
            // Background decorative circle
            Positioned(
              right: -40,
              bottom: -35,
              child: Opacity(
                opacity: 0.18,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.center,
                      end: Alignment.bottomCenter,
                      colors: [startColor, endColor],
                    ),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            Positioned(
              right: 30,
              bottom: -10,
              child: Material(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(50),
                child: Opacity(
                  opacity: 0.3,
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [startColor, endColor],
                      ),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight:
                          title == selected ? FontWeight.w700 : FontWeight.w400,
                    ),
                  ),
                  if (title == selected) SizedBox(height: paddingSmall),
                  if (title == selected)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Container(
                        width: Get.width,
                        height: 8,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [startColor, endColor],
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
