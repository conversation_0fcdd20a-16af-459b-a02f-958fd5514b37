import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class LoginHeader extends StatelessWidget {
  final Widget? child;
  const LoginHeader({super.key, this.child});

  @override
  Widget build(BuildContext context) {
    // Get NetworkController instance, but only for mobile platforms
    NetworkController? networkController;
    if (!kIsWeb) {
      try {
        networkController = Get.find<NetworkController>();
      } catch (e) {
        // NetworkController might not be initialized yet
        networkController = null;
      }
    }

    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: Utils.cachedImageWrapper(
            'image/img-login-header.png',
            alignment: Alignment.topCenter,
          ),
        ),
        // Only show overlay when there's no connection (mobile only)
        if (!kIsWeb && networkController != null)
          Obx(() {
            if (!networkController!.isConnected.value) {
              return Positioned(child: Container(color: kColorBorderDark));
            }
            return const SizedBox.shrink();
          }),
        child ?? Container(),
        SizedBox(width: Get.width, height: 200),
      ],
    );
  }
}
