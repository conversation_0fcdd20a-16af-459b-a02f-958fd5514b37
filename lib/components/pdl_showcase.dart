import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:showcaseview/showcaseview.dart';

class PdlShowcase extends StatelessWidget {
  final GlobalKey globalKey;
  final Widget child;
  final double? radius;
  final String title, description;
  final int number, total;
  final TooltipPosition? tooltipPosition;
  final double? height;

  const PdlShowcase({
    super.key,
    required this.globalKey,
    required this.child,
    this.radius,
    this.height,
    required this.title,
    required this.description,
    required this.number,
    required this.total,
    this.tooltipPosition,
  });

  @override
  Widget build(BuildContext context) {
    return Showcase.withWidget(
      key: globalKey,
      height: height,
      width: Get.width - paddingExtraLarge,
      enableAutoScroll: true,
      tooltipPosition: tooltipPosition,
      disableMovingAnimation: true,
      targetBorderRadius: BorderRadius.circular(radius ?? 8),
      targetPadding: EdgeInsets.all(8),
      toolTipSlideEndDistance: 0,
      container: Builder(
        builder: (context) {
          // Calculate the position of the arrow based on the global key
          final RenderBox? renderBox =
              globalKey.currentContext?.findRenderObject() as RenderBox?;
          final position = renderBox?.localToGlobal(Offset.zero);
          final size = renderBox?.size;

          // Default arrow position (center) if can't determine
          double arrowLeftPosition = Get.width / -10;

          // Determine if tooltip is above or below the child
          bool isTooltipAbove = false;
          if (position != null && size != null) {
            final screenHeight = MediaQuery.of(context).size.height;
            // If the target is in the lower half of the screen, show tooltip above
            if (tooltipPosition != null) {
              isTooltipAbove = tooltipPosition == TooltipPosition.top;
            } else {
              isTooltipAbove = position.dy > screenHeight / 2;
            }

            // Calculate the center of the target widget
            final targetCenter = position.dx + (size.width / 2);
            // Adjust for the tooltip container margin
            arrowLeftPosition = targetCenter - paddingMedium - 15;
          }

          Color bgColor = Get.isDarkMode ? kColorBgDark : Colors.white;

          return Stack(
            clipBehavior: Clip.none,
            children: [
              // If tooltip is above, show triangle at bottom
              if (isTooltipAbove) ...[
                Container(
                  margin: EdgeInsets.only(bottom: 10),
                  width: Get.width - (paddingSmall * 2),
                  decoration: BoxDecoration(
                    color: bgColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: paddingMedium,
                      right: paddingMedium,
                      top: paddingMedium,
                      bottom: paddingSmall,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                title,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                            ),
                            IconButton(
                              icon: Icon(Icons.close),
                              onPressed: () {
                                ShowCaseWidget.of(showCaseContext!).dismiss();
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: paddingSmall),
                        Text(
                          description,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        SizedBox(height: paddingLarge),
                        Row(
                          children: [
                            Expanded(child: Text('$number/$total')),
                            if (number > 1)
                              PdlButton(
                                radius: 24,
                                backgroundColor:
                                    Theme.of(context).colorScheme.surface,
                                foregorundColor:
                                    Theme.of(context).colorScheme.primary,
                                borderColor:
                                    Theme.of(context).colorScheme.primary,
                                title: 'button_back'.tr,
                                onPressed: () {
                                  ShowCaseWidget.of(
                                    showCaseContext!,
                                  ).previous();
                                },
                              ),
                            SizedBox(width: paddingSmall),
                            PdlButton(
                              radius: 24,
                              title:
                                  number == total
                                      ? 'finish_str'.tr
                                      : 'button_next'.tr,
                              onPressed: () {
                                number == total
                                    ? ShowCaseWidget.of(
                                      showCaseContext!,
                                    ).dismiss()
                                    : ShowCaseWidget.of(
                                      showCaseContext!,
                                    ).next();
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: arrowLeftPosition,
                  child: ClipPath(
                    clipper: TriangleClipper(flip: true),
                    child: Container(width: 20, height: 10, color: bgColor),
                  ),
                ),
              ] else ...[
                // Main content container
                Container(
                  margin: EdgeInsets.only(top: 10),
                  width: Get.width - (paddingSmall * 2),
                  decoration: BoxDecoration(
                    color: bgColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: paddingMedium,
                      right: paddingMedium,
                      top: paddingMedium,
                      bottom: paddingSmall,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                title,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                            ),
                            GestureDetector(
                              child: Icon(Icons.close),
                              onTap: () {
                                ShowCaseWidget.of(showCaseContext!).dismiss();
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: paddingSmall),
                        Text(
                          description,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        SizedBox(height: paddingLarge),
                        Row(
                          children: [
                            Expanded(child: Text('$number/$total')),
                            if (number > 1)
                              PdlButton(
                                radius: 24,
                                backgroundColor:
                                    Theme.of(context).colorScheme.surface,
                                foregorundColor:
                                    Theme.of(context).colorScheme.primary,
                                borderColor:
                                    Theme.of(context).colorScheme.primary,
                                title: 'button_back'.tr,
                                onPressed: () {
                                  ShowCaseWidget.of(
                                    showCaseContext!,
                                  ).previous();
                                },
                              ),
                            SizedBox(width: paddingSmall),
                            PdlButton(
                              radius: 24,
                              title:
                                  number == total
                                      ? 'finish_str'.tr
                                      : 'button_next'.tr,
                              onPressed: () {
                                number == total
                                    ? ShowCaseWidget.of(
                                      showCaseContext!,
                                    ).dismiss()
                                    : ShowCaseWidget.of(
                                      showCaseContext!,
                                    ).next();
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                // Triangle arrow pointing up to the target widget
                Positioned(
                  top: 0,
                  left: arrowLeftPosition,
                  child: ClipPath(
                    clipper: TriangleClipper(),
                    child: Container(width: 20, height: 10, color: bgColor),
                  ),
                ),
              ],
            ],
          );
        },
      ),
      child: child,
    );
  }
}

class TriangleClipper extends CustomClipper<Path> {
  final bool flip;
  TriangleClipper({this.flip = false});

  @override
  Path getClip(Size size) {
    final path = Path();
    if (flip) {
      // Upside-down triangle
      path.moveTo(size.width / 2, size.height);
      path.lineTo(0, 0);
      path.lineTo(size.width, 0);
    } else {
      // Normal triangle
      path.moveTo(size.width / 2, 0);
      path.lineTo(0, size.height);
      path.lineTo(size.width, size.height);
    }
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
