import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// A general component for displaying agent photos with initials fallback
///
/// This component handles:
/// - Displaying agent photo from URL if available
/// - Showing initials if photo is null or empty
/// - Customizable size and styling
/// - Error handling for failed image loads
class AgentPhoto extends StatelessWidget {
  /// The agent's photo URL (can be null or empty)
  final String? photoUrl;

  /// The agent's name for generating initials
  final String? agentName;

  /// The size of the photo (width and height)
  final double size;

  /// Background color for the photo container
  final Color? backgroundColor;

  /// Text color for initials
  final Color? textColor;

  /// Text style for initials
  final TextStyle? textStyle;

  /// Border radius for the photo
  final double? borderRadius;

  /// Whether to show a border
  final bool showBorder;

  /// Border color
  final Color? borderColor;

  /// Border width
  final double borderWidth;

  const AgentPhoto({
    super.key,
    this.photoUrl,
    this.agentName,
    this.size = 40,
    this.backgroundColor,
    this.textColor,
    this.textStyle,
    this.borderRadius,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final bool hasPhoto = photoUrl != null && photoUrl!.isNotEmpty;
    final double radius = borderRadius ?? size / 2;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? kColorGlobalBgGreen,
        borderRadius: BorderRadius.circular(radius),
        border:
            showBorder
                ? Border.all(
                  color: borderColor ?? (Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
                  width: borderWidth,
                )
                : null,
      ),
      clipBehavior: Clip.hardEdge,
      child: hasPhoto ? _buildPhotoWidget(context) : _buildInitialsWidget(context),
    );
  }

  /// Build the photo widget when URL is available
  Widget _buildPhotoWidget(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: photoUrl!,
      fit: BoxFit.cover,
      alignment: Alignment.center,
      placeholder: (context, url) => _buildLoadingWidget(),
      errorWidget: (context, url, error) => _buildInitialsWidget(context),
    );
  }

  /// Build the initials widget when photo is not available or fails to load
  Widget _buildInitialsWidget(BuildContext context) {
    final String initials = Utils.getInitials(agentName ?? '-');
    final Color finalTextColor = textColor ?? kColorTextTersierLight;

    // Calculate font size based on container size
    final double fontSize = _calculateFontSize();

    return Center(
      child: Text(
        initials,
        style:
            textStyle ??
            Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: finalTextColor, fontWeight: FontWeight.w700, fontSize: fontSize),
      ),
    );
  }

  /// Build loading widget while image is loading
  Widget _buildLoadingWidget() {
    return Container(
      color: backgroundColor ?? kColorGlobalBgGreen,
      child: Center(
        child: SizedBox(
          width: size * 0.3,
          height: size * 0.3,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(textColor ?? kColorTextTersierLight),
          ),
        ),
      ),
    );
  }

  /// Calculate appropriate font size based on container size
  double _calculateFontSize() {
    if (size <= 24) return 10;
    if (size <= 32) return 12;
    if (size <= 40) return 14;
    if (size <= 48) return 16;
    if (size <= 64) return 18;
    if (size <= 80) return 22;
    if (size <= 100) return 26;
    return 30;
  }
}

/// Predefined sizes for common use cases
class AgentPhotoSize {
  static const double small = 24;
  static const double medium = 32;
  static const double large = 40;
  static const double extraLarge = 48;
  static const double huge = 64;
  static const double profile = 90;
}

/// Factory constructors for common use cases
class AgentPhotoFactory {
  /// Small photo for list items
  static AgentPhoto small({String? photoUrl, String? agentName, Color? backgroundColor, Color? textColor}) {
    return AgentPhoto(
      photoUrl: photoUrl,
      agentName: agentName,
      size: AgentPhotoSize.small,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );
  }

  /// Medium photo for cards
  static AgentPhoto medium({String? photoUrl, String? agentName, Color? backgroundColor, Color? textColor}) {
    return AgentPhoto(
      photoUrl: photoUrl,
      agentName: agentName,
      size: AgentPhotoSize.medium,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );
  }

  /// Large photo for headers
  static AgentPhoto large({String? photoUrl, String? agentName, Color? backgroundColor, Color? textColor}) {
    return AgentPhoto(
      photoUrl: photoUrl,
      agentName: agentName,
      size: AgentPhotoSize.large,
      backgroundColor: backgroundColor,
      textColor: textColor,
    );
  }

  /// Profile photo for profile pages
  static AgentPhoto profile({
    String? photoUrl,
    String? agentName,
    Color? backgroundColor,
    Color? textColor,
    bool showBorder = false,
  }) {
    return AgentPhoto(
      photoUrl: photoUrl,
      agentName: agentName,
      size: AgentPhotoSize.profile,
      backgroundColor: backgroundColor,
      textColor: textColor,
      showBorder: showBorder,
    );
  }
}
