import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// Widget wrapper yang mendeteksi visibility dan mengelola lazy loading
/// untuk widget content di home page
class LazyLoadingWidget extends StatelessWidget {
  final String widgetKey;
  final Widget child;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final double visibilityThreshold;
  final bool enableLazyLoading;

  const LazyLoadingWidget({
    super.key,
    required this.widgetKey,
    required this.child,
    this.loadingWidget,
    this.errorWidget,
    this.visibilityThreshold = 0.1, // 10% visibility threshold
    this.enableLazyLoading = true,
  });

  @override
  Widget build(BuildContext context) {
    // Jika lazy loading disabled, langsung return child
    if (!enableLazyLoading) {
      return child;
    }

    // Get atau create lazy loading controller
    final LazyLoadingController lazyController =
        Get.find<LazyLoadingController>();

    // Register widget setelah frame selesai untuk menghindari setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      lazyController.registerWidget(widgetKey);
    });

    return VisibilityDetector(
      key: Key('lazy_$widgetKey'),
      onVisibilityChanged: (VisibilityInfo info) {
        final isVisible = info.visibleFraction >= visibilityThreshold;
        lazyController.setWidgetVisible(widgetKey, isVisible);
      },
      child: Obx(() {
        final isLoading = lazyController.isWidgetLoading(widgetKey);
        final isLoaded = lazyController.isWidgetLoaded(widgetKey);

        // Jika sedang loading, tampilkan loading widget
        if (isLoading) {
          return _buildLoadingWidget(context);
        }

        // Jika belum loaded, tampilkan placeholder
        if (!isLoaded) {
          return _buildPlaceholderWidget(context);
        }

        // Jika sudah loaded, tampilkan child
        return child;
      }),
    );
  }

  /// Build loading widget
  Widget _buildLoadingWidget(BuildContext context) {
    if (loadingWidget != null) {
      return loadingWidget!;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(paddingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: paddingMedium),
          Text(
            'loading_str'.tr,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// Build placeholder widget sebelum loading
  Widget _buildPlaceholderWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(paddingLarge),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.visibility_outlined,
            size: 48,
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: paddingMedium),
          Text(
            'scroll_to_load_str'.tr,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Shimmer loading widget untuk memberikan efek loading yang lebih menarik
class ShimmerLoadingWidget extends StatelessWidget {
  final double height;
  final double width;
  final BorderRadius? borderRadius;

  const ShimmerLoadingWidget({
    super.key,
    this.height = 100,
    this.width = double.infinity,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
            Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: const _ShimmerAnimation(),
    );
  }
}

/// Animation untuk shimmer effect
class _ShimmerAnimation extends StatefulWidget {
  const _ShimmerAnimation();

  @override
  State<_ShimmerAnimation> createState() => _ShimmerAnimationState();
}

class _ShimmerAnimationState extends State<_ShimmerAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.transparent,
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                Colors.transparent,
              ],
              stops:
                  [
                    _animation.value - 0.3,
                    _animation.value,
                    _animation.value + 0.3,
                  ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }
}

/// Widget untuk menampilkan list shimmer loading
class ShimmerListWidget extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsets? padding;

  const ShimmerListWidget({
    super.key,
    this.itemCount = 3,
    this.itemHeight = 80,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(paddingMedium),
      child: Column(
        children: List.generate(
          itemCount,
          (index) => Padding(
            padding: const EdgeInsets.only(bottom: paddingMedium),
            child: ShimmerLoadingWidget(
              height: itemHeight,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom loading widget untuk widget tertentu
class CustomWidgetLoading extends StatelessWidget {
  final String widgetType;
  final String? title;

  const CustomWidgetLoading({super.key, required this.widgetType, this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(paddingLarge),
      child: Column(
        children: [
          if (title != null) ...[
            Text(
              title!,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: paddingMedium),
          ],
          _buildWidgetSpecificLoading(context),
        ],
      ),
    );
  }

  Widget _buildWidgetSpecificLoading(BuildContext context) {
    switch (widgetType) {
      case 'birthday_widget':
        return _buildBirthdayLoading(context);
      case 'claim_widget':
        return _buildClaimLoading(context);
      case 'polis_lapse_widget':
      case 'polis_jatuh_tempo_widget':
        return _buildPolisLoading(context);
      case 'production_widget':
        return _buildProductionLoading(context);
      case 'validasi_widget':
        return _buildValidasiLoading(context);
      case 'kompensasi_widget':
        return _buildKompensasiLoading(context);
      case 'persistensi_widget':
        return _buildPersistensiLoading(context);
      case 'spaj_widget':
        return _buildSpajLoading(context);
      default:
        return _buildDefaultLoading(context);
    }
  }

  Widget _buildBirthdayLoading(BuildContext context) {
    return const ShimmerListWidget(itemCount: 3, itemHeight: 60);
  }

  Widget _buildClaimLoading(BuildContext context) {
    return const ShimmerListWidget(itemCount: 2, itemHeight: 80);
  }

  Widget _buildPolisLoading(BuildContext context) {
    return const ShimmerListWidget(itemCount: 4, itemHeight: 50);
  }

  Widget _buildProductionLoading(BuildContext context) {
    return Column(
      children: [
        ShimmerLoadingWidget(
          height: 120,
          borderRadius: BorderRadius.circular(12),
        ),
        const SizedBox(height: paddingMedium),
        const ShimmerListWidget(itemCount: 2, itemHeight: 40),
      ],
    );
  }

  Widget _buildValidasiLoading(BuildContext context) {
    return Column(
      children: [
        ShimmerLoadingWidget(
          height: 80,
          borderRadius: BorderRadius.circular(8),
        ),
        const SizedBox(height: paddingMedium),
        const ShimmerListWidget(itemCount: 3, itemHeight: 50),
      ],
    );
  }

  Widget _buildKompensasiLoading(BuildContext context) {
    return Column(
      children: [
        ShimmerLoadingWidget(
          height: 60,
          borderRadius: BorderRadius.circular(8),
        ),
        const SizedBox(height: paddingMedium),
        const ShimmerListWidget(itemCount: 4, itemHeight: 45),
      ],
    );
  }

  Widget _buildPersistensiLoading(BuildContext context) {
    return Column(
      children: [
        ShimmerLoadingWidget(
          height: 100,
          borderRadius: BorderRadius.circular(8),
        ),
        const SizedBox(height: paddingMedium),
        const ShimmerListWidget(itemCount: 3, itemHeight: 55),
      ],
    );
  }

  Widget _buildSpajLoading(BuildContext context) {
    return const ShimmerListWidget(itemCount: 3, itemHeight: 70);
  }

  Widget _buildDefaultLoading(BuildContext context) {
    return const Column(
      children: [
        CircularProgressIndicator(),
        SizedBox(height: paddingMedium),
        ShimmerListWidget(itemCount: 2, itemHeight: 60),
      ],
    );
  }
}
