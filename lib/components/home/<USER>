import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class HomeTitle extends StatelessWidget {
  final String title;
  final Widget? actionWidget;
  const HomeTitle({super.key, required this.title, this.actionWidget});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
            ),
          ),
          actionWidget ?? Container(),
        ],
      ),
    );
  }
}
