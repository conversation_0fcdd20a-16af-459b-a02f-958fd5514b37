import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/icon_menu.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';

class HomeMenuItem {
  final String menuDesc;
  final String menuIcon;
  final String? menuUrl;

  const HomeMenuItem({
    required this.menuDesc,
    required this.menuIcon,
    this.menuUrl,
  });
}

class HomeMenu extends StatelessWidget {
  const HomeMenu({super.key});

  @override
  Widget build(BuildContext context) {
    List<HomeMenuItem> menus = [
      HomeMenuItem(
        menuDesc: "agent_menu_str".tr,
        menuIcon: "modul-keagenan.svg",
        menuUrl: Routes.MENU_KEAGENAN,
      ),
      HomeMenuItem(
        menuDesc: "prospect_menu_str".tr,
        menuIcon: "modul-prospek.svg",
        menuUrl: null,
      ),
      HomeMenuItem(
        menuDesc: "dashboard_menu_str".tr,
        menuIcon: "modul-dashboard.svg",
        menuUrl: null,
      ),
      HomeMenuItem(
        menuDesc: "business_menu_str".tr,
        menuIcon: "modul-bisnis.svg",
        menuUrl: null,
      ),
      HomeMenuItem(
        menuDesc: "training_menu_str".tr,
        menuIcon: "modul-pelatihan.svg",
        menuUrl: null,
      ),
      HomeMenuItem(
        menuDesc: "others_menu_str".tr,
        menuIcon: "modul-lainnya.svg",
        menuUrl: null,
      ),
    ];
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: GridView.count(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: NeverScrollableScrollPhysics(),
        childAspectRatio: 1 / 1.2,
        crossAxisCount: 4,
        children:
            menus
                .map(
                  (e) => IconMenu(
                    onTap: () {
                      if (e.menuUrl != null) {
                        Get.toNamed(Routes.MENU_KEAGENAN);
                      }
                    },
                    iconUrl: 'icon/${e.menuIcon}',
                    title: e.menuDesc,
                  ),
                )
                .toList(),
      ),
    );
  }
}
