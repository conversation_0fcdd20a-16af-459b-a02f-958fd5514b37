import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/widget/home_widget_spaj_controller.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

import '../../pages/widget/spaj_page.dart';
import '../custom_chip.dart';

class HomeWidgetSpaj extends StatelessWidget {
  HomeWidgetSpaj({super.key});

  final HomeWidgetSpajController controller = Get.put(
    HomeWidgetSpajController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.hasError.isTrue) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: 8),
              Text('Error: ${controller.errorMessage.value}'),
              const SizedBox(height: 16),
              WidgetLoadMore(onTap: () => controller.load()),
            ],
          ),
        );
      }

      return Column(
        children: [
          // Show tabs for BM role
          if (controller.userLevel != kLevelBP) _buildTabSelector(context),

          // Content based on role and selected tab
          _buildContent(context),

          WidgetLoadMore(onTap: () => controller.load()),
        ],
      );
    });
  }

  // Tab selector for BM role
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(200),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToIndividu(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Individu',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToTeam(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Team',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent(context) {
    return Obx(() {
      if (controller.arrData.isEmpty) {
        return const Center(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Text('Tidak ada data SPAJ'),
          ),
        );
      }

      return Column(
        children: [
          for (int i = 0; i < min(controller.arrData.length, 3); i++)
            _card(
              context,
              spajData: controller.arrData[i],
              isLast: i == controller.arrData.length - 1,
            ),
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              title: "show_details_str".tr,
              onPressed:
                  () => Get.toNamed(
                    '${Routes.SPAJ}?tab=${controller.selectedSection}',
                  ),
            ),
          ),
        ],
      );
    });
  }

  Widget _card(context, {required WidgetSpajModels spajData, bool? isLast}) {
    return Container(
      padding: EdgeInsets.only(top: paddingSmall),
      width: Get.width,
      child: Column(
        children: [
          if (controller.userLevel != kLevelBP &&
              controller.selectedSection.value != 0)
            // Hide profile ketika show individu tab / level = BP
            Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: paddingSmall),
                  child: Row(
                    children: [
                      CircleAvatar(
                        child: Center(
                          child: Text(spajData.agentName?[0] ?? '-'),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              spajData.agentName == null
                                  ? '-'
                                  : "${spajData.agentName}${(controller.userLevel.inList([kLevelBM, kLevelBD]) && controller.agentName == spajData.agentName && controller.selectedSection.value == 1) ? " (${"me_str".tr})" : ""}",
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            Text(
                              spajData.agentCode ?? '-',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(),
              ],
            ),
          _cardTitle(context, spajData: spajData),
          SizedBox(height: paddingSmall),
          _cardContent(context, spajData: spajData),
          _notesContent(
            context,
            value: spajData.descriptionPending ?? "",
            desc: "status_str".tr,
          ),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              style: ButtonStyle(
                padding: WidgetStatePropertyAll(EdgeInsets.zero),
              ),
              onPressed:
                  () => showNotesSpajBottomSheet(
                    context,
                    spajData.remarkPending ?? "",
                  ),
              child: Text(
                "see_details_str".tr,
                style: TextStyle(color: Colors.blue.shade400, fontSize: 15),
              ),
            ),
          ),
          if (isLast != true) Divider(),
        ],
      ),
    );
  }

  Padding _cardContent(context, {required WidgetSpajModels spajData}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Column(
        children: [
          _contentField(
            context,
            title: 'policy_holder_name_str'.tr,
            value: spajData.policyHolderName ?? '-',
          ),
          _contentField(
            context,
            title: 'premium_str'.tr,
            value: Utils.currencyFormatters(
              data: spajData.basicPremium.toString(),
              currency: spajData.currency,
            ),
          ),
          _contentField(
            context,
            title: 'payment_period_str'.tr,
            value: spajData.frequency ?? '-',
          ),
          if (spajData.spajStatus != kSpajStatAccept)
            _contentField(
              context,
              title: 'submit_date_str'.tr,
              value: DateFormat(
                'dd/MM/yyyy',
              ).format(DateTime.parse(spajData.submitDate!)),
            ),
        ],
      ),
    );
  }

  Widget _contentField(
    context, {
    required String title,
    required String value,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10.0),
      child: Row(
        children: [
          Expanded(child: Text(title)),
          Text(
            value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
        ],
      ),
    );
  }

  Row _cardTitle(context, {required WidgetSpajModels spajData}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            spajData.spajNumber ?? '-',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        CustomChip(
          type:
              spajData.spajStatus == kSpajStatAccept
                  ? ChipType.success
                  : ChipType.warning,
          text:
              [
                    kSpajStatAccept,
                    kSpajStatAcceptPendingPrint,
                    kSpajStatPolicy,
                  ].contains(spajData.spajStatus)
                  ? "${spajData.spajStatus!.toLowerCase().replaceAll(" ", "_")}_status_str"
                      .tr
                  : "-",
        ),
      ],
    );
  }

  Widget _notesContent(context, {required String value, required String desc}) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            desc.tr,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
          Html(
            data: value.isNotEmpty ? value : "-",
            style: {"body": Style(margin: Margins.all(0))},
          ),
        ],
      ),
    );
  }
}
