import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PdlBottomSheet {
  final String title;
  final Widget content;
  final bool? noPadding;
  final bool? dismissable;
  final double? radius;
  final TextStyle? titleStyle;
  PdlBottomSheet({
    required this.content,
    required this.title,
    this.noPadding,
    this.dismissable,
    this.radius,
    this.titleStyle,
  }) {
    if (kIsWeb) {
      PdlBaseDialog(context: Get.context!, title: title, child: content);
    } else {
      Get.bottomSheet(
        Container(
          width: Get.width,
          padding: EdgeInsets.symmetric(
            horizontal: noPadding != true ? paddingMedium : 0,
            vertical: noPadding != true ? paddingLarge : 0,
          ),
          constraints: BoxConstraints(
            maxHeight: Get.height / 1.2,
            minHeight: 200,
          ),
          decoration: BoxDecoration(
            color: Theme.of(Get.context!).colorScheme.surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(radius ?? 20),
              topRight: Radius.circular(radius ?? 20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: TitleWidget(title: title, textStyle: titleStyle),
                  ),
                  dismissable == false
                      ? Container()
                      : GestureDetector(
                        onTap: () => Get.back(),
                        child: Icon(Icons.close),
                      ),
                ],
              ),
              content,
            ],
          ),
        ),
        isScrollControlled: true,
        enableDrag: dismissable ?? true,
        isDismissible: dismissable ?? true,
      );
    }
  }
}
