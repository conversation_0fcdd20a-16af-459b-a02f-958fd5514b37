import 'package:flutter/material.dart';
import '../utils/constants.dart';

class StatusRow extends StatelessWidget {
  final String label;
  final String? value;
  final Widget? customWidget;
  final bool isHorizontal;

  const StatusRow({super.key, required this.label, this.value, this.customWidget, this.isHorizontal = true});

  @override
  Widget build(BuildContext context) {
    if (isHorizontal == false) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: paddingSmall,
        children: [Text(label, style: TextStyle(fontWeight: FontWeight.bold)), customWidget ?? Text(value ?? '')],
      );
    }

    return Row(
      spacing: paddingSmall,
      children: [
        Text(label, style: TextStyle(fontWeight: FontWeight.bold)),
        Spacer(),
        customWidget ?? Text(value ?? ''),
      ],
    );
  }
}
