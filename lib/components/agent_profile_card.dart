import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/agent_photo.dart';
import 'package:pdl_superapp/models/simple_profile_models.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// Agent profile card component for displaying agent information
/// Shows agent photo, name, code, and level in a card format
class AgentProfileCard extends StatelessWidget {
  /// The agent profile data
  final SimpleProfileModels? agentProfile;

  /// Whether to show a loading state
  final bool isLoading;

  const AgentProfileCard({super.key, this.agentProfile, this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingCard(context);
    }

    if (agentProfile == null) {
      return _buildEmptyCard(context);
    }

    return _buildProfileCard(context);
  }

  /// Build the main profile card
  Widget _buildProfileCard(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // Agent photo
          AgentPhoto(
            photoUrl: agentProfile?.agentPhoto,
            agentName: agentProfile?.agentName,
            size: AgentPhotoSize.large,
            showBorder: true,
          ),

          SizedBox(width: paddingMedium),

          // Agent information
          Expanded(
            child: Text(
              '${agentProfile?.agentLevel} ${agentProfile?.agentName}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading state card
  Widget _buildLoadingCard(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // Loading photo placeholder
          Container(
            width: AgentPhotoSize.large,
            height: AgentPhotoSize.large,
            decoration: BoxDecoration(
              color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              borderRadius: BorderRadius.circular(AgentPhotoSize.large / 2),
            ),
            child: Center(child: SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))),
          ),

          SizedBox(width: paddingMedium),

          // Loading text placeholders
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name placeholder
                Container(
                  width: Get.width * 0.4,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),

                SizedBox(height: paddingExtraSmall),

                // Code placeholder
                Container(
                  width: Get.width * 0.25,
                  height: 14,
                  decoration: BoxDecoration(
                    color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),

                SizedBox(height: paddingExtraSmall),

                // Level placeholder
                Container(
                  width: 40,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state card
  Widget _buildEmptyCard(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // Empty photo
          AgentPhoto(photoUrl: null, agentName: null, size: AgentPhotoSize.large),

          SizedBox(width: paddingMedium),

          // Empty state text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Agent tidak ditemukan',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Get.isDarkMode ? kColorTextDark : kColorTextTersierLight),
                ),

                SizedBox(height: paddingExtraSmall),

                Text(
                  'Data profil agent tidak tersedia',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Get.isDarkMode ? kColorTextDark : kColorTextTersierLight),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
