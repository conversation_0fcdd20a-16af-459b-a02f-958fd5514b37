import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class EmptyStateView extends StatelessWidget {
  const EmptyStateView({super.key, this.msg, this.imageUrl});
  final String? imageUrl;
  final String? msg;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            imageUrl ?? 'icon/illustration-empty-no-found.svg',
            width: 220,
            height: 220,
          ),
          SizedBox(height: paddingLarge),
          Padding(
            padding: EdgeInsetsGeometry.symmetric(horizontal: paddingMedium),
            child: Text(
              msg ?? 'Hmmm.. belum ada data tersedia',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
              ),
            ),
          ),
          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }
}
