import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiTypeBottomSheet extends StatelessWidget {
  const TerminasiTypeBottomSheet({
    super.key,
    required this.onTap,
    required this.iconUrl,
    required this.title,
    required this.description,
  });
  final VoidCallback onTap;
  final String iconUrl, title, description;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Color.fromARGB(255, 233, 245, 251),
                shape: BoxShape.circle,
              ),
              width: 50,
              height: 50,
              padding: EdgeInsets.all(12),
              child: Utils.cachedSvgWrapper(iconUrl, color: Color(0xFF0C9DEB)),
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: paddingSmall),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
