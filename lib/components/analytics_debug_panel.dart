import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// Debug panel for testing Firebase Analytics
/// Only visible in debug mode
class AnalyticsDebugPanel extends StatelessWidget {
  const AnalyticsDebugPanel({super.key});

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(paddingMedium),
      padding: const EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(Icons.bug_report, color: Colors.orange, size: 20),
              SizedBox(width: paddingSmall),
              Text(
                'Analytics Debug Panel',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange[800],
                ),
              ),
            ],
          ),
          SizedBox(height: paddingSmall),
          Text(
            'Firebase Analytics Debug View is enabled. Check Firebase Console > Analytics > DebugView',
            style: TextStyle(fontSize: 12, color: Colors.orange[700]),
          ),
          SizedBox(height: paddingSmall),
          Text(
            'Tip: Open Firebase Console > Analytics > DebugView to see events in real-time',
            style: TextStyle(
              fontSize: 11,
              fontStyle: FontStyle.italic,
              color: Colors.orange[600],
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildDebugButton(String label, VoidCallback onPressed) {
  //   return ElevatedButton(
  //     onPressed: onPressed,
  //     style: ElevatedButton.styleFrom(
  //       backgroundColor: Colors.orange[100],
  //       foregroundColor: Colors.orange[800],
  //       padding: EdgeInsets.symmetric(
  //         horizontal: paddingSmall,
  //         vertical: paddingSmall / 2,
  //       ),
  //       textStyle: TextStyle(fontSize: 12),
  //     ),
  //     child: Text(label),
  //   );
  // }
}

/// Floating debug button that can be added to any page
class AnalyticsDebugFab extends StatelessWidget {
  const AnalyticsDebugFab({super.key});

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton.small(
      onPressed: () => _showDebugDialog(context),
      backgroundColor: Colors.orange,
      tooltip: 'Analytics Debug',
      child: Icon(Icons.analytics, color: Colors.white),
    );
  }

  void _showDebugDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.analytics, color: Colors.orange),
                SizedBox(width: paddingSmall),
                Text('Analytics Debug'),
              ],
            ),
            content: SingleChildScrollView(child: AnalyticsDebugPanel()),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Close'),
              ),
            ],
          ),
    );
  }
}

/// Extension to easily add debug panel to any widget
extension AnalyticsDebugExtension on Widget {
  /// Add analytics debug panel below this widget (debug mode only)
  Widget withAnalyticsDebug() {
    if (!kDebugMode) return this;

    return Column(children: [this, AnalyticsDebugPanel()]);
  }

  /// Add floating analytics debug button (debug mode only)
  Widget withAnalyticsDebugFab() {
    if (!kDebugMode) return this;

    return Stack(
      children: [
        this,
        Positioned(bottom: 16, right: 16, child: AnalyticsDebugFab()),
      ],
    );
  }
}
