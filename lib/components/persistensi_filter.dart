import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PersistensiFilter extends StatelessWidget {
  final String selectedView;
  final List<String> availableViews;
  final List<BranchModels> branches;
  final List<String> selectedBranches;
  final Function(String) onViewChanged;
  final Function(List<String>) onBranchesChanged;
  final VoidCallback onReset;
  final VoidCallback onApply;

  const PersistensiFilter({
    super.key,
    required this.selectedView,
    required this.availableViews,
    required this.branches,
    required this.selectedBranches,
    required this.onViewChanged,
    required this.onBranchesChanged,
    required this.onReset,
    required this.onApply,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Get.back(),
                icon: Icon(Icons.close),
              ),
            ],
          ),
          
          SizedBox(height: paddingMedium),
          
          // View Selection
          Text(
            'View',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          SizedBox(height: paddingSmall),
          
          ...availableViews.map((view) => _buildViewOption(view)),
          
          SizedBox(height: paddingMedium),
          
          // Branch Selection (only show if branches are available)
          if (branches.isNotEmpty) ...[
            Text(
              'Cabang',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            SizedBox(height: paddingSmall),
            
            // Branch list with checkboxes
            Container(
              constraints: BoxConstraints(maxHeight: 200),
              child: SingleChildScrollView(
                child: Column(
                  children: branches.map((branch) => _buildBranchOption(branch)).toList(),
                ),
              ),
            ),
          ],
          
          SizedBox(height: paddingLarge),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: PdlButton(
                  onPressed: onReset,
                  title: 'Reset',
                  backgroundColor: Colors.grey[300],
                  foregorundColor: Colors.black,
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: PdlButton(
                  onPressed: () {
                    onApply();
                    Get.back();
                  },
                  title: 'Terapkan',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildViewOption(String view) {
    final isSelected = selectedView == view;
    return GestureDetector(
      onTap: () => onViewChanged(view),
      child: Container(
        margin: EdgeInsets.only(bottom: paddingSmall),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey,
                  width: 2,
                ),
                color: isSelected ? Colors.blue : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            SizedBox(width: paddingSmall),
            Text(
              view,
              style: TextStyle(
                fontSize: 14,
                color: isSelected ? Colors.blue : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBranchOption(BranchModels branch) {
    final isSelected = selectedBranches.contains(branch.branchCode);
    return GestureDetector(
      onTap: () {
        List<String> newSelection = List.from(selectedBranches);
        if (isSelected) {
          newSelection.remove(branch.branchCode);
        } else {
          newSelection.add(branch.branchCode!);
        }
        onBranchesChanged(newSelection);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: paddingSmall),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: isSelected ? Colors.blue : Colors.grey,
                  width: 2,
                ),
                color: isSelected ? Colors.blue : Colors.transparent,
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : null,
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Text(
                branch.branchName ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected ? Colors.blue : Colors.black,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
