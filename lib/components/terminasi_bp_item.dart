import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiBpItem extends StatelessWidget {
  const TerminasiBpItem({
    super.key,
    required this.onTap,
    required this.iconUrl,
    required this.title,
    required this.description,
  });
  final VoidCallback onTap;
  final String iconUrl, title, description;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: kColorGlobalBlue,
                shape: BoxShape.circle,
              ),
              width: 50,
              height: 50,
              padding: EdgeInsets.all(12),
              child: Utils.cachedSvgWrapper(iconUrl, color: kColorGlobalBlue),
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: paddingSmall),
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: paddingSmall),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  SizedBox(height: paddingSmall),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
