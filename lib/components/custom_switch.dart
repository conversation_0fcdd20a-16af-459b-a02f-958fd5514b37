import 'package:flutter/material.dart';

class CustomSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const CustomSwitch({super.key, required this.value, required this.onChanged});

  @override
  CustomSwitchState createState() => CustomSwitchState();
}

class CustomSwitchState extends State<CustomSwitch> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onChanged(!widget.value);
      },
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            width: 35,
            height: 15,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color:
                  widget.value
                      ? Colors.blue.withValues(alpha: 0.5)
                      : Colors.grey.withValues(alpha: 0.5),
            ),
          ),
          AnimatedPositioned(
            duration: Duration(milliseconds: 300),
            left: widget.value ? 15 : 0,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.value ? Colors.blue : Colors.grey,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 5,
                    offset: Offset(2, 2),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(width: 40, height: 20),
        ],
      ),
    );
  }
}
