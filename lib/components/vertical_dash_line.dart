import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';

class VerticalDashLine extends StatelessWidget {
  const VerticalDashLine({
    super.key,
    this.width = 1,
    this.height = double.infinity,
  });
  final double width, height;

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DashedLineVerticalPainter(),
      size: Size(width, height),
    );
  }
}

class _DashedLineVerticalPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double dashHeight = 5, dashSpace = 5, startY = 0;
    final paint =
        Paint()
          ..color = kColorBorderLight
          ..strokeWidth = 1;
    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashHeight), paint);
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
