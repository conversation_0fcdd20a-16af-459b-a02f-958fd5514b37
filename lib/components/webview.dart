import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/import_helper_web/import_helper.dart';

class WebviewPage extends StatefulWidget {
  final String fullUrl;
  final Function(bool)? onScrolledToBottom;
  const WebviewPage({
    super.key,
    required this.fullUrl,
    this.onScrolledToBottom,
  });

  @override
  State<WebviewPage> createState() => _WebviewPageState();
}

class _WebviewPageState extends State<WebviewPage> {
  double loadingProgress = 0.0;
  final GlobalKey webViewKey = GlobalKey();
  InAppWebViewController? webViewController;
  late SharedPreferences prefs;
  bool isLoading = true;
  bool hasError = false;
  String? errorMessage;
  String? iframeViewType;
  bool hasScrolledToBottom = false;

  InAppWebViewSettings get options => InAppWebViewSettings(
    useShouldOverrideUrlLoading: true,
    mediaPlaybackRequiresUserGesture: false,
    javaScriptEnabled: true,
    useHybridComposition: true,
    allowsInlineMediaPlayback: true,
    sharedCookiesEnabled: true,
    // iOS specific settings
    allowsBackForwardNavigationGestures: true,
    allowsLinkPreview: false,
    isFraudulentWebsiteWarningEnabled: false,
    selectionGranularity: SelectionGranularity.CHARACTER,
    dataDetectorTypes: [DataDetectorTypes.NONE],
    // Additional iOS WebView settings to prevent blank screen
    suppressesIncrementalRendering: false,
    allowsAirPlayForMediaPlayback: true,
    allowsPictureInPictureMediaPlayback: true,
    // Security and performance settings
    javaScriptCanOpenWindowsAutomatically: false,
    minimumFontSize: 0,
    // Network settings
    cacheEnabled: true,
    clearCache: false,
    // Rendering settings
    transparentBackground: false,
    disableVerticalScroll: false,
    disableHorizontalScroll: false,
  );

  @override
  void initState() {
    super.initState();
    initiate();
  }

  initiate() async {
    prefs = await SharedPreferences.getInstance();

    // For web platform, create iframe
    if (kIsWeb) {
      _createIframe();
    }
  }

  void _createIframe() {
    if (kIsWeb) {
      // Use helper function to create iframe with scroll callback
      iframeViewType = createWebViewIframe(
        widget.fullUrl,
        onScrolledToBottom:
            widget.onScrolledToBottom != null
                ? (scrolled) {
                  if (!hasScrolledToBottom) {
                    setState(() {
                      hasScrolledToBottom = true;
                    });
                    widget.onScrolledToBottom!(scrolled);
                  }
                }
                : null,
      );

      // Set loading to false after a short delay
      Future.delayed(Duration(milliseconds: 1000), () {
        if (mounted) {
          setState(() {
            isLoading = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          if (hasError)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Failed to load page',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    errorMessage ?? 'Unknown error occurred',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _reloadWebView,
                    child: Text('Retry'),
                  ),
                ],
              ),
            )
          else if (kIsWeb)
            // For web platform, use helper function
            createWebViewWidget(iframeViewType)
          else
            InAppWebView(
              key: webViewKey,
              initialSettings: options,
              onOverScrolled: (controller, x, y, clampedX, clampedY) {
                if (!hasScrolledToBottom) {
                  setState(() {
                    hasScrolledToBottom = true;
                  });
                  if (widget.onScrolledToBottom != null) {
                    widget.onScrolledToBottom!(true);
                  }
                }
              },
              onWebViewCreated: (controller) async {
                webViewController = controller;
                try {
                  Uri url = Uri.parse(widget.fullUrl);
                  await controller.loadUrl(
                    urlRequest: URLRequest(url: WebUri.uri(url)),
                  );
                } catch (e) {
                  setState(() {
                    hasError = true;
                    errorMessage = 'Invalid URL: ${widget.fullUrl}';
                    isLoading = false;
                  });
                }
              },
              onLoadStart: (controller, url) {
                setState(() {
                  isLoading = true;
                  hasError = false;
                });
              },
              onLoadStop: (controller, url) async {
                setState(() {
                  isLoading = false;
                });

                // Update app bar title with page title
                try {
                  String? title = await controller.getTitle();
                  if (title != null && title.isNotEmpty) {
                    setState(() {
                      // Update title if needed
                    });
                  }
                  int? contentHeight = await controller.getContentHeight();
                  if ((contentHeight ?? 0) < 600) {
                    hasScrolledToBottom = true;
                    widget.onScrolledToBottom!(true);
                  }
                } catch (e) {
                  // print('Error getting page title: $e');
                }

                // Add scroll listener to detect when user reaches bottom
                _addScrollListener(controller);
              },
              onProgressChanged: (controller, progress) {
                setState(() {
                  loadingProgress = progress / 100.0;
                });
              },
              onReceivedError: (controller, request, error) {
                setState(() {
                  hasError = true;
                  errorMessage = error.description;
                  isLoading = false;
                });
              },
              onReceivedHttpError: (controller, request, errorResponse) {
                setState(() {
                  // hasError = true;
                  errorMessage = 'HTTP Error: ${errorResponse.statusCode}';
                  isLoading = false;
                });
              },
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                // Allow all navigation for now
                return NavigationActionPolicy.ALLOW;
              },
            ),

          // Loading indicator
          if (isLoading && !hasError)
            Container(
              color: Colors.white,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(kIsWeb ? 'Loading web content...' : 'Loading...'),
                    if (loadingProgress > 0 && !kIsWeb)
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 8,
                        ),
                        child: LinearProgressIndicator(value: loadingProgress),
                      ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _addScrollListener(InAppWebViewController controller) async {
    // Inject JavaScript to detect scroll position
    await controller.evaluateJavascript(
      source: """
      function checkScrollPosition() {
        var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        var windowHeight = window.innerHeight;
        var documentHeight = document.documentElement.scrollHeight;

        // Check if user has scrolled to bottom (with small tolerance)
        var isAtBottom = scrollTop + windowHeight >= documentHeight - 10;

        if (isAtBottom && !window.hasReachedBottom) {
          window.hasReachedBottom = true;
          window.flutter_inappwebview.callHandler('scrolledToBottom', true);
        }
      }

      // Check on scroll
      window.addEventListener('scroll', checkScrollPosition);

      // Check initially in case content is short
      setTimeout(checkScrollPosition, 1000);
    """,
    );

    // Add handler for JavaScript callback
    controller.addJavaScriptHandler(
      handlerName: 'scrolledToBottom',
      callback: (args) {
        if (!hasScrolledToBottom) {
          setState(() {
            hasScrolledToBottom = true;
          });
          if (widget.onScrolledToBottom != null) {
            widget.onScrolledToBottom!(true);
          }
        }
      },
    );
  }

  void _reloadWebView() {
    if (kIsWeb) {
      // For web, recreate the iframe
      setState(() {
        hasError = false;
        isLoading = true;
        errorMessage = null;
        iframeViewType = null;
        hasScrolledToBottom = false;
      });
      _createIframe();
      return;
    }

    setState(() {
      hasError = false;
      isLoading = true;
      errorMessage = null;
      hasScrolledToBottom = false;
    });

    webViewController?.reload();
  }
}
