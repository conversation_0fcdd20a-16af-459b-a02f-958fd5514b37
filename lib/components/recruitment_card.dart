import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentCard extends StatelessWidget {
  final RecruitmentFormModel form;
  final Function()? onTap;

  const RecruitmentCard({super.key, required this.form, this.onTap});

  @override
  Widget build(BuildContext context) {
    // Ambil nama dari form atau tampilkan placeholder jika kosong
    final String name =
        form.namaKtp?.isNotEmpty == true ? form.namaKtp! : 'Kandidat Baru';

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: Get.width,
        color: Colors.transparent,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar dengan inisial nama
                CircleAvatar(
                  backgroundColor: kColorGlobalBlue,
                  child: _buildPassPhoto(form, name),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              name,
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            Text(
                              form.candidateBranch ?? '-',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                            SizedBox(height: paddingMedium),
                            Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color:
                                        Get.isDarkMode
                                            ? kColorGlobalBgDarkBlue
                                            : kColorBorderLight,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: EdgeInsets.all(paddingExtraSmall),
                                  child: Text(
                                    'Draft',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.w500,
                                      color:
                                          Get.isDarkMode
                                              ? kColorTextDark
                                              : kColorTextLight,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      CircleAvatar(
                        backgroundColor:
                            Get.isDarkMode
                                ? kColorGlobalBgDarkBlue
                                : kColorGlobalBgBlue,
                        child: Icon(
                          Icons.chevron_right_outlined,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }

  Widget _buildPassPhoto(RecruitmentFormModel form, String name) {
    // If pasFotoImageUrl is not empty, use it as network image
    if (form.pasFotoImageUrl != null && form.pasFotoImageUrl!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Utils.cachedImageWrapper(
          form.pasFotoImageUrl!,
          isFullUrl: true,
          fit: BoxFit.cover,
          width: 60,
        ),
      );
    }

    // If pasFotoImagePath exists, use it as local file
    if (form.pasFotoImagePath != null && form.pasFotoImagePath!.isNotEmpty) {
      try {
        return ClipRRect(
          borderRadius: BorderRadius.circular(30),
          child: Image.file(
            File(form.pasFotoImagePath!),
            fit: BoxFit.cover,
            width: 60,
            height: 60,
          ),
        );
      } catch (e) {
        // If file doesn't exist or can't be loaded, show initials
        return Text(
          Utils.getInitials(name),
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        );
      }
    }

    // If no image available, show initials
    return Text(
      Utils.getInitials(name),
      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
    );
  }
}
