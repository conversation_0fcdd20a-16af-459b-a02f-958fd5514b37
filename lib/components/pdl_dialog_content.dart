import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PdlDialogContent extends StatelessWidget {
  final String message;
  final Function() onTap;
  final String? textButtonPos;
  final Widget? icon;

  const PdlDialogContent({
    super.key,
    required this.message,
    required this.onTap,
    this.textButtonPos,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: isWideScreen ? Get.width / 3 : Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingSmall),
          <PERSON><PERSON><PERSON><PERSON>(
            width: 60,
            height: 50,
            child:
                icon ?? Utils.cachedSvgWrapper('icon/ic-dialog-question.svg'),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: paddingMedium),
          <PERSON><PERSON><PERSON><PERSON>(
            width: Get.width,
            child: Text(message, textAlign: TextAlign.center),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: paddingMedium),
          <PERSON>zed<PERSON>ox(
            width: Get.width,
            child: Row(
              children: [
                Expanded(
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFFD1D1D1)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text(
                      'label_cancel'.tr,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    onPressed: () => Get.back(),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: FilledButton(
                    onPressed: onTap,
                    child: Text(textButtonPos ?? 'label_sure'.tr),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
