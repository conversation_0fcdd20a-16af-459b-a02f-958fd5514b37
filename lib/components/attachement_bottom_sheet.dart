import 'dart:io';
import 'dart:math';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/controllers/profile/attachement_bottom_controller.dart';
import 'package:pdl_superapp/models/upload_document_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AttachementBottomSheet extends StatelessWidget {
  final bool? isKtp;
  final bool? isKK;
  final bool? isBank;
  final Function(UploadDocumentModels) onFinish;

  AttachementBottomSheet({
    super.key,
    required this.onFinish,
    this.isKtp,
    this.isBank,
    this.isKK,
  });

  final AttachementBottomController controller = Get.put(
    AttachementBottomController(),
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: Get.height / 1.35, minHeight: 200),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: paddingMedium),
            Text(
              'subtitle_support_document'.tr,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: paddingSmall),
            if (isKtp == true)
              _buildSection(
                context,
                'title_new_ktp'.tr,
                controller.ktpFile,
                controller.indicatorKtp,
                'ktp',
              ),
            if (isKK == true)
              _buildSection(
                context,
                'title_new_kk'.tr,
                controller.kkFile,
                controller.indicatorKK,
                'kk',
              ),
            if (isBank == true)
              _buildSection(
                context,
                'title_new_bank_number'.tr,
                controller.bankFile,
                controller.indicatorBank,
                'bank',
              ),
            SizedBox(height: paddingLarge),
            Obx(() {
              final bool canSubmit =
                  controller.areRequiredFilesUploaded(
                    isKtp: isKtp,
                    isKK: isKK,
                    isBank: isBank,
                  ) &&
                  !controller.isAnyFileUploading &&
                  !controller.isSubmitting.value;

              return PdlButton(
                onPressed: canSubmit ? () => _handleSubmit() : null,
                title: 'label_send_document'.tr,
                width: Get.width,
                controller: controller.isSubmitting.value ? controller : null,
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    Rx<XFile?> file,
    RxDouble indicator,
    String type,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _title(context, title),
        Obx(() => _imageContainer(context, file.value, indicator, type)),
        Obx(() {
          final errorMessage = controller.getErrorMessage(type);
          if (errorMessage.isNotEmpty) {
            return Padding(
              padding: const EdgeInsets.only(top: paddingSmall),
              child: Text(
                errorMessage,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            );
          }
          return SizedBox.shrink();
        }),
      ],
    );
  }

  Widget _imageContainer(
    BuildContext context,
    XFile? image,
    RxDouble uploadIndicator,
    String type,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child:
          image != null
              ? _uploadedImageView(context, image, uploadIndicator, type)
              : _uploadButton(context, type),
    );
  }

  Widget _uploadedImageView(
    BuildContext context,
    XFile image,
    RxDouble uploadIndicator,
    String type,
  ) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => _showDeleteDialog(context, type),
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                _buildImageWidget(image),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        image.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      FutureBuilder<int>(
                        future: image.length(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Text(
                              _formatFileSize(snapshot.data!, 1),
                              style: Theme.of(context).textTheme.bodySmall,
                            );
                          }
                          return Text(
                            'Loading...',
                            style: Theme.of(context).textTheme.bodySmall,
                          );
                        },
                      ),
                    ],
                  ),
                ),
                Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
              ],
            ),
          ),
        ),
        // Show upload progress indicator
        if (uploadIndicator.value > 0.0 && uploadIndicator.value < 100.0)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      value: uploadIndicator.value / 100,
                      backgroundColor: Colors.white.withValues(alpha: 0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                    SizedBox(height: paddingSmall),
                    Text(
                      '${uploadIndicator.value.toInt()}%',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _uploadButton(BuildContext context, String type) {
    return Obx(() {
      final bool isUploading = _isUploadingForType(type);

      return GestureDetector(
        onTap:
            isUploading
                ? null
                : () => Utils.imagePicker(
                  context,
                  validateFileSize: true,
                  maxSizeMB: 2.0,
                  onSuccess: (XFile image) => _updateImage(type, image),
                  onError:
                      (String errorMessage) =>
                          controller.setErrorMessage(type, errorMessage),
                ),
        child: Opacity(
          opacity: isUploading ? 0.5 : 1.0,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DottedBorder(
                radius: Radius.circular(8),
                borderType: BorderType.RRect,
                color: Get.isDarkMode ? kColorBorderDark : kColorTextDark,
                dashPattern: [8, 8],
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (isUploading)
                        SizedBox(
                          width: 32,
                          height: 32,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      else
                        Icon(
                          Icons.image,
                          color: Theme.of(context).colorScheme.primary,
                          size: 32,
                        ),
                      SizedBox(height: paddingSmall),
                      Text(
                        isUploading ? 'Uploading...' : 'title_upload_photo'.tr,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: paddingSmall),
                child: Text(
                  'label_image_warning'.tr,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  void _updateImage(String type, XFile image) async {
    // Clear error message when valid file is selected
    controller.clearErrorMessage(type);

    // File size validation is now handled in Utils.imagePicker
    switch (type) {
      case 'ktp':
        controller.ktpFile.value = image;
        controller.performUpdatePicture(image: image, type: 'ktp');
        break;
      case 'kk':
        controller.kkFile.value = image;
        controller.performUpdatePicture(image: image, type: 'kk');
        break;
      case 'bank':
        controller.bankFile.value = image;
        controller.performUpdatePicture(image: image, type: 'bank');
        break;
    }
  }

  void _showDeleteDialog(BuildContext context, String type) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        message: 'title_delete_attachement'.trParams({
          'item': type.toUpperCase(),
        }),
        onTap: () {
          Get.back();
          _deleteImage(type);
        },
      ),
    );
  }

  void _deleteImage(String type) {
    // Clear error message when deleting image
    controller.clearErrorMessage(type);

    switch (type) {
      case 'ktp':
        controller.ktpFile.value = null;
        controller.ktpUrl.value = '';
        controller.indicatorKtp.value = 0.0;
        controller.isUploadingKtp.value = false;
        break;
      case 'kk':
        controller.kkFile.value = null;
        controller.kkURl.value = '';
        controller.indicatorKK.value = 0.0;
        controller.isUploadingKK.value = false;
        break;
      case 'bank':
        controller.bankFile.value = null;
        controller.bankUrl.value = '';
        controller.indicatorBank.value = 0.0;
        controller.isUploadingBank.value = false;
        break;
    }
  }

  /// Format file size from bytes to human readable format
  String _formatFileSize(int bytes, int decimals) {
    if (bytes <= 0) return "0 B";
    const suffixes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    var i = (log(bytes) / log(1024)).floor();
    return '${(bytes / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  /// Build image widget that works on both web and mobile platforms
  Widget _buildImageWidget(XFile image) {
    if (kIsWeb) {
      // On web, use Image.network with the XFile path
      return Image.network(
        image.path,
        width: 60,
        height: 60,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 60,
            height: 60,
            color: Colors.grey[300],
            child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
          );
        },
      );
    } else {
      // On mobile, use Image.file
      return Image.file(
        File(image.path),
        width: 60,
        height: 60,
        fit: BoxFit.cover,
      );
    }
  }

  Widget _title(BuildContext context, String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: paddingMedium),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
      ),
    );
  }

  /// Check if a specific file type is currently uploading
  bool _isUploadingForType(String type) {
    switch (type) {
      case 'ktp':
        return controller.isUploadingKtp.value;
      case 'kk':
        return controller.isUploadingKK.value;
      case 'bank':
        return controller.isUploadingBank.value;
      default:
        return false;
    }
  }

  /// Handle submit button press with loading state
  void _handleSubmit() async {
    controller.isSubmitting.value = true;

    try {
      UploadDocumentModels data = UploadDocumentModels(
        kkUrl: controller.kkURl.value,
        ktpUrl: controller.ktpUrl.value,
        bankUrl: controller.bankUrl.value,
      );

      onFinish(data);
      Get.back();
    } finally {
      controller.isSubmitting.value = false;
    }
  }
}
