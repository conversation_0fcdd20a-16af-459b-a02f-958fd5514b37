import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/components/custom_toast.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ChooseMethodPickImage {
  void show(
    BuildContext context, {
    required String title,
    required Function(XFile file) onSuccess,
    required Function(String errorMessage) onError,
  }) {
    // Clear error message when opening bottom sheet
    RxString profilePictureErrorMessage = ''.obs;
    PdlBottomSheet(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'choose_from_gallery_str'.tr,
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-image.svg',
              width: 24,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  validateFileSize: true,
                  maxSizeMB: 2.0,
                  onSuccess: (val) async {
                    profilePictureErrorMessage.value = '';
                    Get.back();
                    onSuccess(val);
                  },
                  onError: (String errorMessage) {
                    profilePictureErrorMessage.value = errorMessage;
                  },
                ),
          ),
          Divider(),

          CommonWidgets.bottomSheetOptionCard(
            context,
            title: 'take_photo_str'.tr,
            prefix: Utils.cachedSvgWrapper(
              'icon/ic-linear-camera-minimalistic.svg',
              width: 24,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onTap:
                () => Utils.imagePicker(
                  context,
                  isCamera: true,
                  validateFileSize: true,
                  maxSizeMB: 2.0,
                  onSuccess: (val) async {
                    profilePictureErrorMessage.value = '';
                    Get.back();
                    onSuccess(val);
                  },
                  onError: (String errorMessage) {
                    AppToast.error(message: errorMessage);
                    profilePictureErrorMessage.value = errorMessage;
                    onError(errorMessage);
                  },
                ),
          ),
          Obx(() {
            if (profilePictureErrorMessage.value.isNotEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: paddingMedium),
                child: Text(
                  profilePictureErrorMessage.value,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
                  textAlign: TextAlign.center,
                ),
              );
            }
            return SizedBox.shrink();
          }),
        ],
      ),
    );
  }
}
