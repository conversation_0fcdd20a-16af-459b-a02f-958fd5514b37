import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FilterMonthYear extends StatefulWidget {
  final Map<String, dynamic>? initialFilters;

  const FilterMonthYear({super.key, this.initialFilters});

  @override
  State<FilterMonthYear> createState() => _FilterMonthYearState();
}

class _FilterMonthYearState extends State<FilterMonthYear> {
  late DateTime _startDate;
  late DateTime _endDate;

  // List of months
  final List<String> _monthsShort = [
    'jan_3_char_str'.tr,
    'feb_3_char_str'.tr,
    'mar_3_char_str'.tr,
    'apr_3_char_str'.tr,
    'may_3_char_str'.tr,
    'jun_3_char_str'.tr,
    'jul_3_char_str'.tr,
    'aug_3_char_str'.tr,
    'sep_3_char_str'.tr,
    'oct_3_char_str'.tr,
    'nov_3_char_str'.tr,
    'dec_3_char_str'.tr,
  ];

  // Format for display
  late final DateFormat _displayFormat = DateFormat('MMMM yyyy');
  bool _isLocaleInitialized = false;

  @override
  void initState() {
    super.initState();

    // Initialize date formatting
    initializeDateFormatting('id_ID', null).then((_) {
      if (mounted) {
        setState(() {
          _isLocaleInitialized = true;
        });
      }
    });

    // Initialize with provided filters or defaults
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month, 1);

    if (widget.initialFilters != null &&
        widget.initialFilters!.containsKey('startDate') &&
        widget.initialFilters!.containsKey('endDate')) {
      // Use the DateTime objects if available, otherwise parse from string
      if (widget.initialFilters!.containsKey('startDateTime') &&
          widget.initialFilters!.containsKey('endDateTime')) {
        _startDate = widget.initialFilters!['startDateTime'];
        _endDate = widget.initialFilters!['endDateTime'];
      } else {
        // Parse the formatted date strings
        _startDate = DateTime.parse(widget.initialFilters!['startDate']);
        _endDate = DateTime.parse(widget.initialFilters!['endDate']);
      }
    } else {
      // Default: current month and next month
      _startDate = currentMonth;
      _endDate = DateTime(currentMonth.year, currentMonth.month + 1, 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(paddingLarge),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.0),
          topRight: Radius.circular(16.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar at the top
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: paddingMedium),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),

          // Header with title and close button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'filter_date_str'.tr,
                style: TextStyle(
                  fontSize: 22.0,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, size: 24.0),
                onPressed: () => Navigator.pop(context),
                style: IconButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.surfaceDim,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: paddingLarge),

          // Date range visualization
          Card(
            child: Container(
              padding: const EdgeInsets.all(paddingMedium),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(
                    context,
                  ).colorScheme.surface.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateCard(
                          title: 'from_str'.tr,
                          date: _startDate,
                          onTap: () => _selectStartDate(context),
                          isStart: true,
                        ),
                      ),
                      const SizedBox(width: paddingMedium),
                      const Icon(Icons.arrow_forward, color: Colors.grey),
                      const SizedBox(width: paddingMedium),
                      Expanded(
                        child: _buildDateCard(
                          title: 'to_str'.tr,
                          date: _endDate,
                          onTap: () => _selectEndDate(context),
                          isStart: false,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: paddingMedium),
                  // Range info
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: paddingSmall,
                      horizontal: paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE3F2FD),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 16,
                          color: kColorPaninBlue,
                        ),
                        const SizedBox(width: paddingSmall),
                        Text(
                          'min_1_month'.tr,
                          style: TextStyle(
                            color: kColorPaninBlue,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const Spacer(),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetDates,
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400),
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: Text(
                    'reset_str'.tr,
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16.0),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // Format dates as yyyy-MM-dd for API
                    final startDateFormatted = DateFormat(
                      'yyyy-MM-dd',
                    ).format(_startDate);
                    final endDateFormatted = DateFormat(
                      'yyyy-MM-dd',
                    ).format(_endDate);

                    final filters = <String, dynamic>{
                      'startDate': startDateFormatted,
                      'endDate': endDateFormatted,
                      // Keep the original DateTime objects for UI display
                      'startDateTime': _startDate,
                      'endDateTime': _endDate,
                    };
                    Navigator.pop(context, filters);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kColorPaninBlue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                  child: Text(
                    'apply_str'.tr,
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  // Build date card
  Widget _buildDateCard({
    required String title,
    required DateTime date,
    required VoidCallback onTap,
    required bool isStart,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(paddingMedium),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Text(
                    _isLocaleInitialized
                        ? DateFormat(
                          'MMMM yyyy',
                          Get.locale?.toString() ?? "id_ID",
                        ).format(date)
                        : _displayFormat.format(date),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(Icons.calendar_month, color: kColorPaninBlue, size: 18),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Select start date
  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await _showMonthYearPicker(context, _startDate);

    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;

        // Ensure end date is at least 1 month after start date
        final minEndDate = DateTime(_startDate.year, _startDate.month + 1, 1);
        if (_endDate.isBefore(minEndDate)) {
          _endDate = minEndDate;
        }
      });
    }
  }

  // Select end date
  Future<void> _selectEndDate(BuildContext context) async {
    // Calculate minimum allowed end date (start date + 1 month)
    final minDate = DateTime(_startDate.year, _startDate.month + 1, 1);

    final DateTime? picked = await _showMonthYearPicker(
      context,
      _endDate,
      minDate: minDate,
    );

    if (picked != null && picked != _endDate) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  // Reset dates to default
  void _resetDates() {
    setState(() {
      final now = DateTime.now();
      final currentMonth = DateTime(now.year, now.month, 1);
      _startDate = currentMonth;
      _endDate = DateTime(currentMonth.year, currentMonth.month + 1, 1);
    });
    Navigator.pop(context, "reset");
  }

  // Show month-year picker
  Future<DateTime?> _showMonthYearPicker(
    BuildContext context,
    DateTime initialDate, {
    DateTime? minDate,
  }) async {
    final now = DateTime.now();
    final firstDate = DateTime(now.year - 4, 1);
    final lastDate = DateTime(now.year + 1, 12);
    DateTime selectedDate = initialDate;
    DateTime? result;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.all(paddingLarge),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar at the top
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      margin: const EdgeInsets.only(bottom: paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),

                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        minDate != null
                            ? 'Pilih Bulan Akhir'
                            : 'Pilih Bulan Awal',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey.shade100,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: paddingMedium),

                  // Selected date display
                  Container(
                    padding: const EdgeInsets.all(paddingMedium),
                    decoration: BoxDecoration(
                      color: const Color(0xFFE3F2FD),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.calendar_month,
                          color: kColorPaninBlue,
                        ),
                        const SizedBox(width: paddingMedium),
                        Text(
                          _isLocaleInitialized
                              ? DateFormat(
                                'MMMM yyyy',
                                'id_ID',
                              ).format(selectedDate)
                              : _displayFormat.format(selectedDate),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: kColorPaninBlue,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: paddingLarge),

                  // Year selection
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: paddingSmall,
                      horizontal: paddingMedium,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.arrow_back_ios, size: 18),
                          onPressed: () {
                            final newDate = DateTime(
                              selectedDate.year - 1,
                              selectedDate.month,
                              1,
                            );
                            if (!newDate.isBefore(firstDate)) {
                              setState(() => selectedDate = newDate);
                            }
                          },
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        Text(
                          selectedDate.year.toString(),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.arrow_forward_ios, size: 18),
                          onPressed: () {
                            final newDate = DateTime(
                              selectedDate.year + 1,
                              selectedDate.month,
                              1,
                            );
                            if (!newDate.isAfter(lastDate)) {
                              setState(() => selectedDate = newDate);
                            }
                          },
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: paddingMedium),

                  // Month grid
                  Expanded(
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            childAspectRatio: 1.5,
                            crossAxisSpacing: paddingSmall,
                            mainAxisSpacing: paddingSmall,
                          ),
                      itemCount: 12,
                      itemBuilder: (context, index) {
                        final month = index + 1;
                        final date = DateTime(selectedDate.year, month, 1);
                        final isSelected = selectedDate.month == month;
                        final isDisabled =
                            minDate != null && date.isBefore(minDate);

                        return InkWell(
                          onTap:
                              isDisabled
                                  ? null
                                  : () {
                                    setState(() => selectedDate = date);
                                  },
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                                  isSelected ? kColorPaninBlue : Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color:
                                    isDisabled
                                        ? Colors.grey.shade300
                                        : isSelected
                                        ? kColorPaninBlue
                                        : Colors.grey.shade300,
                              ),
                              boxShadow:
                                  isSelected
                                      ? [
                                        BoxShadow(
                                          color: kColorPaninBlue.withAlpha(76),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                      : null,
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              _monthsShort[index],
                              style: TextStyle(
                                color:
                                    isDisabled
                                        ? Colors.grey.shade400
                                        : isSelected
                                        ? Colors.white
                                        : Colors.black,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // Action buttons
                  Padding(
                    padding: const EdgeInsets.only(top: paddingMedium),
                    child: Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.grey.shade400),
                              padding: const EdgeInsets.symmetric(
                                vertical: 16.0,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            child: const Text(
                              'Batal',
                              style: TextStyle(
                                fontSize: 16.0,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16.0),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              result = selectedDate;
                              Navigator.pop(context);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: kColorPaninBlue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                vertical: 16.0,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            child: const Text(
                              'Pilih',
                              style: TextStyle(
                                fontSize: 16.0,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );

    return result;
  }
}
