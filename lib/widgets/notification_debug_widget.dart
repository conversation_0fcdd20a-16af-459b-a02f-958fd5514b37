import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/notification_service.dart';
import 'package:pdl_superapp/utils/notification_test_helper.dart';

/// Widget untuk debugging notification functionality
class NotificationDebugWidget extends StatelessWidget {
  const NotificationDebugWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification Debug Panel',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Status indicators
            Obx(() {
              final service = NotificationService.instance;
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusRow(
                    'Permission Granted',
                    service.isPermissionGranted,
                  ),
                  _buildStatusRow('FCM Enabled', service.isFCMEnabled),
                  _buildStatusRow(
                    'FCM Token Available',
                    service.fcmToken.isNotEmpty,
                  ),
                ],
              );
            }),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),

            // Test buttons
            const Text(
              'Test Actions',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _runQuickTest(),
                  icon: const Icon(Icons.flash_on),
                  label: const Text('Quick Test'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _runFullTest(),
                  icon: const Icon(Icons.bug_report),
                  label: const Text('Full Test'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _sendTestNotification(),
                  icon: const Icon(Icons.notifications),
                  label: const Text('Test Notification'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _debugStatus(),
                  icon: const Icon(Icons.info),
                  label: const Text('Debug Status'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _reinitializeService(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Reinitialize'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testPermissions(),
                  icon: const Icon(Icons.security),
                  label: const Text('Test Permissions'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _testForegroundIssue(),
                  icon: const Icon(Icons.bug_report_outlined),
                  label: const Text('Test Foreground'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),

            // FCM Token display
            const Text(
              'FCM Token',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Obx(() {
              final token = NotificationService.instance.fcmToken;
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Text(
                  token.isEmpty ? 'No token available' : token,
                  style: TextStyle(
                    fontSize: 12,
                    fontFamily: 'monospace',
                    color: token.isEmpty ? Colors.red : Colors.black87,
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  void _runQuickTest() async {
    Get.snackbar(
      'Test Started',
      'Running quick notification test...',
      snackPosition: SnackPosition.BOTTOM,
    );

    await NotificationTestHelper.instance.quickDebugTest();

    Get.snackbar(
      'Test Completed',
      'Check logs for results',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _runFullTest() async {
    Get.snackbar(
      'Full Test Started',
      'Running comprehensive notification test...',
      snackPosition: SnackPosition.BOTTOM,
    );

    await NotificationTestHelper.instance.runFullNotificationTest();

    Get.snackbar(
      'Full Test Completed',
      'Check logs for detailed results',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _sendTestNotification() async {
    await NotificationService.instance.sendTestNotification();

    Get.snackbar(
      'Test Notification',
      'Test notification sent - check notification panel',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _debugStatus() {
    NotificationService.instance.debugNotificationStatus();

    Get.snackbar(
      'Debug Status',
      'Status logged - check console',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _reinitializeService() async {
    Get.snackbar(
      'Reinitializing',
      'Reinitializing notification service...',
      snackPosition: SnackPosition.BOTTOM,
    );

    await NotificationService.instance.reinitialize();

    Get.snackbar(
      'Reinitialized',
      'Notification service reinitialized',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _testPermissions() async {
    Get.snackbar(
      'Testing Permissions',
      'Testing notification permissions...',
      snackPosition: SnackPosition.BOTTOM,
    );

    await NotificationTestHelper.instance.testNotificationPermissions();

    Get.snackbar(
      'Permissions Tested',
      'Check logs for permission status',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _testForegroundIssue() async {
    Get.snackbar(
      'Testing Foreground Issue',
      'Running comprehensive foreground notification test...',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );

    await NotificationTestHelper.instance.testForegroundNotificationIssue();

    Get.snackbar(
      'Foreground Test Completed',
      'Check logs for detailed results. WorkSource warnings are normal.',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }
}
