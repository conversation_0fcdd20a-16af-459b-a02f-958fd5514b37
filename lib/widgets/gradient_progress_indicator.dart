import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

class GradientLinearProgressIndicator extends StatelessWidget {
  final double value;
  final double minHeight;
  final Gradient gradient;
  final Color? backgroundColor;

  const GradientLinearProgressIndicator({
    super.key,
    required this.value,
    this.minHeight = 12.0,
    required this.gradient,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: minHeight,
      width: double.infinity,
      decoration: BoxDecoration(
        color:
            backgroundColor ??
            (Get.isDarkMode ? kColorGlobalBgDarkBlue : Colors.grey[200]),
        borderRadius: BorderRadius.circular(200),
      ),
      child: FractionallySizedBox(
        widthFactor: _getValidWidthFactor(value),
        alignment: Alignment.centerLeft,
        child: Container(
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(200),
          ),
          clipBehavior: Clip.hardEdge,
        ),
      ),
    );
  }

  // Helper method to ensure the widthFactor is valid (between 0.0 and 1.0)
  double _getValidWidthFactor(double value) {
    // Handle NaN
    if (value.isNaN) {
      return 0.0;
    }

    // Clamp between 0.0 and 1.0
    if (value > 1.0) {
      return 1.0;
    } else if (value < 0.0) {
      return 0.0;
    }

    return value;
  }
}
