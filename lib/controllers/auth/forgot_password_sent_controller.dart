import 'dart:async';
import 'dart:convert';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';

class ForgotPasswordSentController extends BaseControllers {
  // Timer properties
  RxInt countdownSeconds = 600.obs; // 10 minutes = 600 seconds
  RxBool isCountdownActive = true.obs;
  Timer? _timer;

  // Email data for resend
  String agentCode = '';
  String email = '';

  @override
  void onInit() {
    super.onInit();
    startCountdown();
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  void startCountdown() {
    isCountdownActive.value = true;
    countdownSeconds.value = 600; // Reset to 10 minutes

    _timer?.cancel(); // Cancel any existing timer
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdownSeconds.value > 0) {
        countdownSeconds.value--;
      } else {
        isCountdownActive.value = false;
        timer.cancel();
      }
    });
  }

  String get formattedCountdown {
    int minutes = countdownSeconds.value ~/ 60;
    int seconds = countdownSeconds.value % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void setEmailData({required String agentCode, required String email}) {
    this.agentCode = agentCode;
    this.email = email;
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    // After successful resend, start countdown again
    startCountdown();
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    // Show error message but don't restart countdown
    Get.snackbar(
      'Error',
      response.body['error_description'] ?? 'error_resent_email_str'.tr,
      snackPosition: SnackPosition.TOP,
    );
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);

    setLoading(false);
    Get.snackbar(
      'Error',
      response?.body['error_description'] ?? 'error_resent_email_str'.tr,
      snackPosition: SnackPosition.TOP,
    );
  }

  void onTapResend() async {
    setLoading(true);

    String secretKey = EnvLoader.get("PRIVATE_KEY");

    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final username = Encryption(secretKey).doencrypt(agentCode, agentCode);

    final emailEncrypted = Encryption(secretKey).doencrypt(email, email);

    var data = {"username": username, "email": emailEncrypted};
    setLoading(true);
    await api.performForgotPassword(controllers: this, data: data);
  }
}
