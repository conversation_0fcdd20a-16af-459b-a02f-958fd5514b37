import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pdl_superapp/components/language_toggle.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/flyer_models.dart';
import 'package:pdl_superapp/utils/analytics_utils.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:pdl_superapp/utils/secure_storage_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginController extends BaseControllers {
  final LocalAuthentication auth = LocalAuthentication();
  RxBool biometricAvailable = false.obs;

  TextEditingController usernameTextController = TextEditingController();
  TextEditingController passwordTextController = TextEditingController();

  RxBool isButtonAvailable = false.obs;
  RxBool isInvalid = false.obs;

  // Remember me functionality
  RxBool rememberMe = false.obs;

  String encyrptedUsername = '';
  String encryptedPassword = '';

  // flyer
  RxList<FlyerModels> flyerArrData = RxList();
  late SharedPreferences prefs;
  LanguagToggleController langC = Get.put(LanguagToggleController());
  late NetworkController networkController;

  @override
  Future<void> onInit() async {
    super.onInit();
    if (!kIsWeb) {
      try {
        networkController = Get.find<NetworkController>();
      } catch (e) {
        networkController = Get.put(NetworkController());
        await networkController.onInit();
      }
    }
    prefs = await SharedPreferences.getInstance();
    _getEncryptedLoginData();
    getFlyer();
    loadSavedCredentials();
    if (!kIsWeb) {
      checkBiometrict();
    }

    // Track login page view (without user ID since user hasn't logged in yet)
    _trackLoginPageView();
  }

  /// Track login page view for analytics (without user data)
  void _trackLoginPageView() {
    // Use Future.microtask to avoid blocking the UI
    Future.microtask(() async {
      try {
        await AnalyticsUtils.trackPageView(
          pageClass: 'LoginPage',
          parameters: {
            'page_type': 'authentication',
            'has_remember_me': 'true',
            'platform': kIsWeb ? 'web' : 'mobile',
          },
        );
      } catch (e) {
        // Silently handle analytics errors to not affect user experience
      }
    });
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqLogin:
        // Save credentials if remember me is enabled
        if (rememberMe.value) {
          SecureStorageService.replaceCredentials(
            username: usernameTextController.text,
            password: passwordTextController.text,
          );
        } else {
          SecureStorageService.loginWithoutRemember();
        }

        Utils.setLoggedIn(
          token: response['accessToken'],
          refreshToken: response['refreshToken'],
          username: encyrptedUsername,
          password: encryptedPassword,
          languageCode: langC.isIndo.value ? 'id_ID' : 'en_US',
          needPassReset: response['needPassReset'] ?? false,
          remainingDays: response['remainingDays'] ?? 0,
        );
        break;
      case kReqGetFlyer:
        parseFlyer(response);
        break;
      default:
    }
  }

  getTnc() {}

  getFlyer() async {
    await api.getFlyer(controllers: this, code: kReqGetFlyer, isToday: true);
  }

  parseFlyer(response) {
    flyerArrData.clear();

    for (int i = 0; i < response['content'].length; i++) {
      FlyerModels data = FlyerModels.fromJson(response['content'][i]);
      flyerArrData.add(data);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isInvalid.value = true;
  }

  checkBiometrict() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    if (canAuthenticateWithBiometrics == true) {
      final List<BiometricType> availableBiometrics =
          await auth.getAvailableBiometrics();

      if (availableBiometrics.isNotEmpty) {
        biometricAvailable.value =
            prefs.getBool(kStorageIsBiometricActive) ?? false;
      } else {
        biometricAvailable.value = false;
      }
    }
  }

  /// Load saved credentials from secure storage
  loadSavedCredentials() async {
    try {
      // Load remember me preference
      // rememberMe.value = await SecureStorageService.getRememberMe();

      // Load saved credentials if remember me is enabled
      // if (rememberMe.value) {
      // final credentials = await SecureStorageService.getSavedCredentials();
      // if (credentials['username'] != null && credentials['password'] != null) {
      //   usernameTextController.text = credentials['username']!;
      //   passwordTextController.text = credentials['password']!;
      //   rememberMe.value = true;
      //   emptValidator(); // Update button availability
      // }
      // // }
      final username = await SecureStorageService.preloadUsername();
      usernameTextController.text = username;
      if (username.isNotEmpty) {
        rememberMe.value = true;
      }
    } catch (e) {
      debugPrint('Error loading saved credentials: $e');
    }
  }

  /// Toggle remember me preference
  toggleRememberMe() async {
    rememberMe.value = !rememberMe.value;

    // If remember me is disabled, clear saved credentials
    if (!rememberMe.value) {
      await SecureStorageService.clearCredentialsOnly();
    }
  }

  emptValidator() {
    if (usernameTextController.text == '' ||
        passwordTextController.text == '') {
      isButtonAvailable.value = false;
    } else {
      isButtonAvailable.value = true;
    }
  }

  perform() async {
    if (!kIsWeb && !networkController.isConnected.value) {
      Utils.popup(
        body:
            '${'title_disconected_str'.tr}. ${'subtitle_internet_disconnected_str'.tr}',
        type: kPopupFailed,
      );
      return;
    }

    // Track login attempt
    trackButtonClick(
      buttonName: 'login_button',
      buttonType: 'primary',
      section: 'authentication',
      additionalData: {
        'remember_me': rememberMe.value ? 'true' : 'false',
        'username_length': usernameTextController.text.length,
      },
    );

    isInvalid.value = false;
    String secretKey = EnvLoader.get("PRIVATE_KEY");
    debugPrint("secret $secretKey");
    // secretKey = base64Decode(secretKey).toString();
    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final username = Encryption(
      secretKey,
    ).doencrypt(usernameTextController.text, "SALT1234");
    final password = Encryption(
      secretKey,
    ).doencrypt(passwordTextController.text, "SALT1234");
    setLoading(true);
    // store username/password encrypted to current state
    encyrptedUsername = username;
    encryptedPassword = password;

    await api.performLogin(
      controllers: this,
      data: {
        "password": password,
        "username": username,
        'rememberMe': rememberMe.value,
      },
      code: kReqLogin,
    );
  }

  void _getEncryptedLoginData() {
    encyrptedUsername = prefs.getString(kStorageUsername) ?? '';
    encryptedPassword = prefs.getString(kStoragePassword) ?? '';
  }

  performLoginBio() async {
    setLoading(true);
    await api.performLogin(
      controllers: this,
      data: {"password": encryptedPassword, "username": encyrptedUsername},
      code: kReqLogin,
    );
  }
}
