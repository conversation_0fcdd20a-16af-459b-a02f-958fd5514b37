import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';

class ForogotPasswordController extends BaseControllers {
  TextEditingController agentCodeTextController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  RxBool isBtnAvailabel = false.obs;
  RxBool isInvalid = false.obs;
  RxBool isEmailInvalid = false.obs;
  RxString errorText = ''.obs;
  RxString emailErrorText = ''.obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    Get.offNamed(
      Routes.FORGOT_PASSWORD_SENT,
      arguments: {
        'email': emailTextController.text,
        'email_encrypted': response['email'],
        'agentCode': agentCodeTextController.text,
      },
    );
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    isInvalid.value = true;
    // Clear email validation error when showing API error
    isEmailInvalid.value = false;
    emailErrorText.value = '';
    errorText.value = 'error_agent_email_not_synced_str'.tr;
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);

    setLoading(false);
    isInvalid.value = true;
    // Clear email validation error when showing API error
    isEmailInvalid.value = false;
    emailErrorText.value = '';
    errorText.value = 'error_agent_email_not_synced_str'.tr;
  }

  /// Validates email format using regex
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validates email and updates error state
  void _validateEmail() {
    final email = emailTextController.text.trim();

    if (email.isEmpty) {
      isEmailInvalid.value = false;
      emailErrorText.value = '';
    } else if (!_isValidEmail(email)) {
      isEmailInvalid.value = true;
      emailErrorText.value = 'error_email_not_valid_str'.tr;
    } else {
      isEmailInvalid.value = false;
      emailErrorText.value = '';
    }
  }

  onChangeText() {
    // Clear general error when user starts typing
    if (isInvalid.value) {
      isInvalid.value = false;
      errorText.value = '';
    }

    // Validate email format
    _validateEmail();

    // Check if button should be enabled
    final isAgentCodeValid = agentCodeTextController.text.isNotEmpty;
    final isEmailValid =
        emailTextController.text.isNotEmpty && !isEmailInvalid.value;

    isBtnAvailabel.value = isAgentCodeValid && isEmailValid;
  }

  onTapNext() {
    performNext();
  }

  performNext() async {
    // Reset all error states
    isInvalid.value = false;
    errorText.value = '';

    // Validate email format before proceeding
    _validateEmail();
    if (isEmailInvalid.value) {
      return; // Don't proceed if email is invalid
    }

    String secretKey = EnvLoader.get("PRIVATE_KEY");

    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final username = Encryption(
      secretKey,
    ).doencrypt(agentCodeTextController.text, agentCodeTextController.text);

    final email = Encryption(
      secretKey,
    ).doencrypt(emailTextController.text, emailTextController.text);

    var data = {"username": username, "email": email};
    setLoading(true);
    await api.performForgotPassword(controllers: this, data: data);
  }
}
