import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';

class ResetPasswordController extends BaseControllers {
  String token = '';

  TextEditingController newPasswordTextController = TextEditingController();
  TextEditingController confirmPasswordTextController = TextEditingController();

  RxBool is8Char = false.obs;
  RxBool isValidChar = false.obs;
  RxBool isConfirmSame = true.obs;
  RxBool isSaveAvailable = false.obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    Get.offNamed(Routes.CHANGE_PASSWORD_SUCCESS);
  }

  passwordCombinationValidator(String value) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';
    RegExp regex = RegExp(pattern);

    if (value.length > 7) {
      is8Char.value = true;
    } else {
      is8Char.value = false;
    }

    if (regex.hasMatch(value)) {
      isValidChar.value = true;
    } else {
      isValidChar.value = false;
    }
  }

  onChangeConfirm(val) {
    if (val == newPasswordTextController.text) {
      isConfirmSame.value = true;
    }
  }

  emptValidator() {
    if (newPasswordTextController.text == '' ||
        confirmPasswordTextController.text == '') {
      isSaveAvailable.value = false;
    } else {
      isSaveAvailable.value = true;
    }
  }

  performSave() async {
    if (newPasswordTextController.text != confirmPasswordTextController.text) {
      isConfirmSame.value = false;
      return;
    }

    String secretKey = EnvLoader.get("PRIVATE_KEY");
    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final newPassword = Encryption(
      secretKey,
    ).doencrypt(newPasswordTextController.text, newPasswordTextController.text);

    final confirmPassword = Encryption(secretKey).doencrypt(
      confirmPasswordTextController.text,
      confirmPasswordTextController.text,
    );
    var data = {"password": newPassword, "passwordConfirm": confirmPassword};
    await api.performResetPassword(controllers: this, data: data, token: token);
  }
}
