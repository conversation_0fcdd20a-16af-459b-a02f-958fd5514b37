// ignore_for_file: public_member_api_docs, sort_constructors_first

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class NetworkController extends GetxController {
  final RxBool isConnected = true.obs;
  late StreamSubscription _subscription;

  // ignore: prefer_typing_uninitialized_variables
  var connectionChecker;

  // State management untuk dialog
  final RxBool _hasShownDisconnectedDialog = false.obs;
  // ignore: unused_field
  bool _wasConnectedBefore = true;

  @override
  Future<void> onInit() async {
    super.onInit();

    if (!kIsWeb) {
      connectionChecker = InternetConnectionChecker.createInstance();

      // Cek status koneksi awal
      try {
        bool initialStatus = await connectionChecker.hasConnection;
        isConnected.value = initialStatus;
        _wasConnectedBefore = initialStatus;
        // print('Initial connection status: $initialStatus');
      } catch (e) {
        // print('Error checking initial connection: $e');
        isConnected.value = false;
        _wasConnectedBefore = false;
      }

      // Setup listener untuk perubahan status
      _subscription = connectionChecker.onStatusChange.listen((
        InternetConnectionStatus status,
      ) {
        bool newConnectionStatus = status == InternetConnectionStatus.connected;
        // print(
        //   'Connection status changed: ${isConnected.value} -> $newConnectionStatus',
        // );

        // Handle disconnection - show dialog
        if (isConnected.value && !newConnectionStatus) {
          // print('Showing disconnection dialog');
          _showDisconnectionDialog();
        }

        // Handle reconnection - show success notification and dismiss dialog
        if (!isConnected.value && newConnectionStatus) {
          // print('Connection restored');
          _dismissDisconnectionDialog();
          _showConnectionRestoredNotification();
        }

        isConnected.value = newConnectionStatus;
      });
    } else {
      isConnected.value = true;
    }
  }

  @override
  void onClose() {
    if (!kIsWeb) {
      _subscription.cancel();
    }
    super.onClose();
  }

  void retry() async {
    if (!kIsWeb && connectionChecker != null) {
      try {
        bool newStatus = await connectionChecker.hasConnection;
        // print('Retry connection check: $newStatus');

        // Update status dan trigger listener logic jika berubah
        if (newStatus == true) {
          // Simulate status change untuk trigger listener logic
          // if (!isConnected.value && newStatus) {
          // Reconnection
          _dismissDisconnectionDialog();
          _showConnectionRestoredNotification();
          // }
          isConnected.value = newStatus;
        }
      } catch (e) {
        // print('Error during retry: $e');
      }
    }
  }

  // /// Method untuk testing - simulasi disconnect
  // void simulateDisconnect() {
  //   print('Simulating disconnect');
  //   isConnected.value = false;
  //   _showDisconnectionDialog();
  // }

  /// Method untuk testing - simulasi reconnect
  // void simulateReconnect() {
  //   print('Simulating reconnect');
  //   isConnected.value = true;
  //   _showConnectionRestoredNotification();
  //   _dismissDisconnectionDialog();
  // }

  /// Debug method untuk cek status
  void debugStatus() {
    // print('=== NetworkController Debug Status ===');
    // print('isConnected: ${isConnected.value}');
    // print('hasShownDisconnectedDialog: ${_hasShownDisconnectedDialog.value}');
    // print('connectionChecker: $connectionChecker');
    // print('Get.context: ${Get.context}');
    // print('Get.isBottomSheetOpen: ${Get.isBottomSheetOpen}');
    // print('=====================================');
  }

  /// Cek apakah dialog disconnect sudah pernah ditampilkan dalam sesi ini
  bool get hasShownDisconnectedDialog => _hasShownDisconnectedDialog.value;

  /// Tandai bahwa dialog disconnect sudah ditampilkan
  void markDisconnectedDialogShown({bool? isShown}) {
    _hasShownDisconnectedDialog.value = isShown ?? true;
  }

  /// Reset state dialog ketika home di-refresh
  void resetDialogState() {
    _hasShownDisconnectedDialog.value = false;
  }

  /// Tampilkan dialog disconnection secara global
  void _showDisconnectionDialog() {
    // print('_showDisconnectionDialog called');

    // Cek apakah dialog sudah pernah ditampilkan dalam sesi ini
    if (_hasShownDisconnectedDialog.value) {
      // print('Dialog already shown, skipping');
      return;
    }

    if (Get.isBottomSheetOpen ?? false) {
      // print('Bottom sheet already open, skipping');
      return;
    }

    // tampilkan
    Utils().showNoConnectionDialog(this);

    // print('Marking dialog as shown and displaying');
    // Tandai bahwa dialog sudah ditampilkan
    markDisconnectedDialogShown();
  }

  /// Tutup dialog disconnection jika sedang terbuka
  void _dismissDisconnectionDialog() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }

  /// Tampilkan notifikasi ketika koneksi kembali
  void _showConnectionRestoredNotification() {
    markDisconnectedDialogShown(isShown: false);
    Utils.popup(
      body: 'Koneksi internet telah kembali normal',
      type: kPopupSuccess,
    );
  }
}
