// ignore_for_file: unused_field

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:local_auth/local_auth.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/models/tutor_model.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:url_launcher/url_launcher.dart';

class ProfileController extends BaseControllers {
  Rx<UserModels> userData = UserModels().obs;
  RxBool isBioActive = false.obs;

  final LocalAuthentication auth = LocalAuthentication();
  late SharedPreferences prefs;

  RxString userLevel = ''.obs;
  RxString userType = ''.obs;

  RxString fullVersion = ''.obs;

  // declare global key for tutorial use
  final GlobalKey _keyTutorPrivateData = GlobalKey();
  final GlobalKey _keyTutorQr = GlobalKey();
  final GlobalKey _keyTutorLicense = GlobalKey();
  final GlobalKey _keyTutorAccountMenu = GlobalKey();
  final GlobalKey _keyTutorAgentMenu = GlobalKey();
  final GlobalKey _keyTutorAppMenu = GlobalKey();

  List<TutorModel> tutorProfile = [];

  RxBool isShowAgentMenu = true.obs;

  RxInt totalLicense = 0.obs;
  RxInt allLisence = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    _getArguments();

    _assignTutor();
    prefs = await SharedPreferences.getInstance();
    Future.delayed(Duration(seconds: 1)).then((val) async {
      await api.getProfile(controllers: this);
      getBiometricStatus();
    });
    fullVersion.value = await Utils.getFullVersionApp();
  }

  void _assignTutor() {
    tutorProfile = [
      TutorModel(
        number: 1,
        key: _keyTutorPrivateData,
        title: 'tutor_personal_info'.tr,
        description: 'desc_personal_info'.tr,
      ),
      TutorModel(
        number: 2,
        key: _keyTutorQr,
        title: 'qr_code_str'.tr,
        description: 'tutor_qr_code_str'.tr,
      ),
      if (isShowAgentMenu.value)
        TutorModel(
          number: 3,
          key: _keyTutorLicense,
          title: 'label_lisence'.tr,
          description: 'tutor_license_str'.tr,
        ),
      TutorModel(
        number: (isShowAgentMenu.value) ? 4 : 3,
        key: _keyTutorAccountMenu,
        title: 'account_setting_str'.tr,
        description: 'tutor_desc_account_setting_str'.tr,
      ),
      if (isShowAgentMenu.value)
        TutorModel(
          number: 5,
          key: _keyTutorAgentMenu,
          title: 'label_download_pdf'.tr,
          description: 'tutor_desc_download_pdf_agent_str'.tr,
        ),
      TutorModel(
        number: isShowAgentMenu.value ? 6 : 5,
        key: _keyTutorAppMenu,
        title: 'label_app_setting'.tr,
        description: 'tutor_desc_app_setting_str'.tr,
      ),
    ];
  }

  getBiometricStatus() async {
    bool isActive = prefs.getBool(kStorageIsBiometricActive) ?? false;
    isBioActive.value = isActive;
  }

  setBiometric() async {
    final bool canAuthenticateWithBiometrics = await auth.canCheckBiometrics;
    final bool canAuthenticate =
        canAuthenticateWithBiometrics || await auth.isDeviceSupported();

    List bioList = await auth.getAvailableBiometrics();

    if (canAuthenticate && bioList.isNotEmpty) {
      isBioActive.value = !isBioActive.value;
    } else {
      isBioActive.value = false;
      Get.snackbar(
        'title_bio_failed'.tr,
        'subtitle_bio_failed'.tr,
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
    Utils.setBiometric();
  }

  @override
  void load() async {
    super.load();
    setLoading(true);
    await api.getProfile(controllers: this, debug: true);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    if (requestCode == kReqPerformLogout) {
      Get.back();
      Utils.setLoggedOut();
      return;
    }
    if (requestCode == kReqGenerateContractPdf) {
      Get.back(); // Close loading dialog
      _handleContractPdfResponse(response);
      return;
    }
    parseData(response);
  }

  @override
  loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    switch (requestCode) {
      case kReqPerformLogout:
        Get.back();
        Get.snackbar(
          'Error',
          response.body,
          colorText: Colors.white,
          backgroundColor: Colors.red,
          snackPosition: SnackPosition.BOTTOM,
        );
        break;
      case kReqGenerateContractPdf:
        Get.back(); // Close loading dialog
        Get.snackbar(
          'Error',
          'Gagal mengunduh dokumen kontrak',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
        break;
    }
  }

  String get dataQrCode {
    return 'Kode Agent : ${userData.value.agentCode}\nNama Agent : ${userData.value.agentName}\nKode Cabang : ${userData.value.branchCode}\nChannel : ${userData.value.channel}';
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      // set local storage
      if (prefs.getString(kStorageUserFirestoreId) == '') {
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      } else {
        await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
        await prefs.setString(
          kStorageAgentCode,
          data.agentCode != null ? data.agentCode! : data.username!,
        );
        await prefs.setString(
          kStorageAgentName,
          data.name ?? data.agentName ?? '-',
        );
        await prefs.setString(
          kStorageUserFirestoreId,
          'agents-${data.agentCode}-${data.id}',
        );
      }
      // set currentUserLevel
      await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
      await prefs.setString(
        kStorageAgentCode,
        data.agentCode != null ? data.agentCode! : data.username!,
      );
      await prefs.setString(
        kStorageAgentName,
        data.name ?? data.agentName ?? '-',
      );
      await prefs.setString(kStorageAgentBranch, data.branchName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
      await prefs.setString(kStorageUserType, data.userType ?? '-');
      userType.value = data.userType ?? '-';

      totalLicense.value = 0;
      allLisence.value = 0;
      // count total aasi - hanya yang expired date lebih dari hari ini
      // Mendapatkan tanggal hari ini
      DateTime today = DateTime.now();

      // Mengecek dan menghitung jumlah lisensi AASI yang masih berlaku
      if (data.licenseNumberAASI != null &&
          DateTime.parse(
            data.licenseExpiredDateAASI ?? today.toString(),
          ).isAfter(today)) {
        totalLicense.value += 1;
      }

      // Mengecek dan menghitung jumlah lisensi AAJI yang masih berlaku
      if (data.licenseNumberAAJI != null &&
          DateTime.parse(
            data.licenseExpiredDateAAJI ?? today.toString(),
          ).isAfter(today)) {
        totalLicense.value += 1;
      }
      if (data.channel == kUserChannelBan) {
        allLisence.value = 1;
      } else {
        allLisence.value = 2;
      }
      userLevel.value = data.roles?.code ?? '-';

      isShowAgentMenu.value =
          (!(data.roles?.code ?? '').inList([
            kUserLevelBdm,
            kUserLevelABDD,
            kUserLevelBDD,
            kUserLevelHOS,
            kUserLevelCAO,
            kUserLevelAgeBranch,
            kUserLevelBanRSM,
            kUserLevelBanBS,
            kUserLevelBanEBO,
            kUserLevelBanHOB,
            kUserLevelBanHOR,
            kUserLevelBanHOS,
            kUserLevelBanSASM,
            kUserLevelBanSBO,
            kUserLevelBanSEBO,
            kUserLevelBanSR,
            kUserLevelBanBTR,
            '-',
          ]));
      FirestoreServices().getInitialValue();
    } catch (e) {
      // print('here err $e');
    }
  }

  requestLogout(context) {
    PdlBaseDialog(
      context: context,
      child: PdlDialogContent(
        message: 'message_logout_str'.tr,
        onTap: () {
          Get.back();
          performLoggedOut();
        },
      ),
    );
  }

  performLoggedOut() async {
    Get.dialog(
      Container(
        padding: EdgeInsets.all(15),
        child: Center(child: CircularProgressIndicator()),
      ),
      barrierDismissible: false,
    );

    setLoading(true);
    await api.performLogout(controllers: this, code: kReqPerformLogout);
  }

  void _getArguments() {
    if (Get.arguments != null) {
      if (Get.arguments['level'] != null) {
        userLevel.value = Get.arguments['level'];
      }
      if (Get.arguments['start_tutorial'] == true &&
          Get.arguments['index'] == 3) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          _startTutorial();
        });
      }
    }
  }

  void _startTutorial() {
    if (showCaseContext != null) {
      final keys = tutorProfile.map((e) => e.key).toList();
      ShowCaseWidget.of(showCaseContext!).startShowCase(keys);
    }
  }

  // Download contract PDF
  void downloadContractPdf() async {
    // Show loading dialog
    Get.dialog(
      Container(
        padding: EdgeInsets.all(15),
        child: Center(child: CircularProgressIndicator()),
      ),
      barrierDismissible: false,
    );

    // Call API to generate contract documents
    await api.generateContractDocuments(
      controllers: this,
      code: kReqGenerateContractPdf,
    );
  }

  void _handleContractPdfResponse(dynamic response) {
    try {
      if (response != null && response['mergedDocumentUrl'] != null) {
        String pdfUrl = response['mergedDocumentUrl'];
        _downloadFile(pdfUrl);
      } else {
        Get.snackbar(
          'Error',
          'URL dokumen tidak ditemukan',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Gagal memproses response: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
  }

  Future<void> _downloadFile(String urlString) async {
    if (urlString.isEmpty) {
      Get.snackbar(
        'Error',
        'URL file tidak valid',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
      return;
    }

    final Uri? uri = Uri.tryParse(urlString);
    if (uri == null) {
      Get.snackbar(
        'Error',
        'URL file tidak valid',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
      return;
    }

    if (await canLaunchUrl(uri)) {
      try {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        Get.snackbar(
          'Berhasil',
          'Dokumen kontrak berhasil diunduh',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgGreen,
          colorText: kColorGlobalGreen,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Gagal mengunduh file: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } else {
      Get.snackbar(
        'Error',
        'Tidak dapat membuka URL. Pastikan Anda memiliki aplikasi untuk membuka file PDF.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
  }
}
