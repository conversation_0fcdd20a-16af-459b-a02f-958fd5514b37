import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/question_models.dart';

class QnaController extends BaseControllers {
  RxList<QuestionModels> arrData = RxList();
  TextEditingController searchTextController = TextEditingController();

  // pagination
  int page = 0;
  int maxPage = 0;
  int limit = 20;
  bool enableLoadMore = true;
  bool? nextPage;
  bool? prevPage;
  ScrollController scrollController = ScrollController();

  // filter
  String questionParams = '';

  @override
  void onInit() {
    super.onInit();
    load();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          loadMore();
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore) return;
    if (page != (maxPage - 1)) {
      String parameter = 'page=${page + 1}';
      if (questionParams != '') {
        parameter = 'question=$questionParams&page=${page + 1}';
      }
      await api.getQuestion(controllers: this, params: parameter);
    }
  }

  @override
  void load() async {
    super.load();
    setLoading(true);
    String parameter = 'page=$page';
    if (questionParams != '') {
      parameter = 'question=$questionParams&page=$page';
    }
    await api.getQuestion(controllers: this, params: parameter);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    questionParams = '';
    setLoading(false);
    enableLoadMore = response['last'] == false;
    parsePagination(response);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    questionParams = '';
    setLoading(false);
  }

  parsePagination(response) {
    if (response['last'] == false) {
      page = page + 1;
    }
    maxPage = response['totalPages'];
  }

  parseData(response) {
    if (response['first'] == true) {
      arrData.clear();
    }
    for (int i = 0; i < response['content'].length; i++) {
      QuestionModels data = QuestionModels.fromJson(response['content'][i]);

      arrData.add(data);
    }
  }
}
