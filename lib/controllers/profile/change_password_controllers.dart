import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:pdl_superapp/utils/env_loader.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/encryption_service.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ChangePasswordControllers extends BaseControllers {
  TextEditingController oldPasswordTextController = TextEditingController();
  TextEditingController newPasswordTextController = TextEditingController();
  TextEditingController confirmPasswordTextController = TextEditingController();

  RxBool is8Char = false.obs;
  RxBool isValidChar = false.obs;
  RxBool isSuccessValidation = true.obs;
  RxBool isSaveAvailable = false.obs;
  RxString errorMessage = ''.obs;

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Utils.popup(body: 'change_pass_popup_failed'.tr, type: kPopupFailed);
  }

  parseData(response) {
    Utils.setLoggedOut(noRedirect: true);
    Get.offAllNamed(Routes.CHANGE_PASSWORD_SUCCESS);
  }

  passwordCombinationValidator(String value) {
    String pattern =
        r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$';
    RegExp regex = RegExp(pattern);

    if (value.length > 7) {
      is8Char.value = true;
    } else {
      is8Char.value = false;
    }

    if (regex.hasMatch(value)) {
      isValidChar.value = true;
    } else {
      isValidChar.value = false;
    }
  }

  onChangeConfirm(val) {
    if (val == newPasswordTextController.text) {
      isSuccessValidation.value = true;
    }
  }

  emptValidator() {
    if (oldPasswordTextController.text == '' ||
        newPasswordTextController.text == '' ||
        confirmPasswordTextController.text == '') {
      isSaveAvailable.value = false;
    } else {
      isSaveAvailable.value = true;
    }
  }

  performSave() async {
    if (newPasswordTextController.text != confirmPasswordTextController.text) {
      isSuccessValidation.value = false;
      errorMessage.value = 'error_label_not_same'.tr;
      return;
    }
    if (newPasswordTextController.text == oldPasswordTextController.text) {
      isSuccessValidation.value = false;
      errorMessage.value = 'error_same_password'.tr;
      return;
    }
    if (!is8Char.value) {
      isSuccessValidation.value = false;
      errorMessage.value = 'length_8_char'.tr;
      return;
    }
    if (!isValidChar.value) {
      isSuccessValidation.value = false;
      errorMessage.value = 'valid_format'.tr;
      return;
    }

    errorMessage.value = '';
    isSuccessValidation.value = true;

    String secretKey = EnvLoader.get("PRIVATE_KEY");

    List<int> bytes = base64.decode(secretKey);
    String decodedStr = utf8.decode(bytes);

    secretKey = decodedStr;

    final oldPassword = Encryption(
      secretKey,
    ).doencrypt(oldPasswordTextController.text, oldPasswordTextController.text);

    final newPassword = Encryption(secretKey).doencrypt(
      confirmPasswordTextController.text,
      confirmPasswordTextController.text,
    );

    var data = {"oldPassword": oldPassword, "password": newPassword};
    setLoading(true);
    await api.performChangePassword(controllers: this, data: data);
  }
}
