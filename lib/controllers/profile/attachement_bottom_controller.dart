import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AttachementBottomController extends BaseControllers {
  String baseUrl = ConfigReader.getBaseUrl();

  RxString ktpUrl = ''.obs;
  RxString kkURl = ''.obs;
  RxString bankUrl = ''.obs;

  Rx<XFile?> ktpFile = Rx<XFile?>(null);
  Rx<XFile?> kkFile = Rx<XFile?>(null);
  Rx<XFile?> bankFile = Rx<XFile?>(null);

  RxDouble indicatorKtp = 0.0.obs;
  RxDouble indicatorKK = 0.0.obs;
  RxDouble indicatorBank = 0.0.obs;

  // Upload states for each file type
  RxBool isUploadingKtp = false.obs;
  RxBool isUploadingKK = false.obs;
  RxBool isUploadingBank = false.obs;

  // Submit button state
  RxBool isSubmitting = false.obs;

  // Error messages for file size validation
  RxString ktpErrorMessage = ''.obs;
  RxString kkErrorMessage = ''.obs;
  RxString bankErrorMessage = ''.obs;

  /// Check if a specific file type is uploaded and has URL
  bool isFileUploaded(String type) {
    switch (type) {
      case 'ktp':
        return ktpFile.value != null && ktpUrl.value.isNotEmpty;
      case 'kk':
        return kkFile.value != null && kkURl.value.isNotEmpty;
      case 'bank':
        return bankFile.value != null && bankUrl.value.isNotEmpty;
      default:
        return false;
    }
  }

  /// Check if all required files are uploaded
  bool areRequiredFilesUploaded({bool? isKtp, bool? isKK, bool? isBank}) {
    bool allUploaded = true;

    if (isKtp == true) {
      allUploaded = allUploaded && isFileUploaded('ktp');
    }
    if (isKK == true) {
      allUploaded = allUploaded && isFileUploaded('kk');
    }
    if (isBank == true) {
      allUploaded = allUploaded && isFileUploaded('bank');
    }

    return allUploaded;
  }

  /// Check if any file is currently uploading
  bool get isAnyFileUploading =>
      isUploadingKtp.value || isUploadingKK.value || isUploadingBank.value;

  /// Set uploading state for specific file type
  void _setUploadingState(String type, bool isUploading) {
    switch (type) {
      case 'ktp':
        isUploadingKtp.value = isUploading;
        break;
      case 'kk':
        isUploadingKK.value = isUploading;
        break;
      case 'bank':
        isUploadingBank.value = isUploading;
        break;
    }
  }

  /// Set error message for specific file type
  void setErrorMessage(String type, String message) {
    switch (type) {
      case 'ktp':
        ktpErrorMessage.value = message;
        break;
      case 'kk':
        kkErrorMessage.value = message;
        break;
      case 'bank':
        bankErrorMessage.value = message;
        break;
    }
  }

  /// Clear error message for specific file type
  void clearErrorMessage(String type) {
    setErrorMessage(type, '');
  }

  /// Get error message for specific file type
  String getErrorMessage(String type) {
    switch (type) {
      case 'ktp':
        return ktpErrorMessage.value;
      case 'kk':
        return kkErrorMessage.value;
      case 'bank':
        return bankErrorMessage.value;
      default:
        return '';
    }
  }

  @override
  void dispose() {
    super.dispose();
    dispose();
  }

  performUpdatePicture({required XFile image, required String type}) async {
    try {
      // Set uploading state for specific file type
      _setUploadingState(type, true);

      MultipartFile multipartFile;

      if (kIsWeb) {
        // On web, read the file as bytes
        final bytes = await image.readAsBytes();
        multipartFile = MultipartFile(bytes, filename: image.name);
      } else {
        // On mobile, use File object
        multipartFile = MultipartFile(File(image.path), filename: image.name);
      }

      final form = FormData({'file': multipartFile});
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String token = prefs.getString(kStorageToken) ?? '';
      final response = await GetConnect().post(
        '$baseUrl/profile/upload/document',
        form,
        headers: {'Authorization': 'Bearer $token'},
        uploadProgress: (percent) {
          switch (type) {
            case 'ktp':
              indicatorKtp.value = percent;
              break;
            case 'kk':
              indicatorKK.value = percent;
              break;
            case 'bank':
              indicatorBank.value = percent;
              break;
            default:
          }
          // You can update UI instead
        },
      );

      if (response.isOk) {
        // Set uploading state to false
        _setUploadingState(type, false);

        switch (type) {
          case 'ktp':
            ktpUrl.value = response.body['initialPreview'][0];
            break;
          case 'kk':
            kkURl.value = response.body['initialPreview'][0];
            break;
          case 'bank':
            bankUrl.value = response.body['initialPreview'][0];
            break;
          default:
        }
        // success
      } else {
        // Set uploading state to false on error
        _setUploadingState(type, false);

        Get.snackbar(
          'Failed',
          '${response.body['error_description'] ?? '-'}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: kColorGlobalBgRed,
          colorText: kColorErrorText,
        );
      }
    } catch (e) {
      // Set uploading state to false on error
      _setUploadingState(type, false);

      Get.snackbar(
        'Error',
        'Failed to upload file: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
      );
    }
  }
}
