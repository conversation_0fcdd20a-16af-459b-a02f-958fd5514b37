import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/question_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class QnaDetailController extends BaseControllers {
  Rx<QuestionModels> questionData = QuestionModels().obs;
  RxString questionId = ''.obs;

  @override
  void onInit() {
    super.onInit();
    questionId.value = Get.parameters['id'] ?? '';
    if (questionId.value.isNotEmpty) {
      load();
    }
  }

  @override
  void load() async {
    super.load();
    setLoading(true);
    await api.getQuestionDetail(controllers: this, id: questionId.value, code: kReqGetQuestionDetail);
  }

  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);
    setLoading(false);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Utils.popup(body: 'Gagal memuat detail pertanyaan. Please try again', type: kPopupFailed);
  }

  parseData(response) {
    questionData.value = QuestionModels.fromJson(response);
  }
}
