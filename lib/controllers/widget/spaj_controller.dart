import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SpajController extends BaseControllers {
  late SharedPreferences prefs;

  SpajController({String? idxSection}) {
    var index = int.tryParse(idxSection ?? "") ?? 0;
    selectedParam = index;
  }

  // Store the SPAJ data
  RxList<WidgetSpajModels> arrData = RxList<WidgetSpajModels>();

  // Store data for individu and team tabs
  RxList<WidgetSpajModels> arrDataIndividu = RxList<WidgetSpajModels>();
  RxList<WidgetSpajModels> arrDataTeam = RxList<WidgetSpajModels>();

  // Store the original data for search functionality
  RxList<WidgetSpajModels> originalData = RxList<WidgetSpajModels>();

  // Search text controller
  final TextEditingController searchController = TextEditingController();

  // Tab selection (0 for Individu, 1 for Team)
  int selectedParam = 0;
  String userLevel = '';
  String agentName = '';

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;
  String params = '';

  RxList filterValue = RxList();

  RxInt totalPages = 0.obs;
  RxInt currentPage = 0.obs;

  final q = Rxn<String>();

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel = prefs.getString(kStorageUserLevel) ?? '';
    agentName = prefs.getString(kStorageAgentName) ?? "";
    load();
  }

  // Update the displayed data based on selected tab
  void updateDisplayData() {
    if (selectedParam == 0) {
      arrData.assignAll(arrDataIndividu);
      originalData.assignAll(arrDataIndividu);
    } else {
      // If team data is empty but we're switching to team tab, try to load it
      if (arrDataTeam.isEmpty && userLevel != kLevelBP) {
        loadSpajTeam();
      }
      arrData.assignAll(arrDataTeam);
      originalData.assignAll(arrDataTeam);
    }
  }

  void loadSpajIndividu({bool isRefresh = false}) {
    if (isRefresh) {
      totalPages.value = 0;
      currentPage.value = 0;
      arrDataIndividu.clear();
      arrData.clear();
      originalData.clear();
    }
    api.getSpajIndividu(
      controllers: this,
      params: 'page=${currentPage.value}&size=10&$params',
      code: 1,
    );
    // for individu
  }

  void loadSpajTeam({bool isRefresh = false}) {
    if (isRefresh) {
      totalPages.value = 0;
      currentPage.value = 0;
      arrDataTeam.clear();
      arrData.clear();
      originalData.clear();
    }
    api.getSpajTeam(
      controllers: this,
      params: 'page=${currentPage.value}&size=10&$params',
      code: 2,
    ); // Request code 2 for team
  }

  @override
  void onClose() {
    // Dispose the controller to avoid memory leaks
    searchController.dispose();
    super.onClose();
  }

  @override
  void load({bool isRefresh = false}) {
    super.load();
    setLoading(true);
    if (selectedParam == 0) {
      loadSpajIndividu(isRefresh: isRefresh);
    } else {
      if (userLevel != kLevelBP) {
        loadSpajTeam(isRefresh: isRefresh);
      }
    }
  }

  void refreshData() {
    load(isRefresh: true);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Determine if this is an individu or team response based on the request code
    String type = 'individu';

    // If we're handling a team API response
    if (requestCode == 2) {
      type = 'team';
    }

    parseData(response, type: type);
    onSearch(searchController.text);
    setLoading(false);
  }

  parseData(response, {String type = 'individu'}) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <WidgetSpajModels>[];

      // Handle different response structures
      var data = [];

      if (response is List) {
        data.addAll(response);
      } else if (response is Map) {
        // Try to extract data from different possible response structures
        if (response.containsKey('content') && response['content'] is List) {
          data.addAll(response['content']);
        } else if (response.containsKey('data') && response['data'] is List) {
          data.addAll(response['data']);
        }

        if (response['totalPages'] != null) {
          totalPages.value =
              (response['totalPages'] ?? 1) -
              1; // dikurangi 1 karena current page nya dimulai dari 0, tapi total pages dimulai dari 1
        }
      }

      // Process the data
      for (var item in data) {
        try {
          WidgetSpajModels element = WidgetSpajModels.fromJson(item);
          tempList.add(element);
        } catch (e) {
          // Skip items that can't be parsed
        }
      }

      if (type == 'individu') {
        arrDataIndividu.addAll(tempList);
        if (selectedParam == 0 || userLevel != kLevelBM) {
          arrData.addAll(tempList);
          originalData.addAll(tempList);
        }
      } else {
        arrDataTeam.addAll(tempList);
        if (selectedParam == 1 && userLevel != kLevelBP) {
          arrData.addAll(tempList);
          originalData.addAll(tempList);
        }
      }

      hasError.value = false;
      errorMessage.value = '';
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = 'Failed to load SPAJ data';
    setLoading(false);
  }

  @override
  void loadError(e, {response}) {
    super.loadError(e, response: response);
    hasError.value = true;
    errorMessage.value = e.toString();
    setLoading(false);
  }

  onTapFilter(String spajStat) {
    if (filterValue.contains(spajStat)) {
      filterValue.remove(spajStat);
    } else {
      filterValue.add(spajStat);
    }
  }

  onTapClearFilter() {
    filterValue.clear();
    searchController.clear();
    params = '';
    refreshData();
    Get.back();
  }

  onTapTerapkan() {
    if (filterValue.isNotEmpty) {
      params =
          '${filterValue.map((element) => "status=$element").join("&")}&size=10';
      // Reset search text when applying filters
      refreshData();
    } else {
      params = '';
      refreshData();
    }
    Get.back();
  }

  // Search functionality
  void onSearch(String value) {
    if (value.length < 3) {
      // If search text is less than 3 characters, restore original data based on current tab
      updateDisplayData();
      return;
    }

    // Filter data based on search text and current tab
    List<WidgetSpajModels> dataToFilter = originalData;

    // Filter data based on search text
    List<WidgetSpajModels> filteredData =
        dataToFilter.where((item) {
          // Check if policyHolderName contains search text
          bool matchesPolicyHolder =
              item.policyHolderName?.toLowerCase().contains(
                value.toLowerCase(),
              ) ??
              false;
          // Check if spajNumber contains search text
          bool matchesSpajNumber =
              item.spajNumber?.toLowerCase().contains(value.toLowerCase()) ??
              false;

          return matchesPolicyHolder || matchesSpajNumber;
        }).toList();

    // Update the displayed data
    arrData.clear();
    arrData.addAll(filteredData);
  }
}
