import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiIndividuController extends BaseControllers {
  Rx<PersistensiModel?> persistensiData = Rx<PersistensiModel?>(null);
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  fetchPersistensiData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    prefs = await SharedPreferences.getInstance();
    final agentCode = prefs.getString(kStorageAgentCode);
    final currentYear = DateTime.now().year.toString();

    if (agentCode == null) {
      hasError.value = true;
      errorMessage.value = 'Agent code not found';
      setLoading(false);
      return;
    }

    try {
      String params = "agentCode=$agentCode&year=$currentYear&page=0&size=1";

      await api.getPersistencyIndividu(
        controllers: this,
        code: kReqGetPersistencyIndividu,
        params: params,
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    parseData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
    setLoading(false);
  }

  void parseData(response) {
    if (response == null) {
      hasError.value = true;
      errorMessage.value = 'No data available';
      return;
    }

    try {
      // Check if response has pagination structure
      if (response is Map<String, dynamic> && response.containsKey('content')) {
        // For individual data, we typically take the first item from content
        if (response['content'] != null && response['content']!.isNotEmpty) {
          persistensiData.value = PersistensiModel.fromJson(
            response['content'][0],
          );
        }
      } else {
        // Legacy response format (single object)
        PersistensiModel parsedData = PersistensiModel.fromJson(response);
        persistensiData.value = parsedData;
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error parsing data: $e';
    }
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(2)}%';
  }
}
