import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class PersistensiWidgetController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String userType = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = false.obs;

  // Filter properties
  RxString selectedView = 'Individu'.obs;
  RxList<String> selectedBranches = <String>[].obs;
  RxList<BranchModels> availableBranches = <BranchModels>[].obs;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiWidgetController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    userType = prefs.getString(kStorageUserType) ?? '';

    if (userType == 'STAFF') {
      selectedSection.value = 1;
    }

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'persistensi_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';
    _hasInitialized = true;

    // Load branches for filter if needed
    if (channel == kUserChannelBan && _shouldShowFilter()) {
      _loadBranches();
    }
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    selectedView.value = 'Individu';
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    selectedView.value = 'Team';
    refreshData();
  }

  void switchToArea() {
    selectedSection.value = 2;
    selectedView.value = 'Cabang';
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (selectedSection.value == 0) {
      individuController.fetchPersistensiData();
    } else if (selectedSection.value == 1) {
      teamController.fetchPersistensiData();
    } else if (selectedSection.value == 2) {
      areaController.fetchPersistensiData();
    }
    setLoading(false);
  }

  // Check if user should see filter (ASM and above for BAN channel)
  bool _shouldShowFilter() {
    if (channel != kUserChannelBan) return false;

    // BO users don't get filter, they get tabs
    return level != kUserLevelBanBO;
  }

  // Check if user should see tabs (BO users for BAN channel)
  bool shouldShowTabs() {
    if (channel != kUserChannelBan) return false;

    return level == kUserLevelBanBO;
  }

  // Get available views for filter based on user level
  List<String> getAvailableViews() {
    if (channel != kUserChannelBan) {
      // AGE channel - keep existing logic
      if (level == kLevelBP) return ['Individu'];
      return ['Individu', 'Team'];
    }

    // BAN channel
    if (level == kUserLevelBanBO) {
      return ['Individu', 'Team'];
    } else {
      // ASM and above
      return ['Individu', 'Team', 'Cabang'];
    }
  }

  // Load branches for filter
  void _loadBranches() {
    // Implementation will be added when we integrate with branch API
    // For now, just initialize empty list
    availableBranches.clear();
  }

  // Apply filter
  void applyFilter(String view, List<String> branches) {
    selectedView.value = view;
    selectedBranches.assignAll(branches);

    // Update selected section based on view
    switch (view) {
      case 'Individu':
        selectedSection.value = 0;
        break;
      case 'Team':
        selectedSection.value = 1;
        break;
      case 'Cabang':
        selectedSection.value = 2;
        break;
    }

    refreshData();
  }

  // Reset filter
  void resetFilter() {
    selectedView.value = 'Individu';
    selectedBranches.clear();
    selectedSection.value = 0;
    refreshData();
  }
}
