import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class PersistensiWidgetController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team
  String level = '';
  String agentCode = '';
  String userType = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = false.obs;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiWidgetController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    userType = prefs.getString(kStorageUserType) ?? '';

    if (userType == 'STAFF') {
      selectedSection.value = 1;
    }

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'persistensi_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    _hasInitialized = true;
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBP || selectedSection.value == 0) {
      individuController.fetchPersistensiData();
    } else {
      teamController.fetchPersistensiData();
    }
    setLoading(false);
  }
}
