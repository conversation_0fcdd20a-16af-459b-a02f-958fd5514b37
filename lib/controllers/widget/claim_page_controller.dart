import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/claim_tracking_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ClaimPageController extends BaseControllers {
  String level = '';
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Search and filter state
  RxString searchQuery = ''.obs;
  Rx<Map<String, dynamic>> activeFilters = Rx<Map<String, dynamic>>({});

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Loading states
  RxBool isLoadingMore = false.obs;

  // Scroll to top button visibility
  RxBool showScrollToTopButton = false.obs;

  // Pagination variables following the reference pattern
  int page = 0;
  int maxPage = 0;
  int limit = 10;
  bool enableLoadMore = true;
  bool? nextPage;
  bool? prevPage;
  ScrollController scrollController = ScrollController();

  TextEditingController searchTextController = TextEditingController();

  // Parameters
  final String? agentCode;
  String userLevel = "";
  final int withDownline;

  ClaimPageController({this.agentCode, required this.withDownline});

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    setupScrollListener();
    fetchClaimTrackingData();

    // Set up listeners for search and filter changes
    ever(searchQuery, (value) {
      if (value.trim().isEmpty) {
        searchTextController.text = "";
      }
    });
  }

  @override
  void onClose() {
    scrollController.dispose();
    searchTextController.dispose();
    super.onClose();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      // Show/hide scroll to top button based on scroll position
      final scrollOffset = scrollController.position.pixels;
      final showButton = scrollOffset > 200; // Show after scrolling 200px

      if (showScrollToTopButton.value != showButton) {
        showScrollToTopButton.value = showButton;
      }

      // Handle pagination at bottom
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          loadMore();
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore) {
      return;
    }

    if (isLoadingMore.value) {
      return; // Prevent multiple simultaneous calls
    }

    fetchClaimTrackingData(isLoadMore: true);
  }

  String setupParams() {
    String params = '';
    params = 'page=$page&size=$limit';
    return params;
  }

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData({
    bool isRefresh = false,
    bool isLoadMore = false,
  }) async {
    if (isLoadMore) {
      isLoadingMore.value = true;
    } else {
      setLoading(true);
    }

    hasError.value = false;
    errorMessage.value = '';

    if (isRefresh) {
      claimTrackingList.clear();
      page = 0;
      enableLoadMore = true;
    }

    try {
      userLevel = prefs.getString(kStorageUserLevel) ?? '';
      final String codeToUse =
          agentCode ?? prefs.getString(kStorageAgentCode) ?? '';

      if (codeToUse.isNotEmpty) {
        String params = setupParams();
        params += "&agent_code=$codeToUse";

        int downline = withDownline;
        activeFilters.value.forEach((key, value) {
          if (key == 'status') {
            List<String> values = value;
            params += "&statusGroup=";
            for (var element in values) {
              params += "${element.toString().toUpperCase()},";
            }
            params = params.substring(0, params.length - 1);
          } else if (key == 'view') {
            downline = value.toString().toLowerCase() == 'individu' ? 0 : 1;
          }
        });
        params += "&withDownline=$downline";

        // Add search parameters if query exists - using original search logic
        if (searchQuery.isNotEmpty) {
          // Original search was client-side, but now we'll implement server-side search
          // You may need to adjust this based on your API's search parameter
          params += "&search=${Uri.encodeComponent(searchQuery.value)}";
        }

        await api.getStatusClaim(controllers: this, params: params);
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        if (isLoadMore) {
          isLoadingMore.value = false;
        } else {
          setLoading(false);
        }
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      if (isLoadMore) {
        isLoadingMore.value = false;
      } else {
        setLoading(false);
      }
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == 0) {
      parseClaimTrackingData(response);
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load claim tracking data';
    setLoading(false);
    isLoadingMore.value = false;
  }

  parsePagination(response) {
    // Update enableLoadMore based on whether this is the last page
    enableLoadMore = response['last'] == false;

    // Only increment page if this is not the last page
    if (response['last'] == false) {
      page = page + 1;
    }

    maxPage = response['totalPages'] ?? 0;
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      // Handle pagination response structure
      if (response is Map<String, dynamic> && response.containsKey('content')) {
        parsePagination(response);

        if (response['first'] == true) {
          claimTrackingList.clear();
        }

        // Add new items to the list
        final tempList = <ClaimTrackingModel>[];
        if (response['content'] is List) {
          for (var item in response['content']) {
            tempList.add(ClaimTrackingModel.fromJson(item));
          }
        }
        claimTrackingList.addAll(tempList);
      } else if (response is List) {
        // Handle legacy list response

        claimTrackingList.clear();
        for (var item in response) {
          claimTrackingList.add(ClaimTrackingModel.fromJson(item));
        }
        // For legacy response, disable load more
        enableLoadMore = false;
      }

      setLoading(false);
      isLoadingMore.value = false;
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
      isLoadingMore.value = false;
    }
  }

  // Apply filters (triggers server-side search)
  void applyFilters() {
    fetchClaimTrackingData(isRefresh: true);
  }

  // Reset all filters
  void resetFilters() {
    searchQuery.value = '';
    activeFilters.value = {};
    // Don't automatically refresh data here to avoid unexpected API calls
    // The UI will call refreshData() when needed
  }

  // Refresh data
  Future<void> refreshData() async {
    page = 0;
    enableLoadMore = true;
    await fetchClaimTrackingData(isRefresh: true);
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }

  // Scroll to top method
  void scrollToTop() {
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }
}
