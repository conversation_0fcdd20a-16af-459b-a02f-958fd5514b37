import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/polis_lapse_team_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class PolisLapseController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  bool isShowOtherAgent = false;
  final String? agentCode;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers for different tabs
  late PolisLapseIndividuController individuController = Get.put(
    PolisLapseIndividuController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  late PolisLapseTeamController teamController = Get.put(
    PolisLapseTeamController(agentCode: agentCode),
    tag: Utils.getRandomString(),
  );

  PolisLapseController({this.isShowOtherAgent = false, this.agentCode}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'polis_lapse_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    _hasInitialized = true;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBP || selectedSection.value == 0) {
      // Individual view
      individuController.fetchPolicyLapsedData();
    } else {
      // Team/Group view
      teamController.fetchPolicyLapsedData();
    }
    setLoading(false);
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }
}
