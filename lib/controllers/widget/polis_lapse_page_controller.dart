import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/models/response/policy_lapsed_response.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

class PolisLapsePageController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  final String? agentCode;

  // For search and filter
  final RxString searchQuery = ''.obs;
  final RxString searchMode =
      'policyHolderName'.obs; // Default search mode: Name
  final Rx<Map<String, dynamic>> activeFilters = Rx<Map<String, dynamic>>({});

  // Store the policy lapsed data
  RxList<PolicyLapsedModel> policyLapsedList = <PolicyLapsedModel>[].obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Loading states
  RxBool isLoadingMore = false.obs;

  // Scroll to top button visibility
  RxBool showScrollToTopButton = false.obs;

  // Pagination variables following the reference pattern
  int page = 0;
  int maxPage = 0;
  int limit = 10;
  bool enableLoadMore = true;
  bool? nextPage;
  bool? prevPage;
  ScrollController scrollController = ScrollController();

  TextEditingController searchTextController = TextEditingController();

  PolisLapsePageController({this.agentCode, required int defaultTab}) {
    selectedSection.value = defaultTab;
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    setupScrollListener();
    fetchPolicyLapsedData(isRefresh: true);
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      // Show/hide scroll to top button based on scroll position
      final scrollOffset = scrollController.position.pixels;
      final showButton = scrollOffset > 200; // Show after scrolling 200px

      if (showScrollToTopButton.value != showButton) {
        showScrollToTopButton.value = showButton;
      }

      // Handle pagination at bottom
      if (scrollController.position.atEdge) {
        if (scrollController.position.pixels == 0) {
          // You're at the top.
        } else {
          // You're at the bottom.
          loadMore();
        }
      }
    });
  }

  Future<void> loadMore() async {
    if (!enableLoadMore) return;
    if (isLoadingMore.value) return; // Prevent multiple simultaneous calls
    if (page != (maxPage)) {
      fetchPolicyLapsedData(isLoadMore: true);
    }
  }

  String setupParams() {
    String params = '';
    params = 'page=$page&size=$limit';
    return params;
  }

  // Fetch policy lapsed data from API
  Future<void> fetchPolicyLapsedData({
    bool isRefresh = false,
    bool isLoadMore = false,
  }) async {
    if (isLoadMore) {
      isLoadingMore.value = true;
    } else {
      setLoading(true);
    }

    hasError.value = false;
    errorMessage.value = '';

    if (isRefresh) {
      policyLapsedList.clear();
      page = 0;
      enableLoadMore = true;
    }

    try {
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        final withDownline = selectedSection.value == 1 ? 1 : 0;
        String params = setupParams();
        params +=
            "&agentCode=$code&valueStatus=LAPSE&withDownline=$withDownline";

        // Add search parameters if query exists
        if (searchQuery.isNotEmpty) {
          params +=
              "&${searchMode.value}=${Uri.encodeComponent(searchQuery.value)}";
        }

        // Add date range parameters if they exist
        if (activeFilters.value.containsKey('startDate') &&
            activeFilters.value.containsKey('endDate')) {
          final startDate = activeFilters.value['startDate'];
          final endDate = activeFilters.value['endDate'];
          params += "&startDate=$startDate&endDate=$endDate";
        }

        await api.getPolicyLapsed(controllers: this, params: params);
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        if (isLoadMore) {
          isLoadingMore.value = false;
        } else {
          setLoading(false);
        }
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      if (isLoadMore) {
        isLoadingMore.value = false;
      } else {
        setLoading(false);
      }
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parsePolicyLapsedData(response);
    setLoading(false);
    isLoadingMore.value = false;
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value = 'Failed to load policy lapsed data';
    setLoading(false);
    isLoadingMore.value = false;
  }

  parsePagination(response) {
    if (response['last'] == false) {
      page = page + 1;
    } else {
      enableLoadMore = response['last'] == false;
    }
    maxPage = response['totalPages'];
  }

  // Parse the policy lapsed response data
  void parsePolicyLapsedData(dynamic response) {
    try {
      parsePagination(response);

      final responseParsed = PolicyLapsedResponse.fromJson(response);

      if (response['first'] == true) {
        policyLapsedList.clear();
      }

      // Add new items to the list
      final tempList = <PolicyLapsedModel>[];
      tempList.addAll(responseParsed.content ?? []);
      policyLapsedList.addAll(tempList);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    // Preserve search and filter values when switching tabs
    fetchPolicyLapsedData(isRefresh: true);
  }

  void switchToTeam() {
    selectedSection.value = 1;
    // Preserve search and filter values when switching tabs
    fetchPolicyLapsedData(isRefresh: true);
  }

  // Set search mode
  void setSearchMode(String mode) {
    searchMode.value = mode;
    // Re-fetch data if there's a query
    if (searchQuery.isNotEmpty) {
      fetchPolicyLapsedData(isRefresh: true);
    }
  }

  // Refresh data
  Future<void> refreshData() async {
    page = 0;
    enableLoadMore = true;
    await fetchPolicyLapsedData(isRefresh: true);
  }

  // Reset all filters
  void resetFilters() {
    searchQuery.value = '';
    activeFilters.value = {};
    // Don't automatically refresh data here to avoid unexpected API calls
    // The UI will call refreshData() when needed
  }

  // Apply filters to the data
  void applyFilters() {
    // Fetch data with filters applied
    fetchPolicyLapsedData(isRefresh: true);
  }

  // Getter for filtered list - now just returns the API result
  List<PolicyLapsedModel> get filteredList {
    return policyLapsedList;
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  // Format currency
  String formatCurrency(double amount, String currency) {
    final formatter = NumberFormat.currency(symbol: currency, decimalDigits: 2);
    return formatter.format(amount);
  }

  // Get status display text
  String getDisplayStatus(String status) {
    switch (status) {
      case "ENDING":
        return "Akan Jatuh Tempo";
      case "LAPSE":
        return "Lapse";
      case "ACTIVE":
        return "Aktif";
      default:
        return status;
    }
  }

  // Scroll to top method
  void scrollToTop() {
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }
}
