import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/production/production_detail_widget_controller.dart';
import 'package:pdl_superapp/models/widget/production_graphic_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

/// Controller for production detail graphic widget
/// Similar to GraphicWidgetController but with agentCode parameter support
class ProductionDetailGraphicController extends BaseControllers {
  // Observable list to hold chart data
  final RxList<FlSpot> chartData = <FlSpot>[].obs;

  // Lists to store API data
  final RxList<ProductionGraphicModel> monthlyGraphicData = <ProductionGraphicModel>[].obs;
  final RxList<ProductionGraphicModel> yearlyGraphicData = <ProductionGraphicModel>[].obs;

  // Min and max values for Y axis
  final RxDouble minY = 0.0.obs;
  final RxDouble maxY = 0.0.obs;

  // Track current filter type
  final RxString currentFilterType = kSwitchMonthly.obs;

  // Chart color based on filter type
  final Rx<Color> chartColor = kColorGlobalBlue.obs;

  // Agent code for API calls
  final RxString agentCode = ''.obs;

  // User level for determining which data to use
  final RxString userLevel = ''.obs;

  // List of month names
  final List<String> months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // List of years (will be populated from API data)
  final RxList<int> years = <int>[].obs;

  @override
  void onInit() async {
    super.onInit();

    // Get user level from SharedPreferences
    await _loadUserLevel();

    // Get agentCode from route parameters
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null && args.containsKey('agentCode')) {
      agentCode.value = args['agentCode'] ?? '';
    }

    // Listen to filter type changes
    ever(currentFilterType, (value) {
      updateChartData();
      updateChartColor();
    });

    // Try to get the production detail controller to listen to period changes
    try {
      final productionDetailController = Get.find<ProductionDetailWidgetController>();
      ever(productionDetailController.selectedMonth, (value) {
        currentFilterType.value = value.toString();
      });

      // Initialize with current value
      currentFilterType.value = productionDetailController.selectedMonth.value;
    } catch (e) {
      debugPrint('ProductionDetailWidgetController not found: $e');
    }

    // Fetch data if agentCode is available
    if (agentCode.value.isNotEmpty) {
      fetchData();
    }
  }

  /// Load user level from SharedPreferences
  Future<void> _loadUserLevel() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
      debugPrint('User level loaded: ${userLevel.value}');
    } catch (e) {
      debugPrint('Error loading user level: $e');
      userLevel.value = '';
    }
  }

  /// Set filter type and update chart
  void setFilterType(String filterType) {
    if (currentFilterType.value != filterType) {
      currentFilterType.value = filterType;
    }
  }

  /// Fetch data from API with agentCode parameter
  void fetchData() async {
    if (agentCode.value.isEmpty) return;

    setLoading(true);

    try {
      // Fetch monthly data with agentCode
      await api.getProductionGraphicDataMonthWithAgent(
        controllers: this,
        agentCode: agentCode.value,
        code: kReqGetProductionGraphicMonth,
      );

      // Fetch yearly data with agentCode
      await api.getProductionGraphicDataYearWithAgent(
        controllers: this,
        agentCode: agentCode.value,
        code: kReqGetProductionGraphicYear,
      );
    } catch (e) {
      debugPrint('Error fetching graphic data: $e');
    } finally {
      setLoading(false);
    }

    // Update chart with fetched data
    updateChartData();
    updateChartColor();
  }

  /// Format currency values (in IDR)
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      // Billions
      return '${(value / 1000000000).toStringAsFixed(1)}B';
    } else if (value >= 1000000) {
      // Millions
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      // Thousands
      return '${(value / 1000).toStringAsFixed(1)}K';
    }
    return '${value.toInt()}';
  }

  /// Update chart data based on current filter type
  void updateChartData() {
    chartData.clear();

    // Determine which data to use based on filter type
    List<ProductionGraphicModel> sourceData =
        currentFilterType.value == kSwitchMonthly ? monthlyGraphicData : yearlyGraphicData;

    if (sourceData.isEmpty) {
      return;
    }

    double highestValue = 0;

    // For monthly data, we have 12 months (0-11)
    if (currentFilterType.value == kSwitchMonthly) {
      // Sort data by month
      sourceData.sort((a, b) => (a.month ?? 0).compareTo(b.month ?? 0));

      // Create a map to store data by month (1-12)
      Map<int, double> dataByMonth = {};

      // Process data based on user level
      for (var item in sourceData) {
        if (item.month != null && item.netApe != null) {
          double value = _getValueBasedOnUserLevel(item.netApe);

          // Store in map (month is 1-based, but we need 0-based for chart)
          dataByMonth[item.month! - 1] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }

      // Create chart data points for all months (0-11)
      for (int i = 0; i < 12; i++) {
        double value = dataByMonth[i] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
      }
    }
    // For yearly data, we have the last 5 years
    else {
      // Sort data by year
      sourceData.sort((a, b) => (a.year ?? 0).compareTo(b.year ?? 0));

      // Extract unique years
      years.clear();
      for (var item in sourceData) {
        if (item.year != null && !years.contains(item.year)) {
          years.add(item.year!);
        }
      }

      // Create a map to store data by year
      Map<int, double> dataByYear = {};

      // Process data based on user level
      for (var item in sourceData) {
        if (item.year != null && item.netApe != null) {
          double value = _getValueBasedOnUserLevel(item.netApe);

          // Store in map
          dataByYear[item.year!] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }

      // Create chart data points for all years
      for (int i = 0; i < years.length; i++) {
        int year = years[i];
        double value = dataByYear[year] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
      }
    }

    // Set min and max Y values for better visualization
    minY.value = 0;
    // Make sure we have a non-zero value for maxY to avoid division by zero errors
    maxY.value = highestValue > 0 ? highestValue * 1.2 : 1000; // Add 20% padding at the top or use default
  }

  /// Get the appropriate value based on user level
  /// BD uses group, BM uses team, BP uses individu
  double _getValueBasedOnUserLevel(NetApeGraphicModel? netApe) {
    if (netApe == null) return 0;

    switch (userLevel.value) {
      case kUserLevelBd:
        return (netApe.group ?? 0).toDouble();
      case kUserLevelBm:
        return (netApe.team ?? 0).toDouble();
      case kUserLevelBp:
        return (netApe.individu ?? 0).toDouble();
      default:
        // Default to individu for other user levels
        return (netApe.individu ?? 0).toDouble();
    }
  }

  /// Update chart color based on filter type
  void updateChartColor() {
    if (currentFilterType.value == kSwitchMonthly) {
      chartColor.value = kColorGlobalBlue;
    } else {
      chartColor.value = kColorGlobalGreen;
    }

    // Notify UI that filter has changed
    update();
  }

  /// Handle API response
  @override
  void loadSuccess({required int requestCode, required response, required int statusCode}) {
    super.loadSuccess(requestCode: requestCode, response: response, statusCode: statusCode);

    // Check if response is an object and has 'content' key, otherwise use response directly
    final data = response is Map && response.containsKey('content') ? response['content'] : response;

    // Parse data based on request code
    if (requestCode == kReqGetProductionGraphicMonth) {
      monthlyGraphicData.clear();

      for (var item in data) {
        monthlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    } else if (requestCode == kReqGetProductionGraphicYear) {
      yearlyGraphicData.clear();

      for (var item in data) {
        yearlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    }

    // Update chart data
    updateChartData();
    updateChartColor();
  }

  /// For compatibility with existing code
  RxList<FlSpot> get monthlyData => chartData;
}
