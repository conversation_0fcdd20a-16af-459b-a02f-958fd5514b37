import 'package:fbroadcast/fbroadcast.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/production/production_widget_controller.dart';
import 'package:pdl_superapp/models/widget/production_graphic_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class GraphicWidgetController extends BaseControllers {
  // Observable list to hold chart data
  final RxList<FlSpot> chartData = <FlSpot>[].obs;

  // Lists to store API data
  final RxList<ProductionGraphicModel> monthlyGraphicData =
      <ProductionGraphicModel>[].obs;
  final RxList<ProductionGraphicModel> yearlyGraphicData =
      <ProductionGraphicModel>[].obs;

  // Min and max values for Y axis
  final RxDouble minY = 0.0.obs;
  final RxDouble maxY = 0.0.obs;

  // Track current filter type
  final RxString currentFilterType = kSwitchMonthly.obs;

  // Track current team/individu selection
  final RxString currentTeamType = kSwitchProdIndividu.obs;

  // Chart color based on filter type
  final Rx<Color> chartColor = kColorGlobalBlue.obs;

  // List of month names
  final List<String> months = [
    'jan_3_char_str'.tr,
    'feb_3_char_str'.tr,
    'mar_3_char_str'.tr,
    'apr_3_char_str'.tr,
    'may_3_char_str'.tr,
    'jun_3_char_str'.tr,
    'jul_3_char_str'.tr,
    'aug_3_char_str'.tr,
    'sep_3_char_str'.tr,
    'oct_3_char_str'.tr,
    'nov_3_char_str'.tr,
    'dec_3_char_str'.tr,
  ];

  // List of years (will be populated from API data)
  final RxList<int> years = <int>[].obs;

  // Reference to the production controller to access filter values
  ProductionWidgetController? productionController;

  // Optional agent code for filtering data to specific agent
  final RxString agentCode = ''.obs;

  // Optional user level for determining data source
  // BD = group data, BM = team data, BP = individual data
  final RxString userLevel = ''.obs;
  final RxString userChannel = ''.obs;

  /// Initialize the controller with optional agentCode
  /// This method should be called after the controller is created
  void initializeWithAgentCode(String? agentCodeParam) {
    // Set agentCode if provided
    if (agentCodeParam != null && agentCodeParam.isNotEmpty) {
      agentCode.value = agentCodeParam;
    }

    // Try to get the production controller from Get (may not exist when used independently)
    try {
      productionController = Get.find<ProductionWidgetController>();

      // Listen to changes in the filter type only if production controller exists
      ever(productionController!.selectedMonth, (value) {
        currentFilterType.value = value.toString();
        updateChartData();
        updateChartColor();
      });

      ever(productionController!.selectedType, (value) {
        currentTeamType.value = value.toString();
        updateChartData();
        // Force broadcast the current selected graphic value after tab change
        _broadcastCurrentSelectedValue();
      });

      ever(productionController!.selectedGraphic, (value) {
        updateChartData();
      });

      // Initialize with current values
      currentFilterType.value = productionController!.selectedMonth.value;
      currentTeamType.value = productionController!.selectedType.value;
    } catch (e) {
      // Production controller not found, widget is being used independently
      // Use default values and don't listen to external changes
      productionController = null;
    }

    // Fetch data
    fetchData();
  }

  // Fetch data from API
  void fetchData() async {
    setLoading(true);

    try {
      // Check if agentCode is provided for agent-specific data
      if (agentCode.value.isNotEmpty) {
        // Fetch agent-specific data
        await api.getProductionGraphicDataMonthWithAgent(
          controllers: this,
          agentCode: agentCode.value,
          code: kReqGetProductionGraphicMonth,
        );

        await api.getProductionGraphicDataYearWithAgent(
          controllers: this,
          agentCode: agentCode.value,
          code: kReqGetProductionGraphicYear,
        );
      } else {
        // Fetch general data
        await api.getProductionGraphicDataMonth(
          controllers: this,
          code: kReqGetProductionGraphicMonth,
        );

        await api.getProductionGraphicDataYear(
          controllers: this,
          code: kReqGetProductionGraphicYear,
        );
      }
    } catch (e) {
      debugPrint('Error fetching graphic data: $e');
    } finally {
      setLoading(false);
    }

    // Update chart with fetched data
    updateChartData();
    updateChartColor();
  }

  // Format currency values (in IDR)
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      // Billions
      return '${(value / 1000000000).toStringAsFixed(1)}B';
    } else if (value >= 1000000) {
      // Millions
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      // Thousands
      return '${(value / 1000).toStringAsFixed(1)}K';
    }
    return '${value.toInt()}';
  }

  // Update chart data based on current filter type and team type
  void updateChartData() {
    chartData.clear();

    // Determine which data to use based on filter type
    List<ProductionGraphicModel> sourceData =
        currentFilterType.value == kSwitchMonthly
            ? monthlyGraphicData
            : yearlyGraphicData;

    if (sourceData.isEmpty) {
      // If no data yet, return
      return;
    }

    //Get totalNetApe current year
    final currentYear = DateTime.now().year;
    final currentMonth = DateTime.now().month;

    // Get user level from parameter, production controller, or default
    String currentUserLevel = '';
    if (userLevel.value.isNotEmpty) {
      // Use provided userLevel parameter
      currentUserLevel = userLevel.value;
    } else if (productionController != null) {
      // Use userLevel from production controller
      currentUserLevel = productionController!.userLevel;
    } else {
      // When used independently without userLevel, default to individual data
      currentUserLevel =
          kUserLevelBp; // Default to BP level for individual data
    }
    String currentUserChannel = 'AGE';
    if (userChannel.value.isNotEmpty) {
      // Use provided userChannel parameter
      currentUserChannel = userChannel.value;
    } else if (productionController != null) {
      // Use userChannel from production controller
      currentUserChannel = productionController!.userChannel;
    }

    double highestValue = 0;

    // For monthly data, we have 12 months (0-11)
    if (currentFilterType.value == kSwitchMonthly) {
      // Sort data by month
      sourceData.sort((a, b) => (a.month ?? 0).compareTo(b.month ?? 0));

      // Create a map to store data by month (1-12)
      Map<int, double> dataByMonth = {};

      // Process data based on team/individu selection
      for (var item in sourceData) {
        if (item.month != null && item.netApe != null) {
          double value = 0;

          // Get the appropriate value based on selected tab and user level
          value = _getValueBasedOnSelection(
            item.netApe,
            currentUserLevel,
            currentUserChannel,
          );

          // Store in map (month is 1-based, but we need 0-based for chart)
          dataByMonth[item.month! - 1] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }

      var broadcast =
          productionController?.selectedGraphic.value ?? currentMonth;
      debugPrint("broad $broadcast");
      // Create chart data points for all months (0-11)
      for (int i = 0; i < 12; i++) {
        double value = dataByMonth[i] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
        if (broadcast == i.toString()) {
          FBroadcast.instance().broadcast('total_net_ape', value: value);
        }
      }
    }
    // For yearly data, we have the last 5 years
    else {
      // Sort data by year
      sourceData.sort((a, b) => (a.year ?? 0).compareTo(b.year ?? 0));

      // Extract unique years
      years.clear();
      for (var item in sourceData) {
        if (item.year != null && !years.contains(item.year)) {
          years.add(item.year!);
        }
      }

      // Create a map to store data by year
      Map<int, double> dataByYear = {};

      // Process data based on team/individu selection
      for (var item in sourceData) {
        if (item.year != null && item.netApe != null) {
          double value = 0;

          // Get the appropriate value based on selected tab and user level
          value = _getValueBasedOnSelection(
            item.netApe,
            currentUserLevel,
            currentUserChannel,
          );

          // Debug logging for group yearly data
          if (currentTeamType.value == kSwitchProdGroup &&
              currentFilterType.value == kSwitchYearly) {
            debugPrint(
              'GraphicWidget Group Yearly - Year: ${item.year}, NetApe.group: ${item.netApe?.group}, Selected value: $value',
            );
          }

          // Store in map
          dataByYear[item.year!] = value;

          // Update highest value
          if (value > highestValue) {
            highestValue = value;
          }
        }
      }
      var broadcast =
          productionController?.selectedGraphic.value ?? currentYear;
      // Create chart data points for all years
      for (int i = 0; i < years.length; i++) {
        int year = years[i];
        double value = dataByYear[year] ?? 0;
        chartData.add(FlSpot(i.toDouble(), value));
        if (broadcast == year.toString()) {
          FBroadcast.instance().broadcast('total_net_ape', value: value);
        }
      }
    }

    // Set min and max Y values for better visualization
    minY.value = 0;
    // Make sure we have a non-zero value for maxY to avoid division by zero errors
    maxY.value =
        highestValue > 0
            ? highestValue * 1.2
            : 1000; // Add 20% padding at the top or use default
  }

  // Helper method to get the appropriate value based on selected tab and user level
  double _getValueBasedOnSelection(
    NetApeGraphicModel? netApe,
    String currentUserLevel,
    String currentUserChannel,
  ) {
    if (netApe == null) return 0;
    // BAN channel
    if (currentUserChannel == "BAN") {
      switch (currentTeamType.value) {
        case kSwitchProdGroup:
          return (netApe.group ?? 0).toDouble();
        case kSwitchProdBranch:
          return (netApe.branch ?? 0).toDouble();
        case kSwitchProdTeam:
          return (netApe.team ?? 0).toDouble();
        default:
          return (netApe.individu ?? 0).toDouble();
      }
    }

    // When used with agentCode (independently), use data based on user level
    if (agentCode.value.isNotEmpty) {
      // BD = group data, BM = team data, BP = individual data
      switch (currentUserLevel) {
        case kUserLevelBd:
          return (netApe.group ?? 0).toDouble();
        case kUserLevelBm:
          return (netApe.team ?? 0).toDouble();
        case kUserLevelBp:
        default:
          return (netApe.individu ?? 0).toDouble();
      }
    }

    // Check if it's a standard user level (BP, BM, BD)
    final isStandardUserLevel =
        currentUserLevel == kUserLevelBm ||
        currentUserLevel == kUserLevelBd ||
        currentUserLevel == kUserLevelBp;

    if (isStandardUserLevel) {
      // For standard user levels
      if (currentTeamType.value == kSwitchProdIndividu) {
        return (netApe.individu ?? 0).toDouble();
      } else {
        // For BD level, use group data instead of team data
        if (currentUserLevel == kUserLevelBd) {
          return (netApe.group ?? 0).toDouble();
        } else {
          return (netApe.team ?? 0).toDouble();
        }
      }
    } else {
      // For other user levels (BDM, CAO, etc.)
      switch (currentTeamType.value) {
        case kSwitchProdGroup:
          return (netApe.group ?? 0).toDouble();
        case kSwitchProdBranch:
          return (netApe.branch ?? 0).toDouble();
        case kSwitchProdArea:
          return (netApe.area ?? 0).toDouble();
        default:
          // Default to individu data if no specific type is selected
          return (netApe.individu ?? 0).toDouble();
      }
    }
  }

  // Update chart color based on filter type
  void updateChartColor() {
    if (currentFilterType.value == kSwitchMonthly) {
      chartColor.value = kColorGlobalBlue;
    } else {
      chartColor.value = kColorGlobalGreen;
    }

    // Notify UI that filter has changed
    update();
  }

  // Handle API response
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Check if response is an object and has 'content' key, otherwise use response directly
    final data =
        response is Map && response.containsKey('content')
            ? response['content']
            : response;

    // Parse data based on request code
    if (requestCode == kReqGetProductionGraphicMonth) {
      monthlyGraphicData.clear();

      for (var item in data) {
        monthlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    } else if (requestCode == kReqGetProductionGraphicYear) {
      yearlyGraphicData.clear();

      for (var item in data) {
        yearlyGraphicData.add(ProductionGraphicModel.fromJson(item));
      }
    }

    // Update chart data
    updateChartData();
    updateChartColor();

    // Broadcast current selected value after data update
    _broadcastCurrentSelectedValue();
  }

  /// Force broadcast the current selected graphic value
  /// This is used when tab changes to ensure NET APE total is updated correctly
  void _broadcastCurrentSelectedValue() {
    if (productionController == null || chartData.isEmpty) return;

    final selectedGraphic = productionController!.selectedGraphic.value;

    // Find the corresponding value in chartData and broadcast it
    if (currentFilterType.value == kSwitchMonthly) {
      final monthIndex = int.tryParse(selectedGraphic) ?? -1;
      if (monthIndex >= 0 && monthIndex < chartData.length) {
        final value = chartData[monthIndex].y;
        debugPrint('Broadcasting monthly value for month $monthIndex: $value');
        FBroadcast.instance().broadcast('total_net_ape', value: value);
      }
    } else {
      // For yearly data, find the year in the years list
      final selectedYear = int.tryParse(selectedGraphic) ?? -1;
      final yearIndex = years.indexOf(selectedYear);
      if (yearIndex >= 0 && yearIndex < chartData.length) {
        final value = chartData[yearIndex].y;
        debugPrint('Broadcasting yearly value for year $selectedYear: $value');
        FBroadcast.instance().broadcast('total_net_ape', value: value);
      }
    }
  }

  // For compatibility with existing code
  RxList<FlSpot> get monthlyData => chartData;
}
