import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/models/response/policy_lapsed_response.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PolisLapseIndividuController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the policy lapsed data
  RxList<PolicyLapsedModel> policyLapsedList = <PolicyLapsedModel>[].obs;

  // Error handling
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Optional agent code for viewing other agent's data
  final String? agentCode;

  PolisLapseIndividuController({this.agentCode});

  @override
  void onInit() {
    super.onInit();
    fetchPolicyLapsedData();
  }

  // Fetch policy lapsed data from API
  Future<void> fetchPolicyLapsedData() async {
    setLoading(true);
    hasError.value = false;
    errorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final code = agentCode ?? prefs.getString(kStorageAgentCode);

      if (code != null) {
        await api.getPolicyLapsed(
          controllers: this,
          params: "agentCode=$code&valueStatus=LAPSE&withDownline=0&size=3",
        );
      } else {
        hasError.value = true;
        errorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parsePolicyLapsedData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasError.value = true;
    errorMessage.value =
        response.statusText ?? 'Failed to load policy lapsed data';
    setLoading(false);
  }

  // Parse the policy lapsed response data
  void parsePolicyLapsedData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <PolicyLapsedModel>[];
      final responseParsed = PolicyLapsedResponse.fromJson(response);
      tempList.addAll(responseParsed.content ?? []);

      // Update the observable list all at once
      policyLapsedList.assignAll(tempList);
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
      setLoading(false);
    }
  }
}
