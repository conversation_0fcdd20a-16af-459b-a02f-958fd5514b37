import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiPageController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = true.obs;

  // Agent profile data for other agent view
  Rx<UserModels?> agentProfile = Rx<UserModels?>(null);
  RxBool isLoadingProfile = false.obs;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiPageController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      // Parse mode from URL parameters
      final mode = Get.parameters['mode'] ?? "0";
      selectedSection.value = int.tryParse(mode) ?? 1;
    }

    // Check if agentCode is provided in URL parameters
    final urlAgentCode = Get.parameters['agentCode'];
    if (urlAgentCode != null && urlAgentCode.isNotEmpty) {
      agentCode = urlAgentCode;
      isShowOtherAgent = true; // Force show other agent data
    }
  }

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    // Use agentCode from URL parameter if available, otherwise use stored agentCode
    final urlAgentCode = Get.parameters['agentCode'];
    if (urlAgentCode == null || urlAgentCode.isEmpty) {
      agentCode = prefs.getString(kStorageAgentCode) ?? '';
    }

    channel = prefs.getString(kStorageUserChannel) ?? '';

    setLoading(true);

    // Load agent profile if viewing other agent
    if (isShowOtherAgent && agentCode.isNotEmpty) {
      loadAgentProfile();
    }

    refreshData();
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  void switchToArea() {
    selectedSection.value = 2;
    refreshData();
  }

  // Load agent profile for other agent view
  Future<void> loadAgentProfile() async {
    if (!isShowOtherAgent || agentCode.isEmpty) return;

    isLoadingProfile.value = true;
    try {
      await api.getSimpleProfile(
        controllers: this,
        agentCode: agentCode,
        code: kReqGetProfile,
      );
    } catch (e) {
      isLoadingProfile.value = false;
    }
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (selectedSection.value == 0) {
      individuController.fetchPersistensiData();
    } else if (selectedSection.value == 1) {
      teamController.fetchPersistensiData();
    } else if (selectedSection.value == 2) {
      areaController.fetchPersistensiData();
    }
    setLoading(false);
  }

  // Get available tabs based on channel and role
  List<String> getAvailableTabs() {
    if (channel == kUserChannelBan) {
      // BAN channel
      if (level == kUserLevelBanBO) {
        return ['my_self_str'.tr, 'team_str'.tr];
      } else {
        // ASM and above
        return ['my_self_str'.tr, 'team_str'.tr, 'cabang_str'.tr];
      }
    } else {
      // AGE channel - existing logic
      if (level == kLevelBP) return ['my_self_str'.tr];
      return [
        'my_self_str'.tr,
        level == kLevelBD ? 'group_str'.tr : 'team_str'.tr,
      ];
    }
  }

  // Check if should show tabs (for page view)
  bool shouldShowTabs() {
    return getAvailableTabs().length > 1;
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetProfile) {
      if (response != null) {
        agentProfile.value = UserModels.fromJson(response);
      }
      isLoadingProfile.value = false;
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    if (requestCode == kReqGetProfile) {
      isLoadingProfile.value = false;
    }
  }

  // Get page title based on whether viewing other agent
  String getPageTitle() {
    if (isShowOtherAgent && agentProfile.value != null) {
      final profile = agentProfile.value!;
      return '${profile.agentLevel ?? ''} ${profile.name ?? ''}';
    }
    return 'persistency_str'.tr;
  }
}
