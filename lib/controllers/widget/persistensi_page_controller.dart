import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_individu_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_team_controller.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_area_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PersistensiPageController extends BaseControllers {
  final RxBool isExpanded = true.obs;
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team, 2 for Area
  String level = '';
  String agentCode = '';
  String channel = '';
  bool isShowOtherAgent = false;
  RxBool showFullData = true.obs;

  // Controllers
  late PersistensiIndividuController individuController = Get.put(
    PersistensiIndividuController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiTeamController teamController = Get.put(
    PersistensiTeamController(),
    tag: Utils.getRandomString(),
  );
  late PersistensiAreaController areaController = Get.put(
    PersistensiAreaController(),
    tag: Utils.getRandomString(),
  );
  late SharedPreferences prefs;

  PersistensiPageController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      // Parse mode from URL parameters
      final mode = Get.parameters['mode'] ?? "0";
      selectedSection.value = int.tryParse(mode) ?? 1;
    }
  }

  @override
  onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    agentCode = prefs.getString(kStorageAgentCode) ?? '';
    channel = prefs.getString(kStorageUserChannel) ?? '';

    setLoading(true);
    refreshData();
  }

  // Toggle accordion expanded state
  void toggleExpanded() {
    isExpanded.value = !isExpanded.value;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  void switchToArea() {
    selectedSection.value = 2;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (selectedSection.value == 0) {
      individuController.fetchPersistensiData();
    } else if (selectedSection.value == 1) {
      teamController.fetchPersistensiData();
    } else if (selectedSection.value == 2) {
      areaController.fetchPersistensiData();
    }
    setLoading(false);
  }

  // Get available tabs based on channel and role
  List<String> getAvailableTabs() {
    if (channel == kUserChannelBan) {
      // BAN channel
      if (level == kUserLevelBanBO) {
        return ['Individu', 'Team'];
      } else {
        // ASM and above
        return ['Individu', 'Team', 'Cabang'];
      }
    } else {
      // AGE channel - existing logic
      if (level == kLevelBP) return ['Individu'];
      return ['Individu', 'Team'];
    }
  }

  // Check if should show tabs (for page view)
  bool shouldShowTabs() {
    return getAvailableTabs().length > 1;
  }
}
