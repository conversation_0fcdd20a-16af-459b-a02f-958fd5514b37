import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class PersistensiAreaController extends BaseControllers {
  // Changed to store a list of persistence data for multiple areas
  RxList<PersistensiModel> persistensiDataList = <PersistensiModel>[].obs;
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  ScrollController scrollController = ScrollController();

  // Pagination properties
  int page = 0;
  int maxPage = 0;
  int limit = 10;
  bool enableLoadMore = true;
  RxBool isLoadingMore = false.obs;

  // Filter properties
  String? selectedBranchCode;

  @override
  void onInit() {
    super.onInit();
    setupScrollListener();
  }

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 200 &&
          !isLoadingMore.value &&
          enableLoadMore &&
          page < maxPage) {
        loadMoreData();
      }
    });
  }

  void loadMoreData() {
    if (page < maxPage && !isLoadingMore.value) {
      page++;
      isLoadingMore.value = true;
      fetchPersistensiData();
    }
  }

  fetchPersistensiData({String? branchCode}) async {
    if (page == 0) {
      setLoading(true);
      persistensiDataList.clear();
    }
    
    hasError.value = false;
    errorMessage.value = '';

    prefs = await SharedPreferences.getInstance();
    final agentCode = prefs.getString(kStorageAgentCode);
    final currentYear = DateTime.now().year.toString();

    if (agentCode == null) {
      hasError.value = true;
      errorMessage.value = 'Agent code not found';
      setLoading(false);
      isLoadingMore.value = false;
      return;
    }

    // Update selected branch code if provided
    if (branchCode != null) {
      selectedBranchCode = branchCode;
      page = 0; // Reset pagination when filtering
      persistensiDataList.clear();
      setLoading(true);
    }

    final currentYear = DateTime.now().year.toString();

    try {
      String params = "agentCode=$agentCode&year=$currentYear&page=$page&size=$limit";
      
      // Add branch code filter if selected
      if (selectedBranchCode != null && selectedBranchCode!.isNotEmpty) {
        params += "&branchCode=$selectedBranchCode";
      }

      await api.getPersistencyArea(
        controllers: this,
        code: kReqGetPersistencyArea,
        params: params,
      );
    } catch (e) {
      hasError.value = true;
      errorMessage.value = 'Error fetching data: $e';
      setLoading(false);
      isLoadingMore.value = false;
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Parse pagination info
    parsePagination(response);
    parseData(response);

    setLoading(false);
    isLoadingMore.value = false;
  }

  void parsePagination(response) {
    if (response['totalPages'] != null) {
      maxPage = response['totalPages'] - 1;
    }
    if (response['last'] != null) {
      enableLoadMore = !response['last'];
    }
  }

  void parseData(response) {
    if (response['content'] != null) {
      final List<dynamic> content = response['content'];
      final newData = content.map((item) => PersistensiModel.fromJson(item)).toList();
      
      if (page == 0) {
        persistensiDataList.assignAll(newData);
      } else {
        persistensiDataList.addAll(newData);
      }
    }
  }

  @override
  void loadFailed({
    required int requestCode,
    required String message,
    required int statusCode,
  }) {
    super.loadFailed(
      requestCode: requestCode,
      message: message,
      statusCode: statusCode,
    );
    hasError.value = true;
    errorMessage.value = message;
    setLoading(false);
    isLoadingMore.value = false;
  }

  String formatPercentage(double value) {
    return "${(value * 100).toStringAsFixed(2)}%";
  }

  void refreshData() {
    page = 0;
    enableLoadMore = true;
    fetchPersistensiData();
  }

  void clearFilter() {
    selectedBranchCode = null;
    refreshData();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
