import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/flyer_models.dart';

class HomeCarouselController extends BaseControllers {
  RxList<String> imageUrls = RxList();
  RxList<String> textUrls = RxList();
  RxList<FlyerModels> flyerData = RxList();

  // Page controller for managing the carousel
  late PageController pageController;
  RxDouble currentPage = 0.0.obs;
  Timer? autoplayTimer;
  int autoplayDuration = 5; // Duration in seconds

  // For infinite scrolling, we need the real count and the displayed count
  int get realItemCount => imageUrls.length;
  int get displayedItemCount =>
      10000; // Large number to simulate infinite scrolling
  int get initialPage =>
      (displayedItemCount ~/ 2); // Start from middle for "infinite" effect

  @override
  void onInit() {
    super.onInit();
    api.getFlyer(controllers: this);
    // Initialize the page controller with initial page and viewport fraction for softer transitions
    pageController = PageController(
      initialPage: initialPage,
      viewportFraction: 1.0, // Full screen width for each page
    );

    // Listen to page changes for smooth animation
    pageController.addListener(() {
      currentPage.value = pageController.page ?? 0.0;
    });

    // Start autoplay
    startAutoplay();
  }

  @override
  loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    parseData(response);
  }

  parseData(response) {
    imageUrls.clear();
    textUrls.clear();
    flyerData.clear();

    if (response == null || response.isEmpty) {
      return;
    }
    for (int i = 0; i < response['content'].length; i++) {
      FlyerModels data = FlyerModels.fromJson(response['content'][i]);
      imageUrls.add(data.bgImage ?? '');
      textUrls.add(data.frontImage ?? '');
      flyerData.add(data);
    }
  }

  // Method to start the autoplay timer
  void startAutoplay() {
    autoplayTimer?.cancel();
    autoplayTimer = Timer.periodic(Duration(seconds: autoplayDuration), (
      timer,
    ) {
      // Check if the controller is still attached to prevent errors
      if (pageController.hasClients) {
        int nextPage = (pageController.page!.round() + 1) % displayedItemCount;
        pageController.animateToPage(
          nextPage,
          duration: Duration(milliseconds: 800),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  // Method to pause autoplay (useful for when user is interacting)
  void pauseAutoplay() {
    autoplayTimer?.cancel();
  }

  // Method to resume autoplay
  void resumeAutoplay() {
    if (autoplayTimer == null || !autoplayTimer!.isActive) {
      startAutoplay();
    }
  }

  // Method to get the real index from displayed index (for infinite scrolling)
  int getRealIndex(int displayIndex) {
    if (imageUrls.isEmpty) {
      return -1;
    }
    return displayIndex % realItemCount;
  }

  // Method to get flyer data by real index
  FlyerModels? getFlyerData(int realIndex) {
    if (flyerData.isEmpty || realIndex < 0 || realIndex >= flyerData.length) {
      return null;
    }
    return flyerData[realIndex];
  }

  @override
  void onClose() {
    autoplayTimer?.cancel();
    pageController.dispose();
    super.onClose();
  }
}
