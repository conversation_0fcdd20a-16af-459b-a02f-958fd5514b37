import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/claim_tracking_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class ClaimIndividuController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Error handling
  RxBool hasClaimError = false.obs;
  RxString claimErrorMessage = ''.obs;

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData() async {
    setLoading(true);
    hasClaimError.value = false;
    claimErrorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        await api.getStatusClaim(
          controllers: this,
          params: "agent_code=$agentCode&withDownline=0",
        );
      } else {
        hasClaimError.value = true;
        claimErrorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parseClaimTrackingData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasClaimError.value = true;
    claimErrorMessage.value =
        response.statusText ?? 'Failed to load claim tracking data';
    setLoading(false);
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <ClaimTrackingModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(ClaimTrackingModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      claimTrackingList.assignAll(tempList);
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

class ClaimTeamController extends BaseControllers {
  late SharedPreferences prefs;

  // Store the claim tracking data
  RxList<ClaimTrackingModel> claimTrackingList = <ClaimTrackingModel>[].obs;

  // Error handling
  RxBool hasClaimError = false.obs;
  RxString claimErrorMessage = ''.obs;

  // Fetch claim tracking data from API
  Future<void> fetchClaimTrackingData() async {
    setLoading(true);
    hasClaimError.value = false;
    claimErrorMessage.value = '';

    try {
      prefs = await SharedPreferences.getInstance();
      final agentCode = prefs.getString(kStorageAgentCode);

      if (agentCode != null) {
        // Use the same endpoint but with withDownline=1 parameter
        await api.getStatusClaim(
          controllers: this,
          params: "agent_code=$agentCode&withDownline=1",
        );
      } else {
        hasClaimError.value = true;
        claimErrorMessage.value = 'Agent code not found';
        setLoading(false);
      }
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    parseClaimTrackingData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    hasClaimError.value = true;
    claimErrorMessage.value = 'Failed to load claim tracking data';
    setLoading(false);
  }

  // Parse the claim tracking response data
  void parseClaimTrackingData(dynamic response) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <ClaimTrackingModel>[];

      if (response is List) {
        for (var item in response) {
          tempList.add(ClaimTrackingModel.fromJson(item));
        }
      }

      // Update the observable list all at once
      claimTrackingList.assignAll(tempList);
    } catch (e) {
      hasClaimError.value = true;
      claimErrorMessage.value = e.toString();
      setLoading(false);
    }
  }
}

class ClaimWidgetController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  final RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team/Group
  bool isShowOtherAgent = false;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  // Controllers for different tabs
  late ClaimIndividuController individuController = Get.put(
    ClaimIndividuController(),
    tag: Utils.getRandomString(),
  );

  late ClaimTeamController teamController = Get.put(
    ClaimTeamController(),
    tag: Utils.getRandomString(),
  );

  ClaimWidgetController({this.isShowOtherAgent = false}) {
    if (isShowOtherAgent) {
      selectedSection.value = 1;
    }
  }

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'claim_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    refreshData();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    _hasInitialized = true;
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    refreshData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    refreshData();
  }

  // Refresh data based on current section and user level
  void refreshData() {
    if (level == kLevelBP || selectedSection.value == 0) {
      individuController.fetchClaimTrackingData();
    } else {
      teamController.fetchClaimTrackingData();
    }
    setLoading(false);
  }
}
