import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/widget/kompensasi_controller.dart';
import 'package:pdl_superapp/models/detail_commission_model.dart';
import 'package:pdl_superapp/models/kompensasi_item_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class KompensasiPageController extends BaseControllers {
  final RxString periode = ''.obs;
  final RxString periodeTanggal = ''.obs;
  final RxList<KompensasiItemModel> items = <KompensasiItemModel>[].obs;
  final RxString totalValue = 'Rp0'.obs;
  String level = '';
  late SharedPreferences prefs;

  // Detail commission data
  Rx<DetailCommissionModel?> detailCommissionData = Rx<DetailCommissionModel?>(
    null,
  );
  final RxList<List<String>> yearlyData = <List<String>>[].obs;

  // Reference to KompensasiController
  late KompensasiController kompensasiController;

  @override
  Future<void> onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';

    // Fetch detail commission data from API
    await fetchDetailCommissionData();

    // Get or create KompensasiController
    if (Get.isRegistered<KompensasiController>()) {
      kompensasiController = Get.find<KompensasiController>();
    } else {
      kompensasiController = Get.put(
        KompensasiController(),
        tag: "kompensasi-widget-${DateTime.timestamp()}",
      );
    }
  }

  bool isShowingField(String label) {
    if (level == 'BP' &&
        (label.toLowerCase() == 'override' ||
            label.toLowerCase() == 'bonus generasi')) {
      return false;
    } else if (level == 'BM' &&
        (label.toLowerCase() == 'bonus referensi agen' ||
            label.toLowerCase() == 'bonus generasi')) {
      return false;
    } else if (level == 'BD' && label.toLowerCase() == 'bonus referensi agen') {
      return false;
    }

    return true;
  }

  fetchDetailCommissionData() async {
    setLoading(true);
    await api.getDetailCommission(
      controllers: this,
      code: kReqGetDetailCommission,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetDetailCommission) {
      parseDetailCommissionData(response);
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  void parseDetailCommissionData(dynamic response) {
    if (response == null) return;

    try {
      detailCommissionData.value = DetailCommissionModel.fromJson(response);

      // Update UI data
      updateUIFromDetailCommissionData();
    } catch (e) {
      // Log error
      loadError(e);
    }
  }

  void updateUIFromDetailCommissionData() {
    if (detailCommissionData.value == null) return;

    final data = detailCommissionData.value!;

    // Set period information
    String monthName = '';
    if (data.month != null) {
      monthName = getMonthName(data.month!);
    }

    String yearStr = '';
    if (data.year != null) {
      yearStr = data.year.toString();
    }

    periode.value = 'Periode ${data.period} $monthName $yearStr'.trim();
    periodeTanggal.value = ''; // API doesn't provide date range

    // Clear existing items
    items.clear();

    // Add commission items (from the widget API)
    // This is kept for compatibility with the existing UI
    // In a real app, you would update this to use the detail commission data

    // Update yearly data for the table
    yearlyData.clear();
    for (var detail in data.details) {
      yearlyData.add([
        detail.year,
        formatCurrency(detail.amount.toInt()).replaceAll('Rp', ''),
        if (detail.overriding != null)
          formatCurrency(detail.overriding!.toInt()).replaceAll('Rp', ''),
      ]);
    }

    // Add total row
    yearlyData.add([
      'Total',
      formatCurrency(data.total.toInt()).replaceAll('Rp', ''),
    ]);

    // Update total value
    totalValue.value = formatCurrency(data.total.toInt());
  }

  String getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'Mei',
      'Jun',
      'Jul',
      'Agu',
      'Sep',
      'Okt',
      'Nov',
      'Des',
    ];

    if (month >= 1 && month <= 12) {
      return months[month - 1];
    }
    return '';
  }

  Future<void> refreshData() async {
    // Refresh both controllers
    await fetchDetailCommissionData();
    await kompensasiController.refreshData();
  }

  String formatCurrency(int value) {
    // Convert to string and add thousand separators
    String valueStr = value.toString();
    String result = '';
    int count = 0;

    for (int i = valueStr.length - 1; i >= 0; i--) {
      count++;
      result = valueStr[i] + result;
      if (count % 3 == 0 && i > 0) {
        result = '.$result';
      }
    }

    return 'Rp$result';
  }
}
