import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_spaj_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class HomeWidgetSpajController extends BaseControllers {
  RxList<WidgetSpajModels> arrData = RxList();
  RxList<WidgetSpajModels> arrDataIndividu = RxList();
  RxList<WidgetSpajModels> arrDataTeam = RxList();
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;
  RxInt selectedSection = 0.obs; // 0 for Individu, 1 for Team
  String userLevel = '';
  String agentName = '';
  late SharedPreferences prefs;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel = prefs.getString(kStorageUserLevel) ?? '';
    agentName = prefs.getString(kStorageAgentName) ?? "";

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'spaj_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    load();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    userLevel = prefs.getString(kStorageUserLevel) ?? '';
    agentName = prefs.getString(kStorageAgentName) ?? "";
    _hasInitialized = true;
  }

  @override
  void load() {
    super.load();
    setLoading(true);
    loadSpajIndividu();

    if (userLevel != kLevelBP) {
      loadSpajTeam();
    }
  }

  void loadSpajIndividu() {
    api.getSpajIndividu(
      controllers: this,
      code: 1,
      params: 'page=0&size=3',
    ); // Request code 1 for individu
  }

  void loadSpajTeam() {
    api.getSpajTeam(
      controllers: this,
      code: 2,
      params: 'page=0&size=3',
    ); // Request code 2 for team
  }

  // Switch between sections
  void switchToIndividu() {
    selectedSection.value = 0;
    updateDisplayData();
  }

  void switchToTeam() {
    selectedSection.value = 1;
    updateDisplayData();
  }

  // Update the displayed data based on selected tab
  void updateDisplayData() {
    if (selectedSection.value == 0) {
      arrData.assignAll(arrDataIndividu);
    } else {
      // If team data is empty but we're switching to team tab, try to load it
      if (arrDataTeam.isEmpty && userLevel == kUserLevelBm) {
        loadSpajTeam();
      }
      arrData.assignAll(arrDataTeam);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Determine if this is an individu or team response based on the request code
    String type = 'individu';

    // If we're handling a team API response
    if (requestCode == 2) {
      // Request code 2 for team API
      type = 'team';
    }

    // Debug the response structure
    _debugResponseStructure(response, type: type);

    parseData(response, type: type);
    setLoading(false);
  }

  // Helper method to debug response structure
  void _debugResponseStructure(dynamic response, {String type = 'individu'}) {
    try {
      if (response is List) {
        // It's a list, check if it has items
        if (response.isNotEmpty) {
          // Check the first item's structure
          final firstItem = response.first;
          if (firstItem is Map) {
            // Log the keys to understand the structure
            // We can log this to a file or use a proper logging framework
            // firstItem.keys.toList();
          }
        }
      } else if (response is Map) {
        // It's a map, check its keys
        // We can log this to a file or use a proper logging framework
        // response.keys.toList();
      }
    } catch (e) {
      // Silently ignore any errors in debug method
    }
  }

  parseData(response, {String type = 'individu'}) {
    try {
      // Create a temporary list to avoid concurrent modification
      final tempList = <WidgetSpajModels>[];

      // Handle different response structures
      var data = [];

      if (response is List) {
        data = response;
      } else if (response is Map) {
        // Try to extract data from different possible response structures
        if (response.containsKey('content') && response['content'] is List) {
          data = response['content'];
        } else if (response.containsKey('data') && response['data'] is List) {
          data = response['data'];
        }
      }

      // Process the data
      for (var item in data) {
        try {
          WidgetSpajModels element = WidgetSpajModels.fromJson(item);
          tempList.add(element);
        } catch (e) {
          // Skip items that can't be parsed
        }
      }

      if (type == 'individu') {
        arrDataIndividu.assignAll(tempList);
        if (selectedSection.value == 0 || userLevel != kUserLevelBm) {
          arrData.assignAll(tempList);
        }
      } else {
        arrDataTeam.assignAll(tempList);
        if (selectedSection.value == 1 && userLevel == kUserLevelBm) {
          arrData.assignAll(tempList);
        }
      }

      hasError.value = false;
      errorMessage.value = '';
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString();
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Failed to load SPAJ data';
    setLoading(false);
  }

  @override
  void loadError(e, {response}) {
    super.loadError(e, response: response);
    hasError.value = true;
    errorMessage.value = e.toString();
    setLoading(false);
  }
}
