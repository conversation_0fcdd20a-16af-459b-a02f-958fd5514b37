import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/validasi_hirarki_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ValidasiTabController extends BaseControllers {
  Rx<ValidasiHirarkiModel?> validasiData = Rx<ValidasiHirarkiModel?>(null);
  late SharedPreferences prefs;

  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  // Using the constant from keys.dart

  fetchValidasiData() async {
    setLoading(true);
    prefs = await SharedPreferences.getInstance();

    final agentCode = prefs.getString(kStorageAgentCode);
    await api.getValidasiHirarki(
      controllers: this,
      code: kReqGetValidasiHirarki,
      params: "agentCode=$agentCode",
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    parseData(response);
    setLoading(false);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    hasError.value = true;
    errorMessage.value = response.statusText ?? 'Error loading data';
  }

  void parseData(response) {
    if (response == null || response.isEmpty) {
      return;
    }

    ValidasiHirarkiModel data = ValidasiHirarkiModel.fromJson(response[0]);
    validasiData.value = data;
  }

  // Helper method to format percentage values
  String formatPercentage(double value) {
    return '${value.toStringAsFixed(1)}%';
  }

  // Helper method to format currency values
  String formatCurrency(double value) {
    if (value >= 1000000000) {
      return 'Rp${(value / 1000000000).toStringAsFixed(1)} M';
    } else if (value >= 1000000) {
      return 'Rp${(value / 1000000).toStringAsFixed(1)} Jt';
    } else {
      return 'Rp${value.toStringAsFixed(0)}';
    }
  }
}
