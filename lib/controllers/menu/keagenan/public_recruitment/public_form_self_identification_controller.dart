import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_recruitment_form_controller.dart';
import 'package:pdl_superapp/models/bank_models.dart';
import 'package:pdl_superapp/models/combo_box_models.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/models/recruitment_draft_models.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/form_validation.dart';

class PublicFormSelfIdentificationController extends BaseControllers {
  final PublicRecruitmentFormController baseController;
  PublicFormSelfIdentificationController({required this.baseController});

  List<String> emergencyContactStatusList = [
    '<PERSON>ang Tua',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> / <PERSON><PERSON>',
    'Istri / Suami',
  ];

  // FormSelfIdentification
  final emailController = TextEditingController();
  final nomorHpController = TextEditingController();
  final pekerjaanController = TextEditingController();
  final pekerjaanCodeController = TextEditingController();

  // Emergency Contact
  final emergencyNamaController = TextEditingController();
  final emergencyHubunganController = TextEditingController();
  final emergencyNomorHpController = TextEditingController();

  // Bank
  final namaPemilikRekeningController = TextEditingController();
  final nomorRekeningController = TextEditingController();
  final namaBankController = TextEditingController();
  final bankCode = RxInt(0);

  // Combo Box
  RxBool isBankLoading = true.obs;
  RxList<BankModels> bankList = RxList();
  RxList<ComboBoxValueModels> occupationList = RxList();
  RxBool isOccupationLoading = true.obs;

  RxString formUuId = ''.obs;

  // Countdown timer variables
  Timer? _countdownTimer;
  RxInt countdownSeconds = 0.obs;
  RxBool isCountdownActive = false.obs;
  RxString countdownDisplay = ''.obs;

  // Email verification checking variables
  Timer? _verificationCheckTimer;
  RxBool isEmailVerified = false.obs;
  RxBool isCheckingVerification = false.obs;
  RxString emailVerif = ''.obs;

  // Validation errors
  RxString emailError = ''.obs;
  RxString nomorHpError = ''.obs;
  RxString pekerjaanError = ''.obs;
  RxString emergencyNamaError = ''.obs;
  RxString emergencyHubunganError = ''.obs;
  RxString emergencyNomorHpError = ''.obs;
  RxString namaPemilikRekeningError = ''.obs;
  RxString nomorRekeningError = ''.obs;
  RxString namaBankError = ''.obs;

  // Validation errors for new fields
  RxString lastJobError = ''.obs;
  RxString last5YearJobDataError = ''.obs;
  RxString last2YearProductionDataError = ''.obs;
  RxString lastCompanyManPowerDataError = ''.obs;

  // LeaderForm section
  RxList<Last5YearJobData> last5YearJobData = RxList();
  RxList<Last2YearProductionData> last2YearProductionData = RxList();
  Rx<LastCompanyManPowerData> lastCompanyManPowerData = Rx(
    LastCompanyManPowerData(agentCount: 0, leaderCount: 0),
  );
  RxList<RewardInfoData> rewardInfoData = RxList();
  RxString lastJob = kLastJobSalesIssurance.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFormChangeListeners();
    api.getComboCategoryById(controllers: this, key: 'Sex', debug: true);

    api.getComboCategoryById(
      controllers: this,
      key: 'Occupation',
      code: kReqGetComboBoxOccupation,
      debug: false,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetComboBoxBank:
        parseDataBank(response);
        break;
      case kReqSaveDraft:
        baseController.setLoading(true);
        parseDataSubmitDraft(response);
        break;
      case kReqGetComboBoxOccupation:
        parseDataOccupation(response);
        break;
      case kReqPostRequestVerif:
        // 3. popup email verification page (full page)
        baseController.isVerificationEmailSent.value = true;
        emailVerif.value = response['email'] ?? '-';
        // start the countdown
        _startCountdown();
        // start checking verification status
        _startVerificationCheck();
        // {"success":true,"message":"Verification email sent successfully","email":null,"uuid":"e9d14cdb-3297-4d00-b77d-23640202110a"}
        break;
      case kReqGetIsVerified:
        parseVerificationStatus(response);
        break;
      default:
    }
  }

  void parseDataOccupation(response) {
    occupationList.clear();
    occupationList.assignAll(
      (response as List)
          .map((item) => ComboBoxValueModels.fromJSon(item))
          .toList(),
    );

    isOccupationLoading.value = false;
  }

  void parseDataSubmitDraft(response) async {
    RecruitmentDraftModels draftData = RecruitmentDraftModels.fromJson(
      response,
    );
    log('Draft data: $draftData');
    log('here uuID ${draftData.uuid}');
    formUuId.value = draftData.uuid ?? '';

    // Update form ID di Firestore dengan UUID baru dari server
    if (formUuId.value.isNotEmpty) {
      sendVerification(formUuId.value);
    }
    // baseController.isReadyToSubmit.value = false;
    // 2. send email verification
  }

  sendVerification(String uuid) {
    setLoading(true);
    baseController.api.performSendVerifEmailPublic(
      controllers: this,
      uuid: uuid,
      code: kReqPostRequestVerif,
    );
  }

  // Bank onTextUpdate
  void onBankTextChanged(String value) {
    String params = "bankName=$value";
    if (value.length > 2) {
      baseController.publicApi.getBank(
        controllers: this,
        code: kReqGetComboBoxBank,
        params: params,
      );
    }
    if (value.length < 2) {
      bankList.clear();
    }
  }

  void parseDataBank(response) {
    bankList.clear();
    bankList.assignAll(
      (response['content'] as List)
          .map((item) => BankModels.fromJson(item))
          .toList(),
    );
    isBankLoading.value = false;
    log('Branch list loaded: ${bankList.length} items');

    isBankLoading.value = false;
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk FormSelfIdentification
    emailController.addListener(_onFormChanged);
    nomorHpController.addListener(_onFormChanged);
    pekerjaanController.addListener(_onFormChanged);
    pekerjaanCodeController.addListener(_onFormChanged);
    emergencyNamaController.addListener(_onFormChanged);
    emergencyHubunganController.addListener(_onFormChanged);
    emergencyNomorHpController.addListener(_onFormChanged);
    namaPemilikRekeningController.addListener(_onFormChanged);
    nomorRekeningController.addListener(_onFormChanged);
    namaBankController.addListener(_onFormChanged);
    bankCode.listen((_) => _onFormChanged());

    // Tambahkan listener untuk field-field baru
    last5YearJobData.listen((_) => _onFormChanged());
    last2YearProductionData.listen((_) => _onFormChanged());
    lastCompanyManPowerData.listen((_) => _onFormChanged());
    rewardInfoData.listen((_) => _onFormChanged());
    lastJob.listen((_) => _onFormChanged());
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Validate form self identification
  bool validateForm() {
    bool isValid = true;

    // Trim all text fields before validation
    _trimAllTextFields();

    // Clear previous errors
    _clearAllErrors();

    // Validate email
    final emailError = FormValidation.validateEmail(emailController.text);
    if (emailError != null) {
      this.emailError.value = emailError;
      isValid = false;
    }

    // Validate phone number
    final phoneError = FormValidation.validatePhoneNumber(
      nomorHpController.text,
    );
    if (phoneError != null) {
      nomorHpError.value = phoneError;
      isValid = false;
    }
    final pekerjaanError = FormValidation.validateRequired(
      pekerjaanController.text,
      'Pekerjaan',
    );
    if (pekerjaanError != null) {
      this.pekerjaanError.value = pekerjaanError;
      isValid = false;
    }

    // Validate emergency contact name
    final emergencyNameError = FormValidation.validateRequired(
      emergencyNamaController.text,
      'Nama kontak darurat',
    );
    if (emergencyNameError != null) {
      emergencyNamaError.value = emergencyNameError;
      isValid = false;
    }

    // Validate emergency contact relation
    final emergencyRelationError = FormValidation.validateRequired(
      emergencyHubunganController.text,
      'Hubungan dengan Anda',
    );
    if (emergencyRelationError != null) {
      emergencyHubunganError.value = emergencyRelationError;
      isValid = false;
    }

    // Validate emergency contact phone
    final emergencyPhoneError = FormValidation.validatePhoneNumber(
      emergencyNomorHpController.text,
    );
    if (emergencyPhoneError != null) {
      emergencyNomorHpError.value = emergencyPhoneError;
      isValid = false;
    }

    // Validate bank account holder name
    final accountHolderError = FormValidation.validateRequired(
      namaPemilikRekeningController.text,
      'Nama pemilik rekening',
    );
    if (accountHolderError != null) {
      namaPemilikRekeningError.value = accountHolderError;
      isValid = false;
    }

    // Validate account number
    final accountNumberError = FormValidation.validateAccountNumber(
      nomorRekeningController.text,
    );
    if (accountNumberError != null) {
      nomorRekeningError.value = accountNumberError;
      isValid = false;
    }

    // Validate bank name
    final bankNameError = FormValidation.validateRequired(
      namaBankController.text,
      'Bank',
    );
    if (bankNameError != null || namaBankController.text == 'Pilih') {
      namaBankError.value = bankNameError ?? 'Silakan pilih bank';
      isValid = false;
    }

    // Validate new fields
    if (baseController.verificationController.candidateLevelController.text !=
        "BP") {
      isValid = _validateNewFields() && isValid;
    }

    return isValid;
  }

  void _clearAllErrors() {
    emailError.value = '';
    nomorHpError.value = '';
    emergencyNamaError.value = '';
    emergencyHubunganError.value = '';
    emergencyNomorHpError.value = '';
    namaPemilikRekeningError.value = '';
    nomorRekeningError.value = '';
    namaBankError.value = '';

    // Clear new field errors
    lastJobError.value = '';
    last5YearJobDataError.value = '';
    last2YearProductionDataError.value = '';
    lastCompanyManPowerDataError.value = '';
  }

  bool _validateNewFields() {
    bool isValid = true;

    // Validate lastJob - required
    if (lastJob.value.isEmpty) {
      lastJobError.value = 'Riwayat pekerjaan harus dipilih';
      isValid = false;
    }

    // Validate last5YearJobData - required
    if (last5YearJobData.isEmpty) {
      last5YearJobDataError.value =
          'Minimal harus ada 1 riwayat pekerjaan 5 tahun terakhir';
      isValid = false;
    }

    // rewardInfoData is not required (no validation needed)

    return isValid;
  }

  // Legacy validation methods (kept for backward compatibility)
  bool isEmailValid() {
    if (emailController.text.isEmpty) return false;
    return GetUtils.isEmail(emailController.text);
  }

  bool isPhoneNumberValid() {
    if (nomorHpController.text.isEmpty) return false;
    // Basic phone number validation
    return nomorHpController.text.length >= 10;
  }

  bool isPekerjaanValid() {
    if (pekerjaanController.text.isEmpty) return false;
    return true;
  }

  bool isEmergencyContactValid() {
    return emergencyNamaController.text.isNotEmpty &&
        emergencyHubunganController.text.isNotEmpty &&
        emergencyNomorHpController.text.isNotEmpty &&
        emergencyNomorHpController.text.length >= 10;
  }

  bool isBankInfoValid() {
    return namaPemilikRekeningController.text.isNotEmpty &&
        nomorRekeningController.text.isNotEmpty &&
        namaBankController.text.isNotEmpty &&
        namaBankController.text != 'Pilih';
  }

  bool isFormValid() {
    return isEmailValid() &&
        isPhoneNumberValid() &&
        isEmergencyContactValid() &&
        isBankInfoValid();
  }

  // Method untuk clear semua field
  void clearAllFields() {
    emailController.clear();
    nomorHpController.clear();
    pekerjaanController.clear();
    pekerjaanCodeController.clear();
    emergencyNamaController.clear();
    emergencyHubunganController.clear();
    emergencyNomorHpController.clear();
    namaPemilikRekeningController.clear();
    nomorRekeningController.clear();
    namaBankController.clear();
    bankCode.value = 0;

    // Clear field-field baru
    last5YearJobData.clear();
    last2YearProductionData.clear();
    lastCompanyManPowerData.value = LastCompanyManPowerData(
      agentCount: 0,
      leaderCount: 0,
    );
    rewardInfoData.clear();
    lastJob.value = kLastJobSalesIssurance;
  }

  // Method untuk auto-fill bank account holder name dengan nama KTP
  void autoFillAccountHolderName(String ktpName) {
    if (namaPemilikRekeningController.text.isEmpty && ktpName.isNotEmpty) {
      namaPemilikRekeningController.text = ktpName;
    }
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Isi FormSelfIdentification dengan data
    emailController.text = formData.email ?? '';
    nomorHpController.text = formData.nomorHp ?? '';
    pekerjaanController.text = formData.occupation ?? '';
    pekerjaanCodeController.text = formData.occupationCode ?? '';
    emergencyNamaController.text = formData.emergencyNama ?? '';
    emergencyHubunganController.text = formData.emergencyHubungan ?? '';
    emergencyNomorHpController.text = formData.emergencyNomorHp ?? '';
    namaPemilikRekeningController.text = formData.namaPemilikRekening ?? '';
    namaBankController.text = formData.namaBank ?? '';
    bankCode.value = formData.bankCode ?? 0;
    nomorRekeningController.text = formData.nomorRekening ?? '';

    // Populate lastJob field
    lastJob.value = formData.lastJob ?? kLastJobSalesIssurance;

    // Populate new structured data fields
    if (formData.last5YearJobData != null) {
      last5YearJobData.clear();
      for (var jobData in formData.last5YearJobData!) {
        last5YearJobData.add(Last5YearJobData.fromJson(jobData));
      }
    }

    if (formData.last2YearProductionData != null) {
      last2YearProductionData.clear();
      for (var productionData in formData.last2YearProductionData!) {
        last2YearProductionData.add(
          Last2YearProductionData.fromJson(productionData),
        );
      }
    }

    if (formData.lastCompanyManPowerData != null) {
      lastCompanyManPowerData.value = LastCompanyManPowerData.fromJson(
        formData.lastCompanyManPowerData!,
      );
    }

    if (formData.rewardInfoData != null) {
      rewardInfoData.clear();
      for (var rewardData in formData.rewardInfoData!) {
        rewardInfoData.add(RewardInfoData.fromJson(rewardData));
      }
    }
  }

  // Countdown timer methods
  void _startCountdown() {
    // Set countdown to 10 minutes (600 seconds)
    countdownSeconds.value = 600;
    isCountdownActive.value = true;
    _updateCountdownDisplay();

    // Cancel any existing timer
    _countdownTimer?.cancel();

    // Start new timer
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (countdownSeconds.value > 0) {
        countdownSeconds.value--;
        _updateCountdownDisplay();
      } else {
        // Countdown finished
        _stopCountdown();
      }
    });
  }

  void _updateCountdownDisplay() {
    int minutes = countdownSeconds.value ~/ 60;
    int seconds = countdownSeconds.value % 60;
    countdownDisplay.value =
        '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  void _stopCountdown() {
    _countdownTimer?.cancel();
    isCountdownActive.value = false;
    countdownSeconds.value = 0;
    countdownDisplay.value = '';
  }

  // Method to manually restart countdown (for resend functionality)
  void restartCountdown() {
    _startCountdown();
  }

  // Email verification checking methods
  void _startVerificationCheck() {
    if (formUuId.value.isEmpty) {
      log('Cannot start verification check: UUID is empty');
      return;
    }

    // Reset verification status
    isEmailVerified.value = false;
    isCheckingVerification.value = true;

    // Cancel any existing verification check timer
    _verificationCheckTimer?.cancel();

    // Start checking every 5 seconds
    _verificationCheckTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      if (!isEmailVerified.value && formUuId.value.isNotEmpty) {
        _checkVerificationStatus();
      } else {
        // Stop checking if email is verified or UUID is empty
        _stopVerificationCheck();
      }
    });

    // Do initial check immediately
    _checkVerificationStatus();
  }

  void _checkVerificationStatus() {
    if (formUuId.value.isNotEmpty) {
      api.getIsVerifiedPublic(
        controllers: this,
        uuid: formUuId.value,
        code: kReqGetIsVerified,
      );
    }
  }

  void parseVerificationStatus(response) {
    try {
      // Assuming the API returns something like {"verified": true/false}
      bool verified = response;

      if (verified) {
        _stopVerificationCheck();
        baseController.submitFormFinal();
        log('Email verification confirmed!');
      } else {
        log('Email not yet verified, will check again in 5 seconds');
      }
    } catch (e) {
      log('Error parsing verification status: $e');
    }
  }

  goToPageThree() {
    Future.delayed(Duration(seconds: 1)).then((val) {
      baseController.goToPage(3);
      baseController.isVerificationEmailSent.value = false;
    });
  }

  void _stopVerificationCheck() {
    _verificationCheckTimer?.cancel();
  }

  // Setup trim listeners untuk text fields
  void setupTrimListeners() {
    // Trim listeners sudah tidak diperlukan karena trim akan dilakukan
    // pada onEditingComplete di UI atau saat submit form
  }

  // Helper method untuk trim text field saat editing selesai
  void trimTextFieldOnComplete(TextEditingController controller) {
    final text = controller.text;
    final trimmedText = text.trim();

    if (text != trimmedText) {
      controller.text = trimmedText;
      // Set cursor ke akhir text setelah trim
      controller.selection = TextSelection.collapsed(
        offset: trimmedText.length,
      );
    }
  }

  // Method untuk trim semua text fields sebelum validasi
  void _trimAllTextFields() {
    // Trim self identification fields
    emailController.text = emailController.text.trim();
    nomorHpController.text = nomorHpController.text.trim();
    pekerjaanController.text = pekerjaanController.text.trim();
    pekerjaanCodeController.text = pekerjaanCodeController.text.trim();
    emergencyNamaController.text = emergencyNamaController.text.trim();
    emergencyHubunganController.text = emergencyHubunganController.text.trim();
    emergencyNomorHpController.text = emergencyNomorHpController.text.trim();
    namaPemilikRekeningController.text =
        namaPemilikRekeningController.text.trim();
    nomorRekeningController.text = nomorRekeningController.text.trim();
    namaBankController.text = namaBankController.text.trim();
  }

  @override
  void onClose() {
    // Cancel countdown timer
    _countdownTimer?.cancel();

    // Cancel verification check timer
    _verificationCheckTimer?.cancel();

    // Dispose all controllers
    emailController.dispose();
    nomorHpController.dispose();
    pekerjaanController.dispose();
    pekerjaanCodeController.dispose();
    emergencyNamaController.dispose();
    emergencyHubunganController.dispose();
    emergencyNomorHpController.dispose();
    namaPemilikRekeningController.dispose();
    nomorRekeningController.dispose();
    namaBankController.dispose();

    super.onClose();
  }

  List<String> getYearsListToToday({int? yearsAgo = 5, int? startYear}) {
    int currentYear = DateTime.now().year;
    int start = startYear ?? (currentYear - (yearsAgo ?? 5));

    // Pastikan start tidak melebihi current year
    if (start > currentYear) start = currentYear;

    return List<String>.generate(
      currentYear - start + 1,
      (index) => (currentYear - index).toString(),
    );
  }
}
