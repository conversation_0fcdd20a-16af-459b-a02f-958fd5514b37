import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class PublicEmailVerificationController extends BaseControllers {
  PublicEmailVerificationController();

  // State management
  RxBool isVerifying = false.obs;
  RxBool isVerified = false.obs;
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;
  RxString successMessage = ''.obs;

  // Token from URL
  RxString token = ''.obs;

  @override
  void onInit() {
    super.onInit();
    log('PublicEmailVerificationController initialized');

    // Check if this is web platform only
    if (!kIsWeb) {
      hasError.value = true;
      errorMessage.value =
          'Halaman ini hanya dapat diakses melalui web browser.';
      return;
    }

    // Get token from route parameters
    final routeToken = Get.parameters['token'];
    if (routeToken != null && routeToken.isNotEmpty) {
      token.value = routeToken;
      // Automatically start verification
      verifyEmail();
    } else {
      hasError.value = true;
      errorMessage.value = 'Token verifikasi tidak valid atau tidak ditemukan.';
    }
  }

  // Perform email verification
  void verifyEmail() {
    if (token.value.isEmpty) {
      hasError.value = true;
      errorMessage.value = 'Token verifikasi tidak valid.';
      return;
    }

    isVerifying.value = true;
    hasError.value = false;
    errorMessage.value = '';

    api.verifyEmail(
      controllers: this,
      token: token.value,
      code: kReqVerifyEmailPublic,
    );
  }

  // Retry verification
  void retryVerification() {
    hasError.value = false;
    errorMessage.value = '';
    verifyEmail();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqVerifyEmailPublic) {
      isVerifying.value = false;
      isVerified.value = true;
      successMessage.value =
          'Email berhasil diverifikasi! Terima kasih telah melakukan verifikasi.';

      // Show success snackbar
      Get.snackbar(
        'Berhasil',
        'Email berhasil diverifikasi',
        snackPosition: SnackPosition.TOP,
        backgroundColor: kColorGlobalBgGreen,
        colorText: kColorGlobalGreen,
        duration: Duration(seconds: 3),
      );
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    if (requestCode == kReqVerifyEmailPublic) {
      isVerifying.value = false;
      hasError.value = true;

      // Parse error message from response
      String message = 'Verifikasi email gagal. Silakan coba lagi.';
      try {
        if (response.body != null) {
          if (response.body is Map && response.body['message'] != null) {
            message = response.body['message'];
          } else if (response.body is String) {
            message = response.body;
          }
        }
      } catch (e) {
        log('Error parsing error message: $e');
      }

      errorMessage.value = message;

      // Show error snackbar
      Get.snackbar(
        'Gagal',
        message,
        snackPosition: SnackPosition.TOP,
        backgroundColor: kColorGlobalBgRed,
        colorText: kColorErrorText,
        duration: Duration(seconds: 5),
      );
    }
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);

    isVerifying.value = false;
    hasError.value = true;
    errorMessage.value =
        'Terjadi kesalahan saat verifikasi email. Silakan coba lagi.';

    // Show error snackbar
    Get.snackbar(
      'Error',
      'Terjadi kesalahan saat verifikasi email',
      snackPosition: SnackPosition.TOP,
      backgroundColor: kColorGlobalBgRed,
      colorText: kColorErrorText,
      duration: Duration(seconds: 5),
    );

    log('Email verification error: $e');
  }

  // Navigate back or close
  void goBack() {
    Get.back();
  }

  // Navigate to home
  void goToHome() {
    // For public pages, we can't navigate to authenticated routes
    // So we'll just close the current page
    Get.back();
  }
}
