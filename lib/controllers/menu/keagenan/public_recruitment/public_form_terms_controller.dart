import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/image_file_helper.dart';

class PublicFormTermsController extends BaseControllers {
  final PublicRecruitmentFormController baseController;

  PublicFormTermsController({required this.baseController});

  // Terms and conditions state
  RxBool isAllTermsRead = false.obs;
  RxBool isAgreementChecked = false.obs;
  RxBool isSignatureCompleted = false.obs;
  RxBool isParafCompleted = false.obs;

  // Document viewing state
  RxBool isPkajDocumentViewed = false.obs;
  RxBool isPmkajDocumentViewed = false.obs;
  RxBool isKodeEtikDocumentViewed = false.obs;
  RxBool isAntiTwistingDocumentViewed = false.obs;
  RxList<bool> termDocumentsViewed = <bool>[].obs;

  // Signature and paraf data
  RxString signatureData = ''.obs;
  RxString parafData = ''.obs;

  // Signature and paraf URLs
  RxString signatureUrl = ''.obs;
  RxString parafUrl = ''.obs;

  // Upload states
  RxBool isSignatureUploading = false.obs;
  RxBool isParafUploading = false.obs;
  RxDouble signatureUploadProgress = 0.0.obs;
  RxDouble parafUploadProgress = 0.0.obs;

  // Validation errors
  RxString agreementError = ''.obs;
  RxString signatureError = ''.obs;
  RxString parafError = ''.obs;

  // Share functionality
  RxBool isSharing = false.obs;

  // Document URLs
  RxString pkajUrl = ''.obs;
  RxString pmkajUrl = ''.obs;
  RxString etikUrl = ''.obs;
  RxString antiTwistUrl = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    // Initialize term documents viewed list
    // Adjust the size based on the number of terms documents
    termDocumentsViewed.value = List.filled(5, false); // Assuming 5 documents

    // Fetch document URLs from API
    await api.getGlobalConfig(
      controllers: this,
      key: kConfKeyPkaj,
      code: kReqConfGlobalPkaj,
    );
    await api.getGlobalConfig(
      controllers: this,
      key: kConfKeyAntiTwist,
      code: kReqConfGlobalAntiTwist,
    );
    await api.getGlobalConfig(
      controllers: this,
      key: kConfKeyEtic,
      code: kReqConfGlobalEtic,
    );
    await api.getGlobalConfig(
      controllers: this,
      key: kConfKeyPmkaj,
      code: kReqConfGlobalPmkaj,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    switch (requestCode) {
      case kReqConfGlobalPkaj:
        pkajUrl.value = response['value'] as String;
        break;
      case kReqConfGlobalAntiTwist:
        antiTwistUrl.value = response['value'] as String;
        break;
      case kReqConfGlobalEtic:
        etikUrl.value = response['value'] as String;
        break;
      case kReqConfGlobalPmkaj:
        pmkajUrl.value = response['value'] as String;
        break;
      default:
        break;
    }
  }

  // Check if all terms and conditions have been read
  void checkAllTermsRead() {
    bool allRead =
        isPkajDocumentViewed.value &&
        isPmkajDocumentViewed.value &&
        isKodeEtikDocumentViewed.value &&
        isAntiTwistingDocumentViewed.value;

    if (baseController.verificationController.candidateLevelController.text ==
        'BP') {
      allRead =
          isPkajDocumentViewed.value &&
          isKodeEtikDocumentViewed.value &&
          isAntiTwistingDocumentViewed.value;
    }

    bool previousState = isAllTermsRead.value;
    isAllTermsRead.value = allRead;

    // Log status change for debugging
    if (previousState != allRead) {
      log('Terms reading status changed: $previousState -> $allRead');
      logDocumentStatus();
    }

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Check if a document can be opened based on sequential order
  RxBool canOpenDocument(String documentType) {
    // Always allow opening PKAJ (first document)
    if (documentType == 'PKAJ') {
      return true.obs;
    }

    // For BP role, skip PMKAJ
    bool isBP =
        baseController.verificationController.candidateLevelController.text ==
        'BP';

    if (documentType == 'PMKAJ') {
      // PMKAJ can only be opened if PKAJ is viewed and user is not BP
      return (!isBP && isPkajDocumentViewed.value).obs;
    }

    if (documentType == 'KODE_ETIK') {
      // Kode Etik can be opened if:
      // - For BP: PKAJ is viewed
      // - For non-BP: PKAJ and PMKAJ are viewed
      if (isBP) {
        return (isPkajDocumentViewed.value).obs;
      } else {
        return (isPkajDocumentViewed.value && isPmkajDocumentViewed.value).obs;
      }
    }

    if (documentType == 'ANTI_TWISTING') {
      // Anti Twisting can be opened if:
      // - For BP: PKAJ and Kode Etik are viewed
      // - For non-BP: PKAJ, PMKAJ, and Kode Etik are viewed
      if (isBP) {
        return (isPkajDocumentViewed.value && isKodeEtikDocumentViewed.value)
            .obs;
      } else {
        return (isPkajDocumentViewed.value &&
                isPmkajDocumentViewed.value &&
                isKodeEtikDocumentViewed.value)
            .obs;
      }
    }

    return false.obs;
  }

  // Check if checkbox can be enabled (all documents read + paraf and signature completed)
  bool canEnableCheckbox() {
    return isAllTermsRead.value &&
        isParafCompleted.value &&
        isSignatureCompleted.value;
  }

  // Mark PKAJ document as viewed
  void markPkajDocumentViewed() {
    isPkajDocumentViewed.value = true;
    checkAllTermsRead();
  }

  // Mark PMKAJ document as viewed
  void markPmkajDocumentViewed() {
    isPmkajDocumentViewed.value = true;
    checkAllTermsRead();
  }

  // Mark Kode Etik document as viewed
  void markKodeEtikDocumentViewed() {
    isKodeEtikDocumentViewed.value = true;
    checkAllTermsRead();
  }

  // Mark Anti Twisting document as viewed
  void markAntiTwistingDocumentViewed() {
    isAntiTwistingDocumentViewed.value = true;
    checkAllTermsRead();
  }

  // Mark specific term document as viewed (for backward compatibility)
  void markTermDocumentViewed(int index) {
    switch (index) {
      case 0:
        markPkajDocumentViewed();
        break;
      case 1:
        markPmkajDocumentViewed();
        break;
      case 2:
        markKodeEtikDocumentViewed();
        break;
      case 3:
        markAntiTwistingDocumentViewed();
        break;
      default:
        if (index >= 0 && index < termDocumentsViewed.length) {
          termDocumentsViewed[index] = true;
          checkAllTermsRead();
        }
    }
  }

  // Toggle agreement checkbox
  void toggleAgreement(bool? value) {
    bool newValue = value ?? false;
    bool previousValue = isAgreementChecked.value;

    isAgreementChecked.value = newValue;
    agreementError.value = '';

    // If agreement becomes true and we have signature/paraf data, upload them
    if (newValue && !previousValue) {
      _uploadSignatureAndParaf();
    }

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Set signature data
  void setSignatureData(String data) {
    signatureData.value = data;
    isSignatureCompleted.value = data.isNotEmpty;
    signatureError.value = '';

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Set paraf data
  void setParafData(String data) {
    parafData.value = data;
    isParafCompleted.value = data.isNotEmpty;
    parafError.value = '';

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Clear signature
  void clearSignature() {
    signatureData.value = '';
    isSignatureCompleted.value = false;

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Clear paraf
  void clearParaf() {
    parafData.value = '';
    isParafCompleted.value = false;

    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Upload signature and paraf when agreement is checked
  Future<void> _uploadSignatureAndParaf() async {
    try {
      // Upload signature if available
      if (signatureData.value.isNotEmpty) {
        await _uploadSignatureImage(signatureData.value, kPhotoTypeSignature);
      }

      // Upload paraf if available
      if (parafData.value.isNotEmpty) {
        await _uploadSignatureImage(parafData.value, kPhotoTypeParaf);
      }
    } catch (e) {
      log('Error uploading signature/paraf: $e');
    }
  }

  // Upload signature or paraf image
  Future<void> _uploadSignatureImage(String base64Data, String type) async {
    try {
      // Set upload status
      if (type == kPhotoTypeSignature) {
        isSignatureUploading.value = true;
        signatureUploadProgress.value = 0.0;
      } else if (type == kPhotoTypeParaf) {
        isParafUploading.value = true;
        parafUploadProgress.value = 0.0;
      }

      // Convert base64 to file

      File? imageFile;
      Uint8List? imageBytes;
      if (kIsWeb) {
        imageBytes = base64Decode(base64Data);
      } else {
        imageFile = await _base64ToFile(base64Data, type);
      }

      // Call API upload function
      final result = await baseController.publicApi.uploadRecruitmentImage(
        imageFile: imageFile,
        imageBytes: imageBytes,
        type: type,
        onProgress: (percent) {
          // Update progress
          if (type == kPhotoTypeSignature) {
            signatureUploadProgress.value = percent;
          } else if (type == kPhotoTypeParaf) {
            parafUploadProgress.value = percent;
          }
        },
      );

      if (result != null && result['success'] == true) {
        final uploadedUrl = result['url'] as String?;
        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          // Store the URL
          if (type == kPhotoTypeSignature) {
            signatureUrl.value = uploadedUrl;
          } else if (type == kPhotoTypeParaf) {
            parafUrl.value = uploadedUrl;
          }
          log("Upload berhasil untuk $type: $uploadedUrl");
        }
      } else {
        final message = result?['message'] ?? 'Upload gagal';
        log("Upload gagal untuk $type: $message");
      }

      // Clean up temporary file
      try {
        await ImageFileHelper.safeDeleteImageFile(imageFile);
      } catch (e) {
        log('Failed to delete temporary file: $e');
      }
    } catch (e) {
      log("Error saat upload $type: $e");
    } finally {
      // Reset upload status
      if (type == kPhotoTypeSignature) {
        isSignatureUploading.value = false;
      } else if (type == kPhotoTypeParaf) {
        isParafUploading.value = false;
      }
    }
  }

  // Convert base64 string to temporary file
  Future<File?> _base64ToFile(String base64String, String type) async {
    try {
      final Uint8List bytes = base64Decode(base64String);
      final Directory tempDir = await getTemporaryDirectory();
      final String fileName =
          '${type}_${DateTime.now().millisecondsSinceEpoch}.png';
      final File file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(bytes);
      return file;
    } catch (e) {
      log('Error converting base64 to file: $e');
      return null;
    }
  }

  // Share form functionality
  Future<void> shareForm() async {
    if (isSharing.value) return;

    isSharing.value = true;

    try {
      // Implement share functionality here
      // This could involve generating a shareable link or PDF
      log('Sharing form with ID: ${baseController.formId.value}');

      // Example implementation:
      // final shareUrl = await generateShareableUrl();
      // await Share.share(shareUrl);

      Get.snackbar(
        'Berhasil',
        'Link formulir berhasil dibagikan',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      log('Error sharing form: $e');
      Get.snackbar(
        'Error',
        'Gagal membagikan formulir',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      isSharing.value = false;
    }
  }

  // Populate form data from model
  void populateFormData(RecruitmentFormModel formData) {
    // Populate terms and signature data
    signatureData.value = formData.signature ?? '';
    parafData.value = formData.paraf ?? '';

    // Populate signature and paraf URLs if available
    signatureUrl.value = formData.signatureUrl ?? '';
    parafUrl.value = formData.parafUrl ?? '';

    isSignatureCompleted.value = signatureData.value.isNotEmpty;
    isParafCompleted.value = parafData.value.isNotEmpty;

    // You might want to add fields to RecruitmentFormModel for terms state
    // For now, we'll assume if signature exists, terms were read
    if (signatureData.value.isNotEmpty) {
      isPkajDocumentViewed.value = true;
      isPmkajDocumentViewed.value = true;
      isKodeEtikDocumentViewed.value = true;
      isAntiTwistingDocumentViewed.value = true;
      termDocumentsViewed.fillRange(0, termDocumentsViewed.length, true);
      isAllTermsRead.value = true;
      isAgreementChecked.value = true;
    }
  }

  // Validate form terms
  bool validateForm() {
    bool isValid = true;

    // Clear previous errors
    agreementError.value = '';
    signatureError.value = '';
    parafError.value = '';

    // Validate that all terms have been read
    if (!isAllTermsRead.value) {
      agreementError.value = 'Mohon baca seluruh dokumen terlebih dahulu';
      isValid = false;
    }

    // Validate agreement checkbox
    if (!isAgreementChecked.value) {
      agreementError.value = 'Anda harus menyetujui perjanjian keagenan';
      isValid = false;
    }

    // Validate signature
    if (signatureData.value.isEmpty) {
      signatureError.value = 'Tanda tangan tidak boleh kosong';
      isValid = false;
    }

    // Validate paraf
    if (parafData.value.isEmpty) {
      parafError.value = 'Paraf tidak boleh kosong';
      isValid = false;
    }

    return isValid;
  }

  // Check if form can be submitted
  bool canSubmitForm() {
    return isAllTermsRead.value &&
        isAgreementChecked.value &&
        isSignatureCompleted.value &&
        isParafCompleted.value;
  }

  // Getter methods for individual document states
  bool get isPkajViewed => isPkajDocumentViewed.value;
  bool get isPmkajViewed => isPmkajDocumentViewed.value;
  bool get isKodeEtikViewed => isKodeEtikDocumentViewed.value;
  bool get isAntiTwistingViewed => isAntiTwistingDocumentViewed.value;

  // Get total number of documents viewed
  int get documentsViewedCount {
    int count = 0;
    if (isPkajDocumentViewed.value) count++;
    if (isPmkajDocumentViewed.value) count++;
    if (isKodeEtikDocumentViewed.value) count++;
    if (isAntiTwistingDocumentViewed.value) count++;
    return count;
  }

  // Get total number of required documents
  int get totalRequiredDocuments => 4;

  // Get viewing progress as percentage
  double get viewingProgress => documentsViewedCount / totalRequiredDocuments;

  // Get document status summary for debugging
  Map<String, bool> get documentStatusSummary => {
    'PKAJ': isPkajDocumentViewed.value,
    'PMKAJ': isPmkajDocumentViewed.value,
    'Kode Etik': isKodeEtikDocumentViewed.value,
    'Anti Twisting': isAntiTwistingDocumentViewed.value,
    'All Terms Read': isAllTermsRead.value,
  };

  // Log current document status
  void logDocumentStatus() {
    log('Document Status Summary:');
    documentStatusSummary.forEach((key, value) {
      log('  $key: $value');
    });
    log('Progress: ${(viewingProgress * 100).toStringAsFixed(1)}%');
  }

  // Reset all document viewing states
  void resetDocumentStates() {
    isPkajDocumentViewed.value = false;
    isPmkajDocumentViewed.value = false;
    isKodeEtikDocumentViewed.value = false;
    isAntiTwistingDocumentViewed.value = false;
    termDocumentsViewed.fillRange(0, termDocumentsViewed.length, false);
    checkAllTermsRead();
  }

  // Reset all form states
  void resetFormStates() {
    resetDocumentStates();
    isAgreementChecked.value = false;
    signatureData.value = '';
    parafData.value = '';
    signatureUrl.value = '';
    parafUrl.value = '';
    isSignatureCompleted.value = false;
    isParafCompleted.value = false;

    // Clear errors
    agreementError.value = '';
    signatureError.value = '';
    parafError.value = '';
  }

  @override
  void onClose() {
    // Clean up any resources if needed
    super.onClose();
  }
}
