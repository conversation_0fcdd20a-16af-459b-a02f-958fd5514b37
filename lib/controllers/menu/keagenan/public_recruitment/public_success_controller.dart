import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

class PublicSuccessController extends BaseControllers {
  PublicSuccessController();

  // Navigation state
  RxBool isNavigating = false.obs;

  @override
  void onInit() {
    super.onInit();
    log('PublicSuccessController initialized');
  }

  // Navigate back to home or close the form
  void navigateToHome() {
    if (isNavigating.value) return;

    isNavigating.value = true;

    try {
      // Close all overlays and navigate to root
      Get.offAllNamed('/');
    } catch (e) {
      log('Error navigating to home: $e');
    } finally {
      isNavigating.value = false;
    }
  }

  // Share success information
  Future<void> shareSuccess() async {
    try {
      // Implement share functionality here if needed
      // This could involve sharing a success message or reference number

      Get.snackbar(
        'Berhasil',
        'Informasi berhasil dibagikan',
        snackPosition: SnackPosition.TOP,
      );
    } catch (e) {
      log('Error sharing success: $e');
      Get.snackbar(
        'Error',
        'Gagal membagikan informasi',
        snackPosition: SnackPosition.TOP,
      );
    }
  }
}
