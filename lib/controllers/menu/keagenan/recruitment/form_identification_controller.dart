import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/services/ktp_ocr_service.dart';
import 'package:pdl_superapp/utils/form_validation.dart';
import 'package:pdl_superapp/utils/image_file_helper.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:image/image.dart' as img;

class FormIdentificationController extends BaseControllers {
  final RecruitmentFormController baseController;
  FormIdentificationController({required this.baseController});

  List<String> monthList = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'Mei',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Okt',
    'Nov',
    'Des',
  ];

  // Gender
  // 0 -> Laki-laki
  // 1 -> Perempuan
  RxInt selectedJenisKelamin = 3.obs;

  // isAddressSame
  // 0 -> Sama
  // 1 -> Beda
  RxInt selectedIsAddressSame = 3.obs;

  RxString selectedMaritalStatus = ''.obs;
  List<String> maritalStatusList = [
    'KAWIN',
    'BELUM KAWIN',
    'CERAI',
    'CERAI MATI',
  ];

  // FormIdentification
  final nikController = TextEditingController();
  final namaKtpController = TextEditingController();
  final tempatLahirController = TextEditingController();
  final tanggalLahirController = TextEditingController();
  final bulanLahirController = TextEditingController();
  final tahunLahirController = TextEditingController();
  final jenisKelaminController = TextEditingController();
  final alamatKtpController = TextEditingController();
  final rtKtpController = TextEditingController();
  final rwKtpController = TextEditingController();
  final provinsiKtpController = TextEditingController();
  final kabupatenKtpController = TextEditingController();
  final kecamatanKtpController = TextEditingController();
  final kelurahanKtpController = TextEditingController();

  // Alamat Domisili (Home Address)
  final alamatDomisiliController = TextEditingController();
  final rtDomisiliController = TextEditingController();
  final rwDomisiliController = TextEditingController();
  final provinsiDomisiliController = TextEditingController();
  final kabupatenDomisiliController = TextEditingController();
  final kecamatanDomisiliController = TextEditingController();
  final kelurahanDomisiliController = TextEditingController();

  // Validation errors
  RxString nikError = ''.obs;
  RxString namaKtpError = ''.obs;
  RxString tempatLahirError = ''.obs;
  RxString tanggalLahirError = ''.obs;
  RxString bulanLahirError = ''.obs;
  RxString tahunLahirError = ''.obs;
  RxString alamatKtpError = ''.obs;
  RxString rtKtpError = ''.obs;
  RxString rwKtpError = ''.obs;
  RxString provinsiKtpError = ''.obs;
  RxString kabupatenKtpError = ''.obs;
  RxString kecamatanKtpError = ''.obs;
  RxString kelurahanKtpError = ''.obs;

  // Validation errors for domisili (only when different from KTP)
  RxString alamatDomisiliError = ''.obs;
  RxString rtDomisiliError = ''.obs;
  RxString rwDomisiliError = ''.obs;
  RxString provinsiDomisiliError = ''.obs;
  RxString kabupatenDomisiliError = ''.obs;
  RxString kecamatanDomisiliError = ''.obs;
  RxString kelurahanDomisiliError = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _setupFormChangeListeners();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Tambahkan listener untuk FormIdentification
    nikController.addListener(_onFormChanged);
    namaKtpController.addListener(_onFormChanged);
    tempatLahirController.addListener(_onFormChanged);
    tanggalLahirController.addListener(_onFormChanged);
    bulanLahirController.addListener(_onFormChanged);
    tahunLahirController.addListener(_onFormChanged);
    jenisKelaminController.addListener(_onFormChanged);
    alamatKtpController.addListener(_onFormChanged);
    rtKtpController.addListener(_onFormChanged);
    rwKtpController.addListener(_onFormChanged);
    provinsiKtpController.addListener(_onFormChanged);
    kabupatenKtpController.addListener(_onFormChanged);
    kecamatanKtpController.addListener(_onFormChanged);
    kelurahanKtpController.addListener(_onFormChanged);

    // Tambahkan listener untuk Alamat Domisili
    alamatDomisiliController.addListener(_onFormChanged);
    rtDomisiliController.addListener(_onFormChanged);
    rwDomisiliController.addListener(_onFormChanged);
    provinsiDomisiliController.addListener(_onFormChanged);
    kabupatenDomisiliController.addListener(_onFormChanged);
    kecamatanDomisiliController.addListener(_onFormChanged);
    kelurahanDomisiliController.addListener(_onFormChanged);

    // Tambahkan listener untuk perubahan selection
    selectedJenisKelamin.listen((_) => _onFormChanged());
    selectedMaritalStatus.listen((_) => _onFormChanged());
    selectedIsAddressSame.listen((_) => _onFormChanged());
  }

  // Handler ketika form berubah
  void _onFormChanged() {
    // Notify parent controller about form change
    try {
      baseController.onFormChanged();
    } catch (e) {
      log('Parent controller not found: $e');
    }
  }

  // Method untuk copy alamat KTP ke alamat domisili
  void copyKtpAddressToDomisili() {
    if (selectedIsAddressSame.value == 0) {
      alamatDomisiliController.text = alamatKtpController.text;
      rtDomisiliController.text = rtKtpController.text;
      rwDomisiliController.text = rwKtpController.text;
      provinsiDomisiliController.text = provinsiKtpController.text;
      kabupatenDomisiliController.text = kabupatenKtpController.text;
      kecamatanDomisiliController.text = kecamatanKtpController.text;
      kelurahanDomisiliController.text = kelurahanKtpController.text;
    }
  }

  // Method untuk clear alamat domisili
  void clearDomisiliAddress() {
    if (selectedIsAddressSame.value == 1) {
      alamatDomisiliController.clear();
      rtDomisiliController.clear();
      rwDomisiliController.clear();
      provinsiDomisiliController.clear();
      kabupatenDomisiliController.clear();
      kecamatanDomisiliController.clear();
      kelurahanDomisiliController.clear();
    }
  }

  Future<void> processKtpOcr(dynamic file) async {
    try {
      // Convert to compatible file format for the platform
      final compatibleFile = ImageFileHelper.convertToCompatibleFile(file);
      final preprocessedFile = await preprocessImage(compatibleFile);

      // Use new KTP OCR service with Tesseract.js for web
      final ktpData = await KtpOcrService.extractKtp(preprocessedFile);

      // Check if there are quality issues
      if (ktpData.hasQualityIssues) {
        await showImageQualityDialog(ktpData.qualityIssues!, file);
        return;
      }

      // Map the data to form fields
      nikController.text = ktpData.nik ?? '';
      namaKtpController.text = ktpData.name?.toUpperCase().trim() ?? '';
      tempatLahirController.text = ktpData.placeBirth ?? '';

      // Parse birth date if available
      if (ktpData.birthDay != null && ktpData.birthDay!.isNotEmpty) {
        final birthParts = ktpData.birthDay!.split('-');
        if (birthParts.length >= 3) {
          tanggalLahirController.text = birthParts[0];
          bulanLahirController.text = getMonth(
            int.tryParse(birthParts[1]) ?? 1,
          );
          tahunLahirController.text = birthParts[2];
        }
      }

      // Set gender
      if (ktpData.gender != null) {
        jenisKelaminController.text =
            ktpData.gender!.toUpperCase() == 'LAKI-LAKI'
                ? 'Laki-laki'
                : 'Perempuan';
        selectedJenisKelamin.value =
            ktpData.gender!.toUpperCase() == 'LAKI-LAKI' ? 0 : 1;
      }

      // Set marital status
      selectedMaritalStatus.value = getStatusPerkawinan(
        ktpData.marital ?? 'KAWIN',
      );

      // Set address fields
      alamatKtpController.text = ktpData.address ?? '';
      rtKtpController.text = ktpData.rt ?? '';
      rwKtpController.text = ktpData.rw ?? '';
      kelurahanKtpController.text = ktpData.subDistrict ?? '';
      kecamatanKtpController.text = ktpData.district ?? '';
      provinsiKtpController.text = ktpData.province ?? '';
      kabupatenKtpController.text = ktpData.city ?? '';
    } catch (e) {
      log('Error processing KTP OCR: $e');
    }
  }

  String getStatusPerkawinan(String status) {
    String res = 'KAWIN';
    res = status.replaceAll('_', ' ');
    if (res == 'CERAIMATI') {
      res = 'CERAI MATI';
    }
    if (res == 'BELUMKAWIN') {
      res = 'BELUM KAWIN';
    }

    return res;
  }

  String getMonth(int month) {
    String res = 'Jan';
    res = monthList[month - 1];
    return res;
  }

  // Populate form data from model (from Firestore)
  void populateFormData(RecruitmentFormModel formData) {
    // Isi FormIdentification dengan data
    nikController.text = formData.nik ?? '';
    namaKtpController.text = formData.namaKtp ?? '';
    tempatLahirController.text = formData.tempatLahir ?? '';
    tanggalLahirController.text = formData.tanggalLahir ?? '';
    bulanLahirController.text = formData.bulanLahir ?? '';
    tahunLahirController.text = formData.tahunLahir ?? '';
    jenisKelaminController.text =
        formData.jenisKelamin ??
        (formData.jenisKelamin == 'Laki-laki' ? 'Laki-laki' : 'Perempuan');
    selectedJenisKelamin.value = formData.jenisKelamin == 'Laki-laki' ? 0 : 1;
    selectedMaritalStatus.value =
        formData.maritalStatus?.replaceAll(' ', '_') == ''
            ? 'KAWIN'
            : formData.maritalStatus?.replaceAll(' ', '_') ?? 'KAWIN';
    alamatKtpController.text = formData.alamatKtp ?? '';
    rtKtpController.text = formData.rtKtp ?? '';
    rwKtpController.text = formData.rwKtp ?? '';
    provinsiKtpController.text = formData.provinsiKtp ?? '';
    kabupatenKtpController.text = formData.kabupatenKtp ?? '';
    kecamatanKtpController.text = formData.kecamatanKtp ?? '';
    kelurahanKtpController.text = formData.kelurahanKtp ?? '';

    // Set isDomicileSameAsKtp status
    selectedIsAddressSame.value =
        (formData.isDomicileSameAsKtp ?? true) ? 0 : 1;

    // Isi Alamat Domisili dengan data
    alamatDomisiliController.text = formData.alamatDomisili ?? '';
    rtDomisiliController.text = formData.rtDomisili ?? '';
    rwDomisiliController.text = formData.rwDomisili ?? '';
    provinsiDomisiliController.text = formData.provinsiDomisili ?? '';
    kabupatenDomisiliController.text = formData.kabupatenDomisili ?? '';
    kecamatanDomisiliController.text = formData.kecamatanDomisili ?? '';
    kelurahanDomisiliController.text = formData.kelurahanDomisili ?? '';
  }

  // Populate form data from API model (from approval page)
  void populateFormDataFromApi(RecruitmentApiModel apiData) {
    // Parse birth date from API format (YYYY-MM-DD)
    String birthDate = apiData.birthDate ?? '';
    if (birthDate.isNotEmpty) {
      List<String> dateParts = birthDate.split('-');
      if (dateParts.length == 3) {
        tahunLahirController.text = dateParts[0];
        bulanLahirController.text = getMonth(int.parse(dateParts[1]));
        tanggalLahirController.text = dateParts[2];
      }
    }

    // Isi FormIdentification dengan data dari API
    nikController.text = apiData.nik ?? '';
    namaKtpController.text = apiData.fullName ?? '';
    tempatLahirController.text = apiData.birthPlace ?? '';
    jenisKelaminController.text =
        apiData.gender == 'M' ? 'Laki-laki' : 'Perempuan';
    selectedJenisKelamin.value = apiData.gender == 'M' ? 0 : 1;
    selectedMaritalStatus.value =
        apiData.maritalStatus?.replaceAll('_', ' ') ?? 'KAWIN';
    alamatKtpController.text = apiData.ktpAddress ?? '';
    rtKtpController.text = apiData.ktpRt ?? '';
    rwKtpController.text = apiData.ktpRw ?? '';
    provinsiKtpController.text = apiData.ktpProvince ?? '';
    kabupatenKtpController.text = apiData.ktpCity ?? '';
    kecamatanKtpController.text = apiData.ktpDistrict ?? '';
    kelurahanKtpController.text = apiData.ktpSubDistrict ?? '';

    // Set alamat domisili berdasarkan isDomicileSameAsKtp
    selectedIsAddressSame.value = (apiData.isDomicileSameAsKtp ?? true) ? 0 : 1;

    if (apiData.isDomicileSameAsKtp == true) {
      // Copy KTP address to domisili
      copyKtpAddressToDomisili();
    } else {
      // Isi Alamat Domisili dengan data terpisah
      alamatDomisiliController.text = apiData.domicileAddress ?? '';
      rtDomisiliController.text = apiData.domicileRt ?? '';
      rwDomisiliController.text = apiData.domicileRw ?? '';
      provinsiDomisiliController.text = apiData.domicileProvince ?? '';
      kabupatenDomisiliController.text = apiData.domicileCity ?? '';
      kecamatanDomisiliController.text = apiData.domicileDistrict ?? '';
      kelurahanDomisiliController.text = apiData.domicileSubDistrict ?? '';
    }
  }

  // Validate form identification
  bool validateForm() {
    bool isValid = true;

    // Trim all text fields before validation
    _trimAllTextFields();

    // Clear previous errors
    _clearAllErrors();

    // Validate KTP data
    final nikError = FormValidation.validateNIK(nikController.text);
    if (nikError != null) {
      this.nikError.value = nikError;
      isValid = false;
    }

    final namaError = FormValidation.validateRequired(
      namaKtpController.text,
      'Nama lengkap sesuai KTP',
    );
    if (namaError != null) {
      namaKtpError.value = namaError;
      isValid = false;
    }

    final tempatLahirError = FormValidation.validateRequired(
      tempatLahirController.text,
      'Tempat lahir',
    );
    if (tempatLahirError != null) {
      this.tempatLahirError.value = tempatLahirError;
      isValid = false;
    }

    final tanggalError = FormValidation.validateDate(
      tanggalLahirController.text,
    );
    if (tanggalError != null) {
      tanggalLahirError.value = tanggalError;
      isValid = false;
    }

    final bulanError = FormValidation.validateRequired(
      bulanLahirController.text,
      'Bulan lahir',
    );
    if (bulanError != null) {
      bulanLahirError.value = bulanError;
      isValid = false;
    }

    final tahunError = FormValidation.validateYear(tahunLahirController.text);
    if (tahunError != null) {
      tahunLahirError.value = tahunError;
      isValid = false;
    }

    // Validate KTP address
    final alamatKtpError = FormValidation.validateRequired(
      alamatKtpController.text,
      'Alamat KTP',
    );
    if (alamatKtpError != null) {
      this.alamatKtpError.value = alamatKtpError;
      isValid = false;
    }

    final rtKtpError = FormValidation.validateRtRw(
      rtKtpController.text,
      'RT KTP',
    );
    if (rtKtpError != null) {
      this.rtKtpError.value = rtKtpError;
      isValid = false;
    }

    final rwKtpError = FormValidation.validateRtRw(
      rwKtpController.text,
      'RW KTP',
    );
    if (rwKtpError != null) {
      this.rwKtpError.value = rwKtpError;
      isValid = false;
    }

    final provinsiKtpError = FormValidation.validateRequired(
      provinsiKtpController.text,
      'Provinsi KTP',
    );
    if (provinsiKtpError != null) {
      this.provinsiKtpError.value = provinsiKtpError;
      isValid = false;
    }

    final kabupatenKtpError = FormValidation.validateRequired(
      kabupatenKtpController.text,
      'Kabupaten/Kota KTP',
    );
    if (kabupatenKtpError != null) {
      this.kabupatenKtpError.value = kabupatenKtpError;
      isValid = false;
    }

    final kecamatanKtpError = FormValidation.validateRequired(
      kecamatanKtpController.text,
      'Kecamatan KTP',
    );
    if (kecamatanKtpError != null) {
      this.kecamatanKtpError.value = kecamatanKtpError;
      isValid = false;
    }

    final kelurahanKtpError = FormValidation.validateRequired(
      kelurahanKtpController.text,
      'Kelurahan/Desa KTP',
    );
    if (kelurahanKtpError != null) {
      this.kelurahanKtpError.value = kelurahanKtpError;
      isValid = false;
    }

    // Validate domisili address only if different from KTP
    if (selectedIsAddressSame.value == 1) {
      final alamatDomisiliError = FormValidation.validateRequired(
        alamatDomisiliController.text,
        'Alamat domisili',
      );
      if (alamatDomisiliError != null) {
        this.alamatDomisiliError.value = alamatDomisiliError;
        isValid = false;
      }

      final rtDomisiliError = FormValidation.validateRtRw(
        rtDomisiliController.text,
        'RT domisili',
      );
      if (rtDomisiliError != null) {
        this.rtDomisiliError.value = rtDomisiliError;
        isValid = false;
      }

      final rwDomisiliError = FormValidation.validateRtRw(
        rwDomisiliController.text,
        'RW domisili',
      );
      if (rwDomisiliError != null) {
        this.rwDomisiliError.value = rwDomisiliError;
        isValid = false;
      }

      final provinsiDomisiliError = FormValidation.validateRequired(
        provinsiDomisiliController.text,
        'Provinsi domisili',
      );
      if (provinsiDomisiliError != null) {
        this.provinsiDomisiliError.value = provinsiDomisiliError;
        isValid = false;
      }

      final kabupatenDomisiliError = FormValidation.validateRequired(
        kabupatenDomisiliController.text,
        'Kabupaten/Kota domisili',
      );
      if (kabupatenDomisiliError != null) {
        this.kabupatenDomisiliError.value = kabupatenDomisiliError;
        isValid = false;
      }

      final kecamatanDomisiliError = FormValidation.validateRequired(
        kecamatanDomisiliController.text,
        'Kecamatan domisili',
      );
      if (kecamatanDomisiliError != null) {
        this.kecamatanDomisiliError.value = kecamatanDomisiliError;
        isValid = false;
      }

      final kelurahanDomisiliError = FormValidation.validateRequired(
        kelurahanDomisiliController.text,
        'Kelurahan/Desa domisili',
      );
      if (kelurahanDomisiliError != null) {
        this.kelurahanDomisiliError.value = kelurahanDomisiliError;
        isValid = false;
      }
    }

    return isValid;
  }

  void _clearAllErrors() {
    nikError.value = '';
    namaKtpError.value = '';
    tempatLahirError.value = '';
    tanggalLahirError.value = '';
    bulanLahirError.value = '';
    tahunLahirError.value = '';
    alamatKtpError.value = '';
    rtKtpError.value = '';
    rwKtpError.value = '';
    provinsiKtpError.value = '';
    kabupatenKtpError.value = '';
    kecamatanKtpError.value = '';
    kelurahanKtpError.value = '';

    alamatDomisiliError.value = '';
    rtDomisiliError.value = '';
    rwDomisiliError.value = '';
    provinsiDomisiliError.value = '';
    kabupatenDomisiliError.value = '';
    kecamatanDomisiliError.value = '';
    kelurahanDomisiliError.value = '';
  }

  // Setup trim listeners untuk text fields
  void setupTrimListeners() {
    // Trim listeners sudah tidak diperlukan karena trim akan dilakukan
    // pada onEditingComplete di UI atau saat submit form
  }

  // Helper method untuk trim text field saat editing selesai
  void trimTextFieldOnComplete(TextEditingController controller) {
    final text = controller.text;
    final trimmedText = text.trim();

    if (text != trimmedText) {
      controller.text = trimmedText;
      // Set cursor ke akhir text setelah trim
      controller.selection = TextSelection.collapsed(
        offset: trimmedText.length,
      );
    }
  }

  // Method untuk trim semua text fields sebelum validasi
  void _trimAllTextFields() {
    // Trim KTP fields
    nikController.text = nikController.text.trim();
    namaKtpController.text = namaKtpController.text.trim();
    tempatLahirController.text = tempatLahirController.text.trim();
    tanggalLahirController.text = tanggalLahirController.text.trim();
    bulanLahirController.text = bulanLahirController.text.trim();
    tahunLahirController.text = tahunLahirController.text.trim();
    jenisKelaminController.text = jenisKelaminController.text.trim();
    alamatKtpController.text = alamatKtpController.text.trim();
    rtKtpController.text = rtKtpController.text.trim();
    rwKtpController.text = rwKtpController.text.trim();
    provinsiKtpController.text = provinsiKtpController.text.trim();
    kabupatenKtpController.text = kabupatenKtpController.text.trim();
    kecamatanKtpController.text = kecamatanKtpController.text.trim();
    kelurahanKtpController.text = kelurahanKtpController.text.trim();

    // Trim domisili fields
    alamatDomisiliController.text = alamatDomisiliController.text.trim();
    rtDomisiliController.text = rtDomisiliController.text.trim();
    rwDomisiliController.text = rwDomisiliController.text.trim();
    provinsiDomisiliController.text = provinsiDomisiliController.text.trim();
    kabupatenDomisiliController.text = kabupatenDomisiliController.text.trim();
    kecamatanDomisiliController.text = kecamatanDomisiliController.text.trim();
    kelurahanDomisiliController.text = kelurahanDomisiliController.text.trim();
  }

  @override
  void onClose() {
    // Dispose all controllers
    nikController.dispose();
    namaKtpController.dispose();
    tempatLahirController.dispose();
    tanggalLahirController.dispose();
    bulanLahirController.dispose();
    tahunLahirController.dispose();
    jenisKelaminController.dispose();
    alamatKtpController.dispose();
    rtKtpController.dispose();
    rwKtpController.dispose();
    provinsiKtpController.dispose();
    kabupatenKtpController.dispose();
    kecamatanKtpController.dispose();
    kelurahanKtpController.dispose();

    // Alamat Domisili controllers
    alamatDomisiliController.dispose();
    rtDomisiliController.dispose();
    rwDomisiliController.dispose();
    provinsiDomisiliController.dispose();
    kabupatenDomisiliController.dispose();
    kecamatanDomisiliController.dispose();
    kelurahanDomisiliController.dispose();

    super.onClose();
  }

  Future<dynamic> preprocessImage(dynamic file) async {
    final bytes = await ImageFileHelper.getImageBytes(file);
    img.Image? image = img.decodeImage(bytes);
    if (image == null) return file;

    // 1. Grayscale
    image = img.grayscale(image);

    // 2. Adjust contrast
    image = img.adjustColor(image, contrast: 1.5);

    // 4. Resize jika perlu
    image = img.copyResize(image, width: 1200);

    final processedBytes = img.encodeJpg(image, quality: 100);

    if (kIsWeb) {
      // On web, create an XFile from the processed bytes
      return XFile.fromData(
        processedBytes,
        name: 'preprocessed.jpg',
        mimeType: 'image/jpeg',
      );
    } else {
      // On mobile, create a File
      final output = File('${file.parent.path}/preprocessed.jpg');
      await output.writeAsBytes(processedBytes);
      return output;
    }
  }

  /// Show dialog when image quality is poor
  Future<void> showImageQualityDialog(
    List<String> issues,
    dynamic imageFile,
  ) async {
    final qualityResult = await KtpOcrService.checkImageQuality(imageFile);

    await Get.dialog(
      AlertDialog(
        title: const Text('Kualitas Foto Kurang Baik'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Foto KTP yang Anda ambil memiliki masalah berikut:'),
            const SizedBox(height: 8),
            ...issues.map(
              (issue) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [const Text('• '), Expanded(child: Text(issue))],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (qualityResult.improvementSuggestion.isNotEmpty) ...[
              const Text(
                'Saran perbaikan:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(qualityResult.improvementSuggestion),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Tetap Lanjutkan'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              // Navigate back to photo page to retake photo
              retakeKtpPhoto();
            },
            child: const Text('Foto Ulang'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// Navigate back to photo page to retake KTP photo
  void retakeKtpPhoto() async {
    try {
      // Use the verification controller to pick new KTP image
      await baseController.verificationController.pickKtpImage(
        'KTP',
        kPhotoTypeKtp,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Gagal mengambil foto: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
