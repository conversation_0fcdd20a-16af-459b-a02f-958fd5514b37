import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer';

class ApprovalController extends BaseControllers {
  RxString uuid = ''.obs;
  RxString id = ''.obs;
  RxString selectedStatusApproval = ''.obs;
  TextEditingController remarksApproval = TextEditingController();
  String source = '';

  Rx<RecruitmentApiModel> recruitmentDetail = RecruitmentApiModel().obs;

  // Add support for draft data from Firestore
  Rx<RecruitmentFormModel> draftDetail = RecruitmentFormModel().obs;
  RxBool isDraftData = false.obs;

  RxBool isButtonAvailable = false.obs;
  RxBool isSubmitButtonEnabled = false.obs;

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  late SharedPreferences prefs;

  RxString currentRole = ''.obs;
  String currentAgentCode = '';

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    currentRole.value = prefs.getString(kStorageUserLevelComplete) ?? '';

    // Check if we have uuid (API data) or id (draft data)
    uuid.value = Get.arguments['uuid'] ?? '';
    id.value = Get.arguments['id'] ?? '';
    source = Get.arguments['source'] ?? '';

    currentAgentCode = prefs.getString(kStorageAgentCode) ?? '';

    // Determine if this is draft data or API data
    if (id.value.isNotEmpty && uuid.value.isEmpty) {
      isDraftData.value = true;
      log('Loading draft data with ID: ${id.value}');
    } else if (uuid.value.isNotEmpty) {
      isDraftData.value = false;
      log('Loading API data with UUID: ${uuid.value}');
    }

    load();
  }

  @override
  load() {
    super.load();
    setLoading(true);
    loadData();
  }

  loadData() async {
    setLoading(true);

    if (isDraftData.value) {
      // Load draft data from Firestore
      await loadDraftData();
    } else {
      // Load API data
      await api.getRecruitmentDetail(
        controllers: this,
        code: kReqGetRecruitmentDetail,
        uuid: uuid.value,
      );
    }
  }

  // Load draft data from Firestore
  Future<void> loadDraftData() async {
    try {
      log('Loading draft data from Firestore with ID: ${id.value}');
      final formData = await _firestoreService.getRecruitmentForm(id.value);

      if (formData != null) {
        draftDetail.value = formData;
        log('Successfully loaded draft data: ${formData.namaKtp}');
        // Update section completion status after loading data
        updateSectionCompletionStatus();
        setLoading(false);
      } else {
        // log('No draft data found with ID: ${id.value}');
        // setLoading(false);
        // toastError(title: 'Gagal', message: 'Data draft tidak ditemukan');
      }
    } catch (e) {
      log('Error loading draft data: $e');
      setLoading(false);
      toastError(
        title: 'Gagal',
        message: 'Terjadi kesalahan saat memuat data draft',
      );
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqGetRecruitmentDetail:
        parseData(response);
        break;
      case kReqPostApproval:
        loadData();
        break;
      default:
    }
    parseData(response);
  }

  parseData(response) {
    recruitmentDetail.value = RecruitmentApiModel.fromJson(response);
    // Update section completion status after loading data
    updateSectionCompletionStatus();
    if (recruitmentDetail.value.approvalStatus == 'TERTUNDA' ||
        recruitmentDetail.value.approvalStatus == 'DITOLAK') {
      isButtonAvailable.value = false;
      updateSubmitButtonState(); // Update submit button state
      return;
    }

    if (recruitmentDetail.value.approvalHeader?.approverRole ==
            'RECRUITER:$currentAgentCode' ||
        recruitmentDetail.value.approvalHeader?.approverRole ==
            'UPLINE:$currentAgentCode' ||
        _isRoleMatched(
          recruitmentDetail.value.approvalHeader?.approverRole ?? '',
          currentRole.value,
        )) {
      if (recruitmentDetail.value.approvalHeader?.requestBy?.agentCode ==
          currentAgentCode) {
        if (recruitmentDetail.value.approvalHeader?.approverRole!.contains(
              currentAgentCode,
            ) ==
            true) {
          isButtonAvailable.value = true;
          return;
        }
        isButtonAvailable.value = false;
        return;
      }

      isButtonAvailable.value = true;
    } else {
      isButtonAvailable.value = false;
    }

    // Update submit button state after isButtonAvailable is set
    updateSubmitButtonState();
  }

  /// Check if the current user role exists in the comma-separated approver roles
  /// This replaces the problematic contains() method that caused false positives
  /// Example: approverRole = 'ROLE_AGE_BDM,ROLE_AGE_HOS', currentRole = 'ROLE_AGE_BD'
  /// Should return false (not true like contains() would)
  bool _isRoleMatched(String approverRole, String currentRole) {
    if (approverRole.isEmpty || currentRole.isEmpty) {
      return false;
    }

    // Split comma-separated roles and check for exact match
    List<String> approverRoles =
        approverRole.split(',').map((role) => role.trim()).toList();
    return approverRoles.contains(currentRole);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);

    toastError(
      title: 'Gagal',
      message:
          response.body['message'] ??
          '${response.body['error_description'] ?? 'Terjadi Kesalahan harap ulangi kembali'}',
    );
  }

  Future<void> reqPostApproval({required ApprovalBody body}) async {
    setLoading(true);
    await api.postApproval(
      controllers: this,
      code: kReqPostApproval,
      body: body,
    );
  }

  void toastError({required String title, required String message}) {
    Get.snackbar(
      title,
      message,
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Refresh data method
  Future<void> refreshData() async {
    await loadData();
  }

  // Method to be called when returning from form pages to update completion status
  void onReturnFromForm() {
    if (isDraftData.value) {
      // Reload draft data to get latest changes
      loadDraftData();
    } else {
      // For API data, just update the completion status
      updateSectionCompletionStatus();
    }
  }

  // Reactive observables for form section completion status
  RxBool isVerificationSectionCompleted = false.obs;
  RxBool isIdentificationSectionCompleted = false.obs;
  RxBool isSelfIdentificationSectionCompleted = false.obs;
  RxBool isTermsSectionCompleted = false.obs;
  RxBool isInterviewSectionCompleted = false.obs;

  // Update all section completion status
  void updateSectionCompletionStatus() {
    // if (!isDraftData.value) {
    //   // For API data, always show as completed
    //   isVerificationSectionCompleted.value = true;
    //   isIdentificationSectionCompleted.value = true;
    //   isSelfIdentificationSectionCompleted.value = true;
    //   isTermsSectionCompleted.value = true;
    //   return;
    // }

    final draft = draftDetail.value;
    if (isDraftData.value) {
      isVerificationSectionCompleted.value = _isVerificationSectionCompleted(
        draft,
      );
      isIdentificationSectionCompleted
          .value = _isIdentificationSectionCompleted(draft);
      isSelfIdentificationSectionCompleted
          .value = _isSelfIdentificationSectionCompleted(draft);
      isTermsSectionCompleted.value = _isTermsSectionCompleted(draft);
      isInterviewSectionCompleted.value = _isInterviewSectionCompleted(draft);
    } else {
      isVerificationSectionCompleted.value = _isVerificationSectionCompleted(
        draft,
        apiData: recruitmentDetail.value,
      );
      isIdentificationSectionCompleted
          .value = _isIdentificationSectionCompleted(
        draft,
        apiData: recruitmentDetail.value,
      );
      isSelfIdentificationSectionCompleted
          .value = _isSelfIdentificationSectionCompleted(
        draft,
        apiData: recruitmentDetail.value,
      );
      isTermsSectionCompleted.value = _isTermsSectionCompleted(
        draft,
        apiData: recruitmentDetail.value,
      );
      isInterviewSectionCompleted.value = _isInterviewSectionCompleted(
        draft,
        apiData: recruitmentDetail.value,
      );
    }

    // Update submit button state after section completion status is updated
    updateSubmitButtonState();
  }

  // Update submit button enabled state
  void updateSubmitButtonState() {
    // Submit button is enabled if:
    // 1. Basic button is available (isButtonAvailable)
    // 2. For BM/BD candidates: interview form must be completed
    // 3. For BP candidates: no interview requirement

    if (!isButtonAvailable.value) {
      isSubmitButtonEnabled.value = false;
      return;
    }

    // Check if candidate is BM or BD
    if (_isBMOrBDCandidate()) {
      // For BM/BD: require interview completion
      isSubmitButtonEnabled.value = isInterviewSectionCompleted.value;
    } else {
      // For BP: no interview requirement
      isSubmitButtonEnabled.value = true;
    }
  }

  // Check if candidate is BM or BD level
  bool _isBMOrBDCandidate() {
    String candidateLevel = '';

    if (isDraftData.value) {
      candidateLevel = draftDetail.value.candidateLevel ?? '';
    } else {
      candidateLevel = recruitmentDetail.value.positionLevel ?? '';
    }

    return candidateLevel == 'BM' || candidateLevel == 'BD';
  }

  // Check if specific form section is completed for draft data
  bool isFormSectionCompleted(int sectionIndex) {
    switch (sectionIndex) {
      case 0: // Verifikasi Identitas
        return isVerificationSectionCompleted.value;
      case 1: // Data Diri Sesuai KTP
        return isIdentificationSectionCompleted.value;
      case 2: // Kelengkapan Data Pribadi
        return isSelfIdentificationSectionCompleted.value;
      case 3: // Perjanjian Keagenan
        return isTermsSectionCompleted.value;
      case 4: // Hasil Interview
        return isInterviewSectionCompleted.value;
      default:
        return false;
    }
  }

  // Check if Verifikasi Identitas section is completed
  bool _isVerificationSectionCompleted(
    RecruitmentFormModel draft, {
    RecruitmentApiModel? apiData,
  }) {
    if (apiData != null) {
      return (apiData.ktpPhoto?.isNotEmpty == true) &&
          (apiData.selfiePhoto?.isNotEmpty == true) &&
          (apiData.passPhoto?.isNotEmpty == true) &&
          apiData.positionLevel?.isNotEmpty == true &&
          apiData.branch?.branchName?.isNotEmpty == true;
    }
    return (draft.ktpImageUrl?.isNotEmpty == true ||
            draft.ktpImagePath?.isNotEmpty == true) &&
        (draft.selfieKtpImageUrl?.isNotEmpty == true ||
            draft.selfieKtpImagePath?.isNotEmpty == true) &&
        (draft.pasFotoImageUrl?.isNotEmpty == true ||
            draft.pasFotoImagePath?.isNotEmpty == true) &&
        draft.candidateLevel?.isNotEmpty == true &&
        draft.candidateBranch?.isNotEmpty == true;
  }

  // Check if Data Diri Sesuai KTP section is completed
  bool _isIdentificationSectionCompleted(
    RecruitmentFormModel draft, {
    RecruitmentApiModel? apiData,
  }) {
    if (apiData != null) {
      return apiData.nik?.isNotEmpty == true &&
          apiData.fullName?.isNotEmpty == true &&
          apiData.birthPlace?.isNotEmpty == true &&
          apiData.birthDate?.isNotEmpty == true &&
          apiData.gender?.isNotEmpty == true &&
          apiData.ktpAddress?.isNotEmpty == true &&
          apiData.ktpProvince?.isNotEmpty == true &&
          apiData.ktpCity?.isNotEmpty == true &&
          apiData.ktpDistrict?.isNotEmpty == true &&
          apiData.ktpSubDistrict?.isNotEmpty == true &&
          apiData.maritalStatus?.isNotEmpty == true;
    }
    return draft.nik?.isNotEmpty == true &&
        draft.namaKtp?.isNotEmpty == true &&
        draft.tempatLahir?.isNotEmpty == true &&
        draft.tanggalLahir?.isNotEmpty == true &&
        draft.bulanLahir?.isNotEmpty == true &&
        draft.tahunLahir?.isNotEmpty == true &&
        draft.jenisKelamin?.isNotEmpty == true &&
        draft.alamatKtp?.isNotEmpty == true &&
        draft.provinsiKtp?.isNotEmpty == true &&
        draft.kabupatenKtp?.isNotEmpty == true &&
        draft.kecamatanKtp?.isNotEmpty == true &&
        draft.kelurahanKtp?.isNotEmpty == true &&
        draft.maritalStatus?.isNotEmpty == true;
  }

  // Check if Kelengkapan Data Pribadi section is completed
  bool _isSelfIdentificationSectionCompleted(
    RecruitmentFormModel draft, {
    RecruitmentApiModel? apiData,
  }) {
    if (apiData != null) {
      bool basicDataComplete =
          apiData.email?.isNotEmpty == true &&
          apiData.phoneNumber?.isNotEmpty == true &&
          apiData.occupation?.isNotEmpty == true &&
          apiData.emergencyContactName?.isNotEmpty == true &&
          apiData.emergencyContactRelation?.isNotEmpty == true &&
          apiData.emergencyContactPhone?.isNotEmpty == true &&
          apiData.bankAccountName?.isNotEmpty == true &&
          apiData.bankAccountNumber?.isNotEmpty == true &&
          apiData.bank?.bankName?.isNotEmpty == true &&
          apiData.lastJob?.isNotEmpty == true;
      bool jobDataComplete = true;
      bool jobHistoryComplete = true;

      if (apiData.positionLevel != 'BP') {
        // if (apiData.lastJob != kLastJobNotSales) {
        //   jobDataComplete =
        //       (apiData.last2YearProductionData?.isNotEmpty == true) &&
        //       (apiData.lastCompanyManPowerData != null);
        // }
        jobHistoryComplete = apiData.last5YearJobData?.isNotEmpty == true;
      }
      return basicDataComplete && jobDataComplete && jobHistoryComplete;
    } else {
      bool basicDataComplete =
          draft.email?.isNotEmpty == true &&
          draft.nomorHp?.isNotEmpty == true &&
          draft.occupation?.isNotEmpty == true &&
          draft.emergencyNama?.isNotEmpty == true &&
          draft.emergencyHubungan?.isNotEmpty == true &&
          draft.emergencyNomorHp?.isNotEmpty == true &&
          draft.namaPemilikRekening?.isNotEmpty == true &&
          draft.nomorRekening?.isNotEmpty == true &&
          draft.namaBank?.isNotEmpty == true &&
          draft.lastJob?.isNotEmpty == true;
      bool jobDataComplete = true;

      bool jobHistoryComplete = true;
      if (draft.candidateLevel != 'BP') {
        if (draft.lastJob != kLastJobNotSales) {
          jobDataComplete =
              (draft.last2YearProductionData?.isNotEmpty == true) &&
              (draft.lastCompanyManPowerData != null);
        }
        jobHistoryComplete = draft.last5YearJobData?.isNotEmpty == true;
      }

      // last5YearJobData is always required
      return basicDataComplete && jobDataComplete && jobHistoryComplete;
    }

    // Check required fields based on lastJob
  }

  // Check if Perjanjian Keagenan section is completed
  bool _isTermsSectionCompleted(
    RecruitmentFormModel draft, {
    RecruitmentApiModel? apiData,
  }) {
    if (apiData != null) {
      return (apiData.signature?.isNotEmpty == true) &&
          (apiData.paraf?.isNotEmpty == true);
    }
    return (draft.signatureUrl?.isNotEmpty == true ||
            draft.signature?.isNotEmpty == true) &&
        (draft.parafUrl?.isNotEmpty == true || draft.paraf?.isNotEmpty == true);
  }

  // Check if Hasil Interview section is completed
  bool _isInterviewSectionCompleted(
    RecruitmentFormModel draft, {
    RecruitmentApiModel? apiData,
  }) {
    if (apiData != null) {
      return apiData.resultInterview?.isNotEmpty == true;
    }
    // For draft data, we don't have interview data stored in Firestore yet
    return false;
  }
}
