import 'dart:convert';
import 'dart:developer';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/approval_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class FormInterviewController extends BaseControllers {
  // isFromLifeInsurance
  RxInt selectedIsAsuransiJiwa = 3.obs;
  // hasWorkHistory5Years
  RxInt selectedHasWorkHistory5Years = 3.obs;
  // hasProduction2Years
  RxInt selectedHasProduction2Years = 3.obs;
  // meetsManpowerRequirement
  RxInt selectedMeetsManpowerRequirement = 3.obs;
  // hasCompanyAwards
  RxInt selectedHasCompanyAwards = 3.obs;
  // hasTerminationLetter
  RxInt selectedHasTerminationLetter = 3.obs;
  // hasCommitmentLetter
  RxInt selectedHasCommitmentLetter = 3.obs;
  // hasReferralCommitmentLetter
  RxInt selectedHasReferralCommitmentLetter = 3.obs;
  // hasAAJICertification
  RxInt selectedHasAAJICertification = 3.obs;
  // hasExemptionLetter
  RxInt selectedHasExemptionLetter = 3.obs;

  RxBool isFormDisabled = false.obs;
  String uuid = '';

  @override
  void onInit() {
    super.onInit();

    // Get UUID from parameters
    uuid = Get.parameters['uuid'] ?? '';

    // Listen to all form field changes to update form state
    ever(selectedIsAsuransiJiwa, (_) => updateFormState());
    ever(selectedHasWorkHistory5Years, (_) => updateFormState());
    ever(selectedHasProduction2Years, (_) => updateFormState());
    ever(selectedMeetsManpowerRequirement, (_) => updateFormState());
    ever(selectedHasCompanyAwards, (_) => updateFormState());
    ever(selectedHasTerminationLetter, (_) => updateFormState());
    ever(selectedHasCommitmentLetter, (_) => updateFormState());
    ever(selectedHasReferralCommitmentLetter, (_) => updateFormState());
    ever(selectedHasAAJICertification, (_) => updateFormState());
    ever(selectedHasExemptionLetter, (_) => updateFormState());

    // Load data from API if UUID is available
    if (uuid.isNotEmpty) {
      loadInterviewData();
    }

    // Initial form state check
    updateFormState();
  }

  void updateFormState() {
    // Form is disabled if not all fields are filled OR currently submitting
    isFormDisabled.value = isAllFieldsFilled;
  }

  // Check if all form fields are filled (not value 3)
  bool get isAllFieldsFilled {
    return selectedIsAsuransiJiwa.value != 3 &&
        selectedHasWorkHistory5Years.value != 3 &&
        selectedHasProduction2Years.value != 3 &&
        selectedMeetsManpowerRequirement.value != 3 &&
        selectedHasCompanyAwards.value != 3 &&
        selectedHasTerminationLetter.value != 3 &&
        selectedHasCommitmentLetter.value != 3 &&
        selectedHasReferralCommitmentLetter.value != 3 &&
        selectedHasAAJICertification.value != 3 &&
        selectedHasExemptionLetter.value != 3;
  }

  // Check if form should be enabled (all fields filled and not currently submitting)
  bool get isFormEnabled {
    return isAllFieldsFilled && !isFormDisabled.value;
  }

  // Convert RxInt values to boolean (0 = true, 1 = false)
  Map<String, dynamic> get formData => {
    "isFromLifeInsurance": selectedIsAsuransiJiwa.value == 0,
    "hasWorkHistory5Years": selectedHasWorkHistory5Years.value == 0,
    "hasProduction2Years": selectedHasProduction2Years.value == 0,
    "meetsManpowerRequirement": selectedMeetsManpowerRequirement.value == 0,
    "hasCompanyAwards": selectedHasCompanyAwards.value == 0,
    "hasTerminationLetter": selectedHasTerminationLetter.value == 0,
    "hasCommitmentLetter": selectedHasCommitmentLetter.value == 0,
    "hasReferralCommitmentLetter":
        selectedHasReferralCommitmentLetter.value == 0,
    "hasAAJICertification": selectedHasAAJICertification.value == 0,
    "hasExemptionLetter": selectedHasExemptionLetter.value == 0,
  };

  // Load interview data from API
  void loadInterviewData() async {
    setLoading(true);
    await api.getRecruitmentDetail(
      controllers: this,
      code: kReqGetRecruitmentDetail,
      uuid: uuid,
    );
  }

  // Parse interview data from API response
  void parseInterviewData(String? resultInterview) {
    if (resultInterview == null || resultInterview.isEmpty) {
      return;
    }

    try {
      final Map<String, dynamic> interviewData = jsonDecode(resultInterview);

      // Parse each field and convert boolean to RxInt (true = 0, false = 1)
      selectedIsAsuransiJiwa.value =
          (interviewData['isFromLifeInsurance'] == true) ? 0 : 1;
      selectedHasWorkHistory5Years.value =
          (interviewData['hasWorkHistory5Years'] == true) ? 0 : 1;
      selectedHasProduction2Years.value =
          (interviewData['hasProduction2Years'] == true) ? 0 : 1;
      selectedMeetsManpowerRequirement.value =
          (interviewData['meetsManpowerRequirement'] == true) ? 0 : 1;
      selectedHasCompanyAwards.value =
          (interviewData['hasCompanyAwards'] == true) ? 0 : 1;
      selectedHasTerminationLetter.value =
          (interviewData['hasTerminationLetter'] == true) ? 0 : 1;
      selectedHasCommitmentLetter.value =
          (interviewData['hasCommitmentLetter'] == true) ? 0 : 1;
      selectedHasReferralCommitmentLetter.value =
          (interviewData['hasReferralCommitmentLetter'] == true) ? 0 : 1;
      selectedHasAAJICertification.value =
          (interviewData['hasAAJICertification'] == true) ? 0 : 1;
      selectedHasExemptionLetter.value =
          (interviewData['hasExemptionLetter'] == true) ? 0 : 1;

      updateFormState();
    } catch (e) {
      log('Error parsing interview data: $e');
    }
  }

  performSubmitInterview() async {
    if (!isAllFieldsFilled) {
      return;
    }
    setLoading(true);
    updateFormState(); // This will disable the form during loading
    await api.performSubmitInterview(
      controllers: this,
      data: formData,
      uuid: uuid,
      code: kReqSubmitInterview,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    if (requestCode == kReqGetRecruitmentDetail) {
      // Parse interview data from API response
      setLoading(false);
      parseInterviewData(response['resultInterview']);
    } else if (requestCode == kReqSubmitInterview) {
      // Show success popup

      Get.back();

      Utils.popup(
        body: 'Data interview berhasil disimpan',
        type: kPopupSuccess,
      );

      // Refresh data in approval page
      if (Get.isRegistered<ApprovalController>()) {
        final approvalController = Get.find<ApprovalController>();
        approvalController.refreshData();
      }

      setLoading(false);
    }

    updateFormState(); // Update form state after loading completes
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    updateFormState(); // Update form state after loading completes
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    setLoading(false);
    updateFormState(); // Update form state after loading completes
  }
}
