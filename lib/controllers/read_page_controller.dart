import 'dart:convert';

import 'package:fleather/fleather.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/info_models.dart';
import 'package:pdl_superapp/utils/keys.dart';

class ReadPageController extends BaseControllers {
  Rx<InfoModels> infoData = Rx(InfoModels());

  late FleatherController jsonController;

  RxBool fleatherReady = false.obs;
  performGetInformation(String infoType) async {
    await api.getInformation(
      controllers: this,
      infoType: infoType,
      code: kReqGetInfo,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseData(response);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  parseData(response) {
    if (response['content'].isNotEmpty) {
      InfoModels data = InfoModels.fromJson(response['content'][0]);
      infoData.value = data;
      setUpContent();
    }
  }

  setUpContent() {
    ParchmentDocument document = ParchmentDocument.fromJson(
      jsonDecode(infoData.value.content ?? ''),
    );
    jsonController = FleatherController(document: document);
    fleatherReady.value = true;
  }
}
