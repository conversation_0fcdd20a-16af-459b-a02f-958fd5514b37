import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AgentDetailController extends BaseControllers {
  // Agent profile data
  Rx<UserModels?> agentProfile = Rx<UserModels?>(null);

  // Persistency data
  Rx<PersistensiModel?> individuData = Rx<PersistensiModel?>(null);
  RxList<PersistensiModel> teamData = <PersistensiModel>[].obs;

  // Loading states
  RxBool isLoadingProfile = false.obs;
  RxBool isLoadingIndividu = false.obs;
  RxBool isLoadingTeam = false.obs;

  // Error states
  RxBool hasProfileError = false.obs;
  RxBool hasIndividuError = false.obs;
  RxBool hasTeamError = false.obs;

  RxString profileErrorMessage = ''.obs;
  RxString individuErrorMessage = ''.obs;
  RxString teamErrorMessage = ''.obs;

  late SharedPreferences prefs;
  String agentCode = '';
  String currentUserAgentCode = '';

  @override
  void onInit() {
    super.onInit();
    agentCode = Get.parameters['agentCode'] ?? '';
    if (agentCode.isNotEmpty) {
      _initializeData();
    }
  }

  Future<void> _initializeData() async {
    prefs = await SharedPreferences.getInstance();
    currentUserAgentCode = prefs.getString(kStorageAgentCode) ?? '';

    // Load all data
    await Future.wait([loadAgentProfile(), loadIndividuData(), loadTeamData()]);
  }

  // Load agent profile using simple profile API
  Future<void> loadAgentProfile() async {
    isLoadingProfile.value = true;
    hasProfileError.value = false;
    profileErrorMessage.value = '';

    try {
      await api.getSimpleProfile(
        controllers: this,
        agentCode: agentCode,
        code: kReqGetProfile,
      );
    } catch (e) {
      hasProfileError.value = true;
      profileErrorMessage.value = 'Error loading profile: $e';
      isLoadingProfile.value = false;
    }
  }

  // Load individual persistency data for this agent
  Future<void> loadIndividuData() async {
    isLoadingIndividu.value = true;
    hasIndividuError.value = false;
    individuErrorMessage.value = '';

    try {
      final currentYear = DateTime.now().year.toString();
      await api.getPersistencyIndividu(
        controllers: this,
        params: "agentCode=$agentCode&year=$currentYear",
        code: kReqGetPersistencyIndividu,
      );
    } catch (e) {
      hasIndividuError.value = true;
      individuErrorMessage.value = 'Error loading individual data: $e';
      isLoadingIndividu.value = false;
    }
  }

  // Load team persistency data for this agent
  Future<void> loadTeamData() async {
    isLoadingTeam.value = true;
    hasTeamError.value = false;
    teamErrorMessage.value = '';

    try {
      final currentYear = DateTime.now().year.toString();
      await api.getPersistencyTeam(
        controllers: this,
        params: "agentCode=$agentCode&year=$currentYear&page=0&size=50",
        code: kReqGetPersistencyTeam,
      );
    } catch (e) {
      hasTeamError.value = true;
      teamErrorMessage.value = 'Error loading team data: $e';
      isLoadingTeam.value = false;
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqGetProfile:
        _parseProfileData(response);
        break;
      case kReqGetPersistencyIndividu:
        _parseIndividuData(response);
        break;
      case kReqGetPersistencyTeam:
        _parseTeamData(response);
        break;
    }
  }

  void _parseProfileData(response) {
    if (response != null) {
      agentProfile.value = UserModels.fromJson(response);
    }
    isLoadingProfile.value = false;
  }

  void _parseIndividuData(response) {
    if (response != null) {
      individuData.value = PersistensiModel.fromJson(response);
    }
    isLoadingIndividu.value = false;
  }

  void _parseTeamData(response) {
    if (response['content'] != null) {
      final List<dynamic> content = response['content'];
      teamData.assignAll(
        content.map((item) => PersistensiModel.fromJson(item)).toList(),
      );
    }
    isLoadingTeam.value = false;
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);

    switch (requestCode) {
      case kReqGetProfile:
        hasProfileError.value = true;
        profileErrorMessage.value = 'Failed to load profile';
        isLoadingProfile.value = false;
        break;
      case kReqGetPersistencyIndividu:
        hasIndividuError.value = true;
        individuErrorMessage.value = 'Failed to load individual data';
        isLoadingIndividu.value = false;
        break;
      case kReqGetPersistencyTeam:
        hasTeamError.value = true;
        teamErrorMessage.value = 'Failed to load team data';
        isLoadingTeam.value = false;
        break;
    }
  }

  String formatPercentage(double value) {
    return "${(value * 100).toStringAsFixed(2)}%";
  }

  // Refresh all data
  void refreshData() {
    _initializeData();
  }
}
