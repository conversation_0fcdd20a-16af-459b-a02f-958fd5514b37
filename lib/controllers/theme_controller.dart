import 'dart:convert';
import 'dart:developer' as dev;

import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/theme_models.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeController extends BaseControllers {
  // Observable theme model that can be accessed from anywhere
  final Rx<ThemeModel> currentTheme = ThemeModel.empty().obs;

  // Flag to track if theme is loaded
  final RxBool isThemeLoaded = false.obs;

  // Cache key for storing theme data
  static const String _themeDataKey = 'current_theme_data';

  @override
  void onInit() {
    super.onInit();
    loadCachedThemeData();
    fetchCurrentTheme();
  }

  /// Fetch current theme from API
  Future<void> fetchCurrentTheme() async {
    setLoading(true);
    try {
      await api.getTheme(controllers: this);
    } catch (e) {
      _logError('Error fetching current theme: $e');
      setLoading(false);
    }
  }

  /// Load cached theme data from SharedPreferences
  Future<void> loadCachedThemeData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_themeDataKey);

      if (cachedData != null && cachedData.isNotEmpty) {
        try {
          final jsonData = cachedData;
          currentTheme.value = ThemeModel.fromJson(jsonDecode(jsonData));
          isThemeLoaded.value = true;
          _log('Loaded cached theme data: ${currentTheme.value.name}');
        } catch (e) {
          _logError('Error parsing cached theme data: $e');
        }
      }
    } catch (e) {
      _logError('Error loading cached theme data: $e');
    }
  }

  /// Cache theme data in SharedPreferences
  Future<void> cacheThemeData(ThemeModel themeData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonData = jsonEncode(themeData.toJson());
      await prefs.setString(_themeDataKey, jsonData);
      _log('Theme data cached successfully');
    } catch (e) {
      _logError('Error caching theme data: $e');
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    try {
      final themeData = ThemeModel.fromJson(response);
      currentTheme.value = themeData;
      isThemeLoaded.value = true;

      // Cache the theme data
      cacheThemeData(themeData);

      _log('Theme loaded successfully: ${themeData.name}');
    } catch (e) {
      _logError('Error parsing theme data: $e');
    }

    setLoading(false);
  }

  /// Get asset URL by filename
  String getAssetUrl(String fileName) {
    try {
      final asset = currentTheme.value.assets.firstWhere(
        (asset) => asset.fileName == fileName,
        orElse: () => ThemeAssetModel(id: 0, url: '', fileName: ''),
      );

      return asset.url;
    } catch (e) {
      _logError('Error getting asset URL for $fileName: $e');
      return '';
    }
  }

  /// Log message using LoggerService if available
  void _log(String message) {
    try {
      Get.find<LoggerService>().log('[ThemeController] $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[ThemeController] $message');
    }
  }

  /// Log error using LoggerService if available
  void _logError(String message) {
    try {
      Get.find<LoggerService>().log('[ThemeController] ERROR: $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[ThemeController] ERROR: $message');
    }
  }
}
