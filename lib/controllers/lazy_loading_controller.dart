import 'dart:async';
import 'dart:developer';
import 'package:get/get.dart';

// Event untuk trigger loading widget
class WidgetLoadEvent {
  final String widgetKey;
  final String widgetType;

  WidgetLoadEvent(this.widgetKey, this.widgetType);
}

/// Controller untuk mengelola lazy loading dari widget-widget di home page
/// Mengatur kapan widget harus mulai loading data berdasarkan visibility
/// dan mengimplementasikan sequential loading untuk menghindari overload
class LazyLoadingController extends GetxController {
  // Map untuk menyimpan loading state setiap widget
  final RxMap<String, bool> _widgetLoadingStates = <String, bool>{}.obs;

  // Map untuk menyimpan visibility state setiap widget
  final RxMap<String, bool> _widgetVisibilityStates = <String, bool>{}.obs;

  // Map untuk menyimpan apakah widget sudah pernah di-load
  final RxMap<String, bool> _widgetLoadedStates = <String, bool>{}.obs;

  // Queue untuk sequential loading
  final List<String> _loadingQueue = [];

  // Flag untuk mencegah multiple sequential loading
  bool _isSequentialLoadingActive = false;

  // Delay antara loading widget (dalam milliseconds)
  final int loadingDelay = 500;

  // Timer untuk debouncing visibility changes
  Timer? _visibilityTimer;

  // Stream controller untuk widget load events
  final StreamController<WidgetLoadEvent> _loadEventController =
      StreamController<WidgetLoadEvent>.broadcast();

  @override
  void onInit() {
    super.onInit();
    log('LazyLoadingController initialized');
  }

  /// Mendaftarkan widget untuk lazy loading
  void registerWidget(String widgetKey) {
    if (!_widgetLoadingStates.containsKey(widgetKey)) {
      _widgetLoadingStates[widgetKey] = false;
      _widgetVisibilityStates[widgetKey] = false;
      _widgetLoadedStates[widgetKey] = false;
      log('Registered widget for lazy loading: $widgetKey');
    }
  }

  /// Menandai widget sebagai visible dan memulai loading jika belum pernah di-load
  void setWidgetVisible(String widgetKey, bool isVisible) {
    // Pastikan widget sudah terdaftar sebelum mengubah visibility
    if (!_widgetVisibilityStates.containsKey(widgetKey)) {
      // Jika belum terdaftar, daftarkan dulu
      registerWidget(widgetKey);
    }

    _widgetVisibilityStates[widgetKey] = isVisible;

    if (isVisible && !_widgetLoadedStates[widgetKey]!) {
      log('Widget $widgetKey became visible, adding to loading queue');
      _addToLoadingQueue(widgetKey);
    }
  }

  /// Menambahkan widget ke queue untuk sequential loading
  void _addToLoadingQueue(String widgetKey) {
    if (!_loadingQueue.contains(widgetKey)) {
      _loadingQueue.add(widgetKey);
      // Process queue asynchronously untuk menghindari blocking UI
      Future.microtask(() => _processLoadingQueue());
    }
  }

  /// Memproses queue loading secara sequential
  void _processLoadingQueue() async {
    if (_isSequentialLoadingActive || _loadingQueue.isEmpty) {
      return;
    }

    _isSequentialLoadingActive = true;
    while (_loadingQueue.isNotEmpty) {
      final widgetKey = _loadingQueue.removeAt(0);

      // Skip jika widget sudah di-load atau tidak visible lagi
      if (_widgetLoadedStates[widgetKey]! ||
          !_widgetVisibilityStates[widgetKey]!) {
        continue;
      }

      log('Became Starting to load widget: $widgetKey');
      await _loadWidget(widgetKey);

      // Delay sebelum loading widget berikutnya
      if (_loadingQueue.isNotEmpty) {
        await Future.delayed(Duration(milliseconds: loadingDelay));
      }
    }

    _isSequentialLoadingActive = false;
  }

  /// Memulai loading untuk widget tertentu
  Future<void> _loadWidget(String widgetKey) async {
    try {
      _widgetLoadingStates[widgetKey] = true;

      // Trigger loading berdasarkan widget key
      await _triggerWidgetLoad(widgetKey);

      _widgetLoadedStates[widgetKey] = true;
      log('became Widget $widgetKey loaded successfully');
    } catch (e) {
      log('became Error loading widget $widgetKey: $e');
    } finally {
      _widgetLoadingStates[widgetKey] = false;
    }
  }

  /// Trigger loading untuk widget controller yang sesuai
  Future<void> _triggerWidgetLoad(String widgetKey) async {
    switch (widgetKey) {
      case 'birthday_widget':
        await _loadBirthdayWidget();
        break;
      case 'claim_widget':
        await _loadClaimWidget();
        break;
      case 'polis_lapse_widget':
        await _loadPolisLapseWidget();
        break;
      case 'polis_jatuh_tempo_widget':
        await _loadPolisJatuhTempoWidget();
        break;
      case 'production_widget':
        await _loadProductionWidget();
        break;
      case 'validasi_widget':
        await _loadValidasiWidget();
        break;
      case 'kompensasi_widget':
        await _loadKompensasiWidget();
        break;
      case 'persistensi_widget':
        await _loadPersistensiWidget();
        break;
      case 'spaj_widget':
        await _loadSpajWidget();
        break;
      default:
        log('Unknown widget key: $widgetKey');
    }
  }

  /// Load methods untuk setiap widget
  Future<void> _loadBirthdayWidget() async {
    try {
      // Emit event untuk birthday widget
      _loadEventController.add(WidgetLoadEvent('birthday_widget', 'birthday'));
      log('became Triggered birthday widget loading event');
    } catch (e) {
      log('Error loading birthday widget: $e');
    }
  }

  Future<void> _loadClaimWidget() async {
    try {
      _loadEventController.add(WidgetLoadEvent('claim_widget', 'claim'));
      log('Triggered claim widget loading event');
    } catch (e) {
      log('Error loading claim widget: $e');
    }
  }

  Future<void> _loadPolisLapseWidget() async {
    try {
      _loadEventController.add(
        WidgetLoadEvent('polis_lapse_widget', 'polis_lapse'),
      );
      log('Triggered polis lapse widget loading event');
    } catch (e) {
      log('Error loading polis lapse widget: $e');
    }
  }

  Future<void> _loadPolisJatuhTempoWidget() async {
    try {
      _loadEventController.add(
        WidgetLoadEvent('polis_jatuh_tempo_widget', 'polis_jatuh_tempo'),
      );
      log('Triggered polis jatuh tempo widget loading event');
    } catch (e) {
      log('Error loading polis jatuh tempo widget: $e');
    }
  }

  Future<void> _loadProductionWidget() async {
    try {
      _loadEventController.add(
        WidgetLoadEvent('production_widget', 'production'),
      );
      log('Triggered production widget loading event');
    } catch (e) {
      log('Error loading production widget: $e');
    }
  }

  Future<void> _loadValidasiWidget() async {
    try {
      _loadEventController.add(WidgetLoadEvent('validasi_widget', 'validasi'));
      log('Triggered validasi widget loading event');
    } catch (e) {
      log('Error loading validasi widget: $e');
    }
  }

  Future<void> _loadKompensasiWidget() async {
    try {
      _loadEventController.add(
        WidgetLoadEvent('kompensasi_widget', 'kompensasi'),
      );
      log('Triggered kompensasi widget loading event');
    } catch (e) {
      log('Error loading kompensasi widget: $e');
    }
  }

  Future<void> _loadPersistensiWidget() async {
    try {
      _loadEventController.add(
        WidgetLoadEvent('persistensi_widget', 'persistensi'),
      );
      log('Triggered persistensi widget loading event');
    } catch (e) {
      log('Error loading persistensi widget: $e');
    }
  }

  Future<void> _loadSpajWidget() async {
    try {
      _loadEventController.add(WidgetLoadEvent('spaj_widget', 'spaj'));
      log('Triggered spaj widget loading event');
    } catch (e) {
      log('Error loading spaj widget: $e');
    }
  }

  /// Getter untuk loading state
  bool isWidgetLoading(String widgetKey) {
    return _widgetLoadingStates[widgetKey] ?? false;
  }

  /// Getter untuk loaded state
  bool isWidgetLoaded(String widgetKey) {
    return _widgetLoadedStates[widgetKey] ?? false;
  }

  /// Getter untuk visibility state
  bool isWidgetVisible(String widgetKey) {
    return _widgetVisibilityStates[widgetKey] ?? false;
  }

  /// Force reload widget
  Future<void> forceReloadWidget(String widgetKey) async {
    _widgetLoadedStates[widgetKey] = false;
    await _loadWidget(widgetKey);
  }

  /// Reset semua state (untuk refresh)
  void resetAllStates() {
    _widgetLoadingStates.clear();
    _widgetVisibilityStates.clear();
    _widgetLoadedStates.clear();
    _loadingQueue.clear();
    _isSequentialLoadingActive = false;
  }

  /// Getter untuk load event stream
  Stream<WidgetLoadEvent> get loadEventStream => _loadEventController.stream;

  @override
  void onClose() {
    _visibilityTimer?.cancel();
    _loadEventController.close();
    super.onClose();
  }
}
