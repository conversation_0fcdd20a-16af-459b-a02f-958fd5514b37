import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:pdl_superapp/controllers/main_controller.dart';
import 'package:pdl_superapp/controllers/profile/setting_controller.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/models/user_models.dart';
import 'package:pdl_superapp/models/widget_api_model.dart';
import 'package:uuid/uuid.dart';
import 'package:pdl_superapp/utils/import_helper_web/import_helper.dart';

import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>/kompensasi_widget.dart';
import 'package:pdl_superapp/pages/home/<USER>/validasi_widget.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/utils/analytics_utils.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeController extends BaseControllers {
  late SharedPreferences prefs;

  // for UI
  Rx<UserModels> userData = Rx(UserModels());

  // Favorite widgets
  final RxList<String> favoriteWidgetIds = RxList();
  final RxList<Widget> favoriteWidgets = RxList();

  // API widget data
  final RxList<WidgetApiModel> apiWidgets = RxList();
  final RxList<String> availableWidgetKeys = RxList();

  RxString userLevel = ''.obs;
  RxString userChannel = ''.obs;

  // Loading state for UI
  @override
  RxBool isLoading = true.obs;

  final settingC = Get.put(SettingController());
  final MainController mainC = Get.find();

  @override
  void onInit() async {
    super.onInit();
    _setLoadingState(true); // Set loading to true on initialization
    prefs = await SharedPreferences.getInstance();

    // Load user level from preferences first
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    userChannel.value = prefs.getString(kStorageUserChannel) ?? '';

    // Load cached user data if available to show immediately
    await _loadCachedUserData();

    // Progressive loading: Load cached favorite widgets immediately
    // This will show cached content while other initialization happens
    await _loadFavoriteWidgetsFromCache();

    // Clean up any duplicate favorite widgets in storage
    await cleanupDuplicateFavoriteWidgets();

    // Check if app was restarted due to Shorebird update
    final wasRestarted = prefs.getBool('shorebird_restart_flag') ?? false;
    if (wasRestarted) {
      log('App was restarted due to Shorebird update, adding extra delay');
      // Clear the flag
      await prefs.setBool('shorebird_restart_flag', false);
      // Add extra delay for iOS after Shorebird restart
      await Future.delayed(const Duration(milliseconds: 2000));
    } else {
      // Reduced delay for faster startup
      await Future.delayed(const Duration(milliseconds: 200));
    }

    // Ensure ConfigReader is initialized before making API calls
    try {
      await ConfigReader.initialize();
    } catch (e) {
      log('ConfigReader re-initialization failed: $e');
    }

    await registerDevice();
  }

  /// Load cached user data from SharedPreferences
  Future<void> _loadCachedUserData() async {
    try {
      // Load basic user info from SharedPreferences to show immediately
      String agentName = prefs.getString(kStorageAgentName) ?? '';
      String agentCode = prefs.getString(kStorageAgentCode) ?? '';
      String userLevelStr = prefs.getString(kStorageUserLevel) ?? '';
      String userChannelStr = prefs.getString(kStorageUserChannel) ?? '';
      String userId = prefs.getString(kStorageUserId) ?? '';

      if (agentName.isNotEmpty || agentCode.isNotEmpty) {
        // Create a basic UserModels object with cached data
        userData.value = UserModels(
          agentName: agentName.isNotEmpty ? agentName : null,
          agentCode: agentCode.isNotEmpty ? agentCode : null,
          level: userLevelStr.isNotEmpty ? userLevelStr : null,
          channel: userChannelStr.isNotEmpty ? userChannelStr : null,
          id: userId.isNotEmpty ? int.tryParse(userId) : null,
        );
        log('Loaded cached user data successfully: $agentName');
        // Don't set isLoading to false here, wait for fresh data
      }
    } catch (e) {
      log('Error loading cached user data: $e');
    }
  }

  /// Load fresh data from API in proper sequence
  Future<void> _loadFreshData() async {
    try {
      log('Starting fresh data load sequence');

      // First load profile data (this is critical for user info)
      // This is awaited to ensure user data is loaded before widgets
      await api.getProfile(
        controllers: this,
        code: kReqGetProfile,
        debug: true,
      );

      log('Profile data loaded, now loading widget sort');

      // Then load widget sort data (not awaited to allow parallel loading)
      // This will be handled by its own success/failure callbacks
      String channel = prefs.getString(kStorageUserChannel) ?? '';
      api.getWidgetSort(controllers: this, channel: channel);
    } catch (e) {
      log('Error loading fresh data: $e');
      // If API fails, at least show cached data
      _setLoadingState(false);
    }
  }

  @override
  void onReady() async {
    super.onReady();
    // Add additional delay for iOS after potential Shorebird restart
    if (isIOS) {
      await Future.delayed(const Duration(milliseconds: 1000));
    }

    // Check for updates after UI is fully ready
    if (!kIsWeb) {
      await Utils.checkForUpdates();
    }
  }

  loadData() async {
    try {
      // Progressive loading: Load cached data first
      await _loadFavoriteWidgetsFromCache();

      // Load fresh data from API in sequence to avoid race conditions
      await _loadFreshData();

      userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
      userChannel.value = prefs.getString(kStorageUserChannel) ?? '';
    } catch (e) {
      log('Error in loadData: $e');
      _setLoadingState(false);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) async {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqAddDevices:
        // Load data after device is registered
        _setLoadingState(true);
        loadData();
        break;
      case kReqGetProfile:
        log('Profile API success, parsing data');
        // parse data user
        await parseData(response);
        // Load favorite widgets after profile is parsed
        loadFavoriteWidgets();
        // isLoading.value is set to false in parseData
        break;
      case 0: // Default code for getWidgetSort
        log('Widget sort API success, parsing widget data');
        await parseWidgetSortData(response);
        break;
      default:
        log('Unknown request code: $requestCode');
        _setLoadingState(
          false,
        ); // Ensure loading is set to false for other cases
    }
  }

  @override
  load() async {
    super.load();
    _setLoadingState(true); // Set loading to true before API call

    try {
      // Progressive loading: Load favorite widgets from cache first
      await _loadFavoriteWidgetsFromCache();

      // Load fresh data from API in sequence
      await _loadFreshData();

      // Check for favorite widget changes and reload if needed
      await checkForFavoriteWidgetChanges();

      // Force reload favorite widgets from Firestore
      await _forceReloadFavoriteWidgets();
    } catch (e) {
      log('Error in load method: $e');
      _setLoadingState(
        false,
      ); // Ensure loading is set to false if API call fails
    }
  }

  // Load favorite widgets from cache immediately for faster UI response
  Future<void> _loadFavoriteWidgetsFromCache() async {
    try {
      // Load from cache first to show UI immediately
      List<String>? cachedIds = await _loadFromFirestore();

      if (cachedIds != null && cachedIds.isNotEmpty) {
        log('Loading favorite widgets from cache for immediate display');
        _updateFavoriteWidgets(cachedIds);
        // Don't set loading to false here if we're still loading fresh data
        // Only set to false if this is the only data we have
        if (userData.value.agentName != null &&
            userData.value.agentName!.isNotEmpty) {
          _setLoadingState(false);
        }
      } else {
        // If no cached data, try SharedPreferences as fallback
        log('No Firestore cache, trying SharedPreferences');
        List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];
        // Use saved IDs even if empty (allows 0 favorite widgets)
        log(
          'Using SharedPreferences favorite widgets (${savedIds.length} widgets)',
        );
        _updateFavoriteWidgets(savedIds);
      }
    } catch (e) {
      log('Error loading favorite widgets from cache: $e');
      // Fallback to empty widgets on error (allows 0 favorite widgets)
      _updateFavoriteWidgets([]);
    }
  }

  /// Centralized method to set loading state consistently
  void _setLoadingState(bool loading) {
    isLoading.value = loading;
    setLoading(loading);
  }

  // Force reload favorite widgets from Firestore
  Future<void> _forceReloadFavoriteWidgets() async {
    try {
      // Clear the last known list to force a reload
      _lastKnownFavoriteIds.clear();

      // Load favorite widgets
      loadFavoriteWidgets();
    } catch (e) {
      log('Error reloading favorite widgets: $e');
    }
  }

  // Store the last known list of favorite widget IDs for comparison
  List<String> _lastKnownFavoriteIds = [];

  // Check if favorite widgets have changed in SharedPreferences
  Future<void> checkForFavoriteWidgetChanges() async {
    try {
      List<String> currentIds = prefs.getStringList('favorite_widgets') ?? [];

      log(
        'HomeController checking favorite widget changes: last known: $_lastKnownFavoriteIds, current: $currentIds',
      );

      // Check if the list has changed
      if (!_areListsEqual(_lastKnownFavoriteIds, currentIds)) {
        log('HomeController detected favorite widget changes, reloading');
        // Update the last known list
        _lastKnownFavoriteIds = List.from(currentIds);

        // Reload the widgets
        loadFavoriteWidgets();
      } else {
        log('HomeController: No changes in favorite widgets');
      }
    } catch (e) {
      log('Error checking for favorite widget changes: $e');
    }
  }

  // Helper method to compare two lists
  bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  // Load favorite widgets from Firestore and SharedPreferences
  void loadFavoriteWidgets() async {
    try {
      // First try to get from Firestore
      List<String>? firestoreIds = await _loadFromFirestore();

      // If Firestore data is available, use it
      if (firestoreIds != null && firestoreIds.isNotEmpty) {
        log('Using favorite widgets from Firestore');

        // Also update SharedPreferences to keep them in sync
        await prefs.setStringList('favorite_widgets', firestoreIds);

        _updateFavoriteWidgets(firestoreIds);
        return;
      }

      // Otherwise, fall back to SharedPreferences
      log('Falling back to SharedPreferences for favorite widgets');
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];

      // Use saved IDs even if empty (allows 0 favorite widgets)
      log('Using saved favorite widgets (${savedIds.length} widgets)');

      // Update widgets with the local data
      _updateFavoriteWidgets(savedIds);
    } catch (e) {
      log('Error loading favorite widgets: $e');

      // In case of error, try to use local data
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];

      // Use saved IDs even if empty (allows 0 favorite widgets)
      log('Using fallback favorite widgets (${savedIds.length} widgets)');
      _updateFavoriteWidgets(savedIds);
    }
  }

  // Update favorite widgets with the given IDs
  void _updateFavoriteWidgets(List<String> ids) {
    // Remove duplicates from the input list while preserving order
    List<String> deduplicatedIds = [];
    Set<String> seenIds = <String>{};

    for (String id in ids) {
      if (!seenIds.contains(id)) {
        deduplicatedIds.add(id);
        seenIds.add(id);
      }
    }

    // Log if duplicates were found and removed
    if (deduplicatedIds.length != ids.length) {
      log(
        'Removed ${ids.length - deduplicatedIds.length} duplicate widget IDs',
      );
      log('Original IDs: $ids');
      log('Deduplicated IDs: $deduplicatedIds');
    }

    // Update the last known list
    _lastKnownFavoriteIds = List.from(deduplicatedIds);

    // Update the favoriteWidgetIds list
    favoriteWidgetIds.value = deduplicatedIds;

    // Generate widgets based on IDs
    generateFavoriteWidgets();
  }

  // Load favorite widgets from Firestore
  Future<List<String>?> _loadFromFirestore() async {
    try {
      // Get FirestoreServices instance
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
      } catch (e) {
        log('FirestoreServices not found, creating new instance');
        firestoreServices = Get.put(FirestoreServices());
      }

      // Get favorite widgets from Firestore
      return await firestoreServices.getFavoriteWidgets();
    } catch (e) {
      log('Error loading from Firestore: $e');
      return null;
    }
  }

  /// Parse widget sort data from API response
  Future<void> parseWidgetSortData(dynamic response) async {
    try {
      if (response is List) {
        // Parse the API response into WidgetApiModel objects
        apiWidgets.clear();
        availableWidgetKeys.clear();

        for (var item in response) {
          WidgetApiModel widget = WidgetApiModel.fromJson(item);
          if (widget.isActive) {
            apiWidgets.add(widget);
            availableWidgetKeys.add(widget.widgetKey);
          }
        }

        // Sort by ordering
        apiWidgets.sort((a, b) => a.ordering.compareTo(b.ordering));

        log('Parsed ${apiWidgets.length} active widgets from API');
        log('Available widget keys: ${availableWidgetKeys.join(', ')}');

        // Regenerate favorite widgets with API filtering
        generateFavoriteWidgets();
      }
    } catch (e) {
      log('Error parsing widget sort data: $e');
    }
  }

  // Generate HomeWidgetBase widgets based on favoriteWidgetIds with lazy loading
  void generateFavoriteWidgets() {
    favoriteWidgets.clear();

    // Map of widget IDs to their corresponding widgets with lazy loading
    Map<String, Widget> widgetMap = {
      '1': HomeWidgetBase(
        iconUrl: 'icon/widget-ulang-tahun-nasabah.svg',
        title: 'customer_birthday_str',
        widgetKey: kWidgetKeyUlangTahunNasabah,
        content: LazyLoadingWidget(
          widgetKey: 'birthday_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'birthday_widget',
            title: 'customer_birthday_str'.tr,
          ),
          child: BirthdayWidget(),
        ),
      ),
      '2': HomeWidgetBase(
        iconUrl: 'icon/widget-status-klaim.svg',
        title: 'claim_status_str',
        widgetKey: kWidgetKeyStatusKlaim,
        content: LazyLoadingWidget(
          widgetKey: 'claim_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'claim_widget',
            title: 'claim_status_str'.tr,
          ),
          child: ClaimWidget(),
        ),
      ),
      '3': HomeWidgetBase(
        iconUrl: 'icon/widget-polis-lapse.svg',
        title: 'lapsed_policy_str',
        widgetKey: kWidgetKeyPolisLapse,
        content: LazyLoadingWidget(
          widgetKey: 'polis_lapse_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'polis_lapse_widget',
            title: 'lapsed_policy_str'.tr,
          ),
          child: PolisLapsedWidget(),
        ),
      ),
      '4': HomeWidgetBase(
        iconUrl: 'icon/widget-polis-jatuh-tempo.svg',
        title: 'expired_policy_str',
        widgetKey: kWidgetKeyPolisJatuhTempo,
        content: LazyLoadingWidget(
          widgetKey: 'polis_jatuh_tempo_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'polis_jatuh_tempo_widget',
            title: 'expired_policy_str'.tr,
          ),
          child: PolisJatuhTempoWidget(),
        ),
      ),
      '5': HomeWidgetBase(
        iconUrl: 'icon/widget-produksi-saya.svg',
        title: 'my_production_str',
        widgetKey: kWidgetKeyProduksiSaya,
        content: LazyLoadingWidget(
          widgetKey: 'production_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'production_widget',
            title: 'my_production_str'.tr,
          ),
          child: HomeWidgetProduction(),
        ),
      ),
      '6': HomeWidgetBase(
        iconUrl: 'icon/widget-validasi-promosi.svg',
        title: 'validation_promotion_str',
        widgetKey: kWidgetKeyValidasiPromosi,
        content: LazyLoadingWidget(
          widgetKey: 'validasi_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'validasi_widget',
            title: 'validation_promotion_str'.tr,
          ),
          child: ValidasiWidget(),
        ),
      ),
      '7': HomeWidgetBase(
        iconUrl: 'icon/widget-estimasi-kompensasi.svg',
        title: 'compensation_estimate_str',
        widgetKey: kWidgetKeyEstimasiKompensasi,
        content: LazyLoadingWidget(
          widgetKey: 'kompensasi_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'kompensasi_widget',
            title: 'compensation_estimate_str'.tr,
          ),
          child: KompensasiWidget(),
        ),
      ),
      '8': HomeWidgetBase(
        iconUrl: 'icon/widget-persistensi.svg',
        title: 'persistency_str',
        widgetKey: kWidgetKeyPersistensi,
        content: LazyLoadingWidget(
          widgetKey: 'persistensi_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'persistensi_widget',
            title: 'persistency_str'.tr,
          ),
          child: PersistensiWidget(),
        ),
      ),
      '9': HomeWidgetBase(
        iconUrl: 'icon/widget-status-spaj.svg',
        title: 'status_spaj_str',
        widgetKey: kWidgetKeyStatusSpaj,
        content: LazyLoadingWidget(
          widgetKey: 'spaj_widget',
          loadingWidget: CustomWidgetLoading(
            widgetType: 'spaj_widget',
            title: 'status_spaj_str'.tr,
          ),
          child: HomeWidgetSpaj(),
        ),
      ),
    };

    // Add widgets in the order specified by favoriteWidgetIds, but only if available in API
    log(
      'Generating ${favoriteWidgetIds.length} favorite widgets with API filtering',
    );

    // Use a Set to track already added widget IDs to prevent duplicates
    Set<String> addedWidgetIds = <String>{};

    for (String id in favoriteWidgetIds) {
      // Skip if this widget ID has already been added
      if (addedWidgetIds.contains(id)) {
        log('Skipping duplicate widget ID: $id');
        continue;
      }

      if (widgetMap.containsKey(id)) {
        Widget widget = widgetMap[id]!;

        // Get the widget key from the widget
        String? widgetKey = _getWidgetKeyFromId(id);

        // Only add widget if it's available in API or if API data is not loaded yet
        if (availableWidgetKeys.isEmpty ||
            (widgetKey != null && availableWidgetKeys.contains(widgetKey))) {
          log('Adding widget with ID: $id, widgetKey: $widgetKey');
          favoriteWidgets.add(widget);
          addedWidgetIds.add(id); // Track this widget as added
        } else {
          log(
            'Skipping widget with ID: $id, widgetKey: $widgetKey - not available in API',
          );
        }
      }
    }

    log('Generated ${favoriteWidgets.length} filtered favorite widgets');
  }

  /// Get widget key from widget ID
  String? _getWidgetKeyFromId(String id) {
    switch (id) {
      case '1':
        return kWidgetKeyUlangTahunNasabah;
      case '2':
        return kWidgetKeyStatusKlaim;
      case '3':
        return kWidgetKeyPolisLapse;
      case '4':
        return kWidgetKeyPolisJatuhTempo;
      case '5':
        return kWidgetKeyProduksiSaya;
      case '6':
        return kWidgetKeyValidasiPromosi;
      case '7':
        return kWidgetKeyEstimasiKompensasi;
      case '8':
        return kWidgetKeyPersistensi;
      case '9':
        return kWidgetKeyStatusSpaj;
      default:
        return null;
    }
  }

  /// Get all available widgets filtered by API
  List<Map<String, dynamic>> getAvailableWidgetsForList() {
    List<Map<String, dynamic>> allWidgets = [
      {
        'id': '1',
        'label': 'customer_birthday_str',
        'iconUrl': 'icon/widget-ulang-tahun-nasabah.svg',
        'widgetKey': kWidgetKeyUlangTahunNasabah,
      },
      {
        'id': '2',
        'label': 'claim_status_str',
        'iconUrl': 'icon/widget-status-klaim.svg',
        'widgetKey': kWidgetKeyStatusKlaim,
      },
      {
        'id': '3',
        'label': 'lapsed_policy_str',
        'iconUrl': 'icon/widget-polis-lapse.svg',
        'widgetKey': kWidgetKeyPolisLapse,
      },
      {
        'id': '4',
        'label': 'expired_policy_str',
        'iconUrl': 'icon/widget-polis-jatuh-tempo.svg',
        'widgetKey': kWidgetKeyPolisJatuhTempo,
      },
      {
        'id': '5',
        'label': 'my_production_str',
        'iconUrl': 'icon/widget-produksi-saya.svg',
        'widgetKey': kWidgetKeyProduksiSaya,
      },
      {
        'id': '6',
        'label': 'validation_promotion_str',
        'iconUrl': 'icon/widget-validasi-promosi.svg',
        'widgetKey': kWidgetKeyValidasiPromosi,
      },
      {
        'id': '7',
        'label': 'compensation_estimate_str',
        'iconUrl': 'icon/widget-estimasi-kompensasi.svg',
        'widgetKey': kWidgetKeyEstimasiKompensasi,
      },
      {
        'id': '8',
        'label': 'persistency_str',
        'iconUrl': 'icon/widget-persistensi.svg',
        'widgetKey': kWidgetKeyPersistensi,
      },
      {
        'id': '9',
        'label': 'status_spaj_str',
        'iconUrl': 'icon/widget-status-spaj.svg',
        'widgetKey': kWidgetKeyStatusSpaj,
      },
    ];

    // Filter widgets based on API availability
    if (availableWidgetKeys.isEmpty) {
      // If API data is not loaded yet, return all widgets
      return allWidgets;
    }

    return allWidgets.where((widget) {
      return availableWidgetKeys.contains(widget['widgetKey']);
    }).toList();
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      userLevel.value = data.roles?.code ?? '-';
      userChannel.value = data.channel ?? '-';

      // Save user data to SharedPreferences for caching
      await prefs.setString(kStorageAgentName, data.agentName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserUsername, data.username ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
      await prefs.setString(kStorageUserId, (data.id ?? 0).toString());
      await prefs.setString(
        kStorageAgentCode,
        data.agentCode ?? data.username ?? '-',
      );
      await prefs.setString(kStorageUserChannel, data.channel ?? '-');
      await prefs.setString(kStorageUserType, data.userType ?? '-');

      var widgetAccess =
          data.roles?.accesses
              ?.where((element) {
                return element.domain?.contains("agent.Widget") ?? false;
              })
              .where((element) {
                return element.action?.contains("view") ?? false;
              }) ??
          [];
      await prefs.setStringList(
        kStorageUserRoles,
        widgetAccess.map((e) {
          var domain = e.domain ?? "";
          if (domain.contains("agent.Widget.BirthdayCustomer")) {
            return '1';
          } else if (domain.contains("agent.Widget.ClaimTracking")) {
            return '2';
          } else if (domain.contains("agent.Widget.PolicyLapsed")) {
            return '3';
          } else if (domain.contains("agent.Widget.PolicyOverdue")) {
            return '4';
          } else if (domain.contains("agent.Widget.Produksi")) {
            return '5';
          } else if (domain.contains("agent.Widget.Promosi") &&
              data.userType != 'STAFF') {
            return '6';
          } else if (domain.contains("agent.Widget.Commission")) {
            return '7';
          } else if (domain.contains("agent.Widget.Persistency")) {
            return '8';
          } else if (domain.contains("agent.Widget.Spaj")) {
            return '9';
          } else {
            return '';
          }
        }).toList(),
      );

      // Always update locale from Firebase after login
      updateLocaleFromFirebase();

      // Initialize analytics with user data after successful login
      await _initializeAnalyticsWithUser(data);

      log('User data parsed and cached successfully: ${data.agentName}');
    } catch (e) {
      log('Error parsing user data: $e');
    } finally {
      _setLoadingState(
        false,
      ); // Ensure loading is set to false even if parsing fails
    }
  }

  void updateLocaleFromFirebase() async {
    try {
      log('Attempting to update locale from Firebase after login');

      // Wait a bit to ensure Firestore ID is set
      await Future.delayed(Duration(milliseconds: 500));

      final firestoreServices = FirestoreServices();

      // Get initial values from Firebase which includes language setting
      await firestoreServices.getInitialValue();

      // Update setting controller to reflect current locale
      settingC.locale.value = Get.locale.toString();
      settingC.tempLocale.value = Get.locale.toString();

      log('Locale updated from Firebase successfully: ${Get.locale}');
    } catch (e) {
      log('Error updating locale from Firebase: $e');
      // Fallback to default locale if Firebase fails
      Utils.setLocale('id_ID');
      settingC.locale.value = 'id_ID';
      settingC.tempLocale.value = 'id_ID';
    }
  }

  /// Initialize analytics with user data after successful login
  Future<void> _initializeAnalyticsWithUser(UserModels data) async {
    try {
      // Prepare user properties for analytics
      final userProperties = <String, String>{
        'user_level': data.level ?? 'unknown',
        'user_role': data.roles?.code ?? 'unknown',
        'agent_code': data.agentCode ?? 'unknown',
        'branch_code': data.branchCode ?? 'unknown',
        'user_type': 'agent',
      };

      // Initialize analytics with user ID and properties
      await AnalyticsUtils.initializeWithUser(
        userId: data.agentCode ?? data.username ?? data.id?.toString(),
        userProperties: userProperties,
      );

      // Analytics initialized with user data
    } catch (e) {
      // Continue without analytics - don't block user experience
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    log(
      'Load failed for request code: $requestCode, response: ${response.statusText}',
    );

    // Handle specific failures
    switch (requestCode) {
      case kReqGetProfile:
        log('Profile API failed, keeping cached data if available');
        break;
      case 0: // getWidgetSort
        log('Widget sort API failed, using default widget order');
        break;
      default:
        log('Unknown API failed: $requestCode');
    }

    _setLoadingState(false); // Ensure loading is set to false on failure
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    log('Load error: $e');

    // Implement fallback mechanisms
    _handleLoadError(e, response);

    _setLoadingState(false); // Ensure loading is set to false on error
  }

  /// Handle load errors with fallback mechanisms
  void _handleLoadError(dynamic error, Response? response) {
    log('Handling load error with fallback mechanisms');

    // If we have cached user data, keep showing it
    if (userData.value.agentName != null &&
        userData.value.agentName!.isNotEmpty) {
      log('Using cached user data as fallback');
    } else {
      // Try to load from SharedPreferences as last resort
      _loadCachedUserData();
    }

    // Keep current favorite widgets state (even if empty)
    log(
      'Using current favorite widgets state (${favoriteWidgets.length} widgets)',
    );
  }

  /// Clean up duplicate favorite widget IDs from storage
  Future<void> cleanupDuplicateFavoriteWidgets() async {
    try {
      // Check SharedPreferences
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];
      List<String> cleanedIds = _removeDuplicatesFromStringList(savedIds);

      if (cleanedIds.length != savedIds.length) {
        log(
          'Found and cleaning ${savedIds.length - cleanedIds.length} duplicate IDs in SharedPreferences',
        );
        await prefs.setStringList('favorite_widgets', cleanedIds);
      }

      // Check and clean Firestore data
      try {
        List<String>? firestoreIds = await _loadFromFirestore();
        if (firestoreIds != null) {
          List<String> cleanedFirestoreIds = _removeDuplicatesFromStringList(
            firestoreIds,
          );

          if (cleanedFirestoreIds.length != firestoreIds.length) {
            log(
              'Found and cleaning ${firestoreIds.length - cleanedFirestoreIds.length} duplicate IDs in Firestore',
            );

            // Save cleaned data back to Firestore
            FirestoreServices firestoreServices;
            try {
              firestoreServices = Get.find<FirestoreServices>();
            } catch (e) {
              firestoreServices = Get.put(FirestoreServices());
            }
            await firestoreServices.saveFavoriteWidgets(cleanedFirestoreIds);
          }
        }
      } catch (e) {
        log('Error cleaning Firestore favorite widgets: $e');
      }
    } catch (e) {
      log('Error during favorite widgets cleanup: $e');
    }
  }

  /// Helper method to remove duplicates from a list of strings while preserving order
  List<String> _removeDuplicatesFromStringList(List<String> list) {
    List<String> result = [];
    Set<String> seen = <String>{};

    for (String item in list) {
      if (!seen.contains(item)) {
        result.add(item);
        seen.add(item);
      }
    }

    return result;
  }

  /// Public method to manually clean up duplicate favorite widgets and reload
  Future<void> fixDuplicateFavoriteWidgets() async {
    try {
      log('Manually fixing duplicate favorite widgets...');

      // Clean up storage
      await cleanupDuplicateFavoriteWidgets();

      // Reload favorite widgets
      loadFavoriteWidgets();

      log('Duplicate favorite widgets cleanup completed');
    } catch (e) {
      log('Error fixing duplicate favorite widgets: $e');
    }
  }

  performAddDevice(var data) async {
    await api.performRegisterDevice(
      controllers: this,
      data: data,
      code: kReqAddDevices,
    );
  }

  registerDevice() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String buildNumber = await Utils.getBuildNumber();
    String appVersion = await Utils.getAppVersionOnly();
    String deviceId = '';

    // Get Firebase token from NotificationService
    String firebaseToken = '';
    try {
      firebaseToken = await FirebaseMessaging.instance.getToken() ?? '';
    } catch (e) {
      log('Error getting Firebase token: $e');
    }

    var data = {};
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;

      // Generate or retrieve UUID for web device ID
      String? storedWebDeviceId = prefs.getString(kWebDeviceId);
      if (storedWebDeviceId == null || storedWebDeviceId.isEmpty) {
        // Generate new UUID and store it
        storedWebDeviceId = const Uuid().v4();
        await prefs.setString(kWebDeviceId, storedWebDeviceId);
      }
      deviceId = storedWebDeviceId;

      data = {
        "deviceId": storedWebDeviceId,
        "deviceModel": "${webBrowserInfo.browserName}",
        "osType": "${webBrowserInfo.platform}",
        "osVersion": "${webBrowserInfo.appVersion}",
        "appVersion": appVersion,
        "appBuildNumber": buildNumber,
        "deviceLanguage": "${Get.locale}",
        "screenWidth": Get.width,
        "screenHeight": Get.height,
        "connectionType": "",
        "timezone": "",
        "firebaseToken": firebaseToken,
        "manufacturer": "${webBrowserInfo.vendor}",
      };
    } else {
      if (isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
        data = {
          "deviceId": androidInfo.id,
          "deviceModel": androidInfo.model,
          "osType": Platform.localeName,
          "osVersion": androidInfo.version.release,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": firebaseToken,
          "manufacturer": androidInfo.manufacturer,
        };
      }
      if (isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = '${iosInfo.identifierForVendor}';
        data = {
          "deviceId": "${iosInfo.identifierForVendor}",
          "deviceModel": iosInfo.modelName,
          "osType": Platform.localeName,
          "osVersion": iosInfo.systemVersion,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": firebaseToken,
          "manufacturer": "Apple",
        };
      }
    }

    performAddDevice(data);
    await prefs.setString(kDeviceId, deviceId);
  }

  void changeTheme(String name, ThemeData theme) {
    Get.changeTheme(theme);
  }
}
