import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/lazy_loading_controller.dart';
import 'dart:async';

class HomeWidgetProductionController extends BaseControllers {
  late SharedPreferences prefs;
  RxString userLevel = ''.obs;
  RxString userChannel = ''.obs;
  RxString userType = ''.obs;
  // core data
  Rx<WidgetProductionSumModels> widgetDataMonth = Rx(
    WidgetProductionSumModels(),
  );
  Rx<WidgetProductionSumModels> widgetDataYear = Rx(
    WidgetProductionSumModels(),
  );

  RxString selectedType = kSwitchMonthly.obs;

  // Flag untuk lazy loading
  bool _hasInitialized = false;

  // StreamSubscription untuk lazy loading
  StreamSubscription<WidgetLoadEvent>? _loadEventSubscription;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    userChannel.value = prefs.getString(kStorageUserChannel) ?? '';
    userType.value = prefs.getString(kStorageUserType) ?? '';

    final LazyLoadingController lazyLoadingController =
        Get.find<LazyLoadingController>();
    setupLazyLoading(lazyLoadingController);
    // Tidak langsung fetch data, tunggu trigger dari lazy loading
    _hasInitialized = true;
  }

  /// Method untuk setup lazy loading listener
  void setupLazyLoading(LazyLoadingController lazyLoadingController) {
    // Listen untuk load events dari LazyLoadingController
    try {
      _loadEventSubscription = lazyLoadingController.loadEventStream.listen((
        event,
      ) {
        if (event.widgetKey == 'production_widget') {
          initializeLazyLoading();
        }
      });
    } catch (e) {
      // LazyLoadingController belum ada, fallback ke loading biasa
      initializeLazyLoading();
    }
  }

  /// Method untuk trigger lazy loading dari LazyLoadingController
  Future<void> initializeLazyLoading() async {
    if (!_hasInitialized) {
      await _initialize();
    }
    load();
  }

  @override
  void onClose() {
    _loadEventSubscription?.cancel();
    super.onClose();
  }

  /// Private initialization method
  Future<void> _initialize() async {
    prefs = await SharedPreferences.getInstance();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    userChannel.value = prefs.getString(kStorageUserChannel) ?? '';
    _hasInitialized = true;
  }

  @override
  void load() async {
    final year = DateTime.now().year.toString();
    final month = DateTime.now().month.toString();
    final paramsMonthly = 'year=$year&month=$month';
    final paramsYearly = 'year=$year';
    super.load();
    await api.getWidgetProductionSum(
      controllers: this,
      params: paramsMonthly,
      code: kReqGetWgtSumMonth,
    );
    await api.getWidgetProductionSum(
      controllers: this,
      params: paramsYearly,
      code: kReqGetWgtSumYear,
    );
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    parseData(response, requestCode);
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  parseData(response, int reqCode) {
    WidgetProductionSumModels data = WidgetProductionSumModels.fromJson(
      response,
    );
    switch (reqCode) {
      case kReqGetWgtSumMonth:
        widgetDataMonth.value = data;
        break;
      case kReqGetWgtSumYear:
        widgetDataYear.value = data;
        break;
      default:
    }
  }
}
