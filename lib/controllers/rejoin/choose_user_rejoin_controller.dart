import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/response/eligible_candidates_response.dart';
import 'package:pdl_superapp/models/response/requested_rejoin_response.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ChooseUserRejoinController extends BaseControllers {
  late SharedPreferences prefs;
  String level = '';
  RxBool isReqNew = false.obs;
  final rejoinLevel = Rxn<String>();

  Rx<EligibleCandidatesResponse> eligibleResp =
      EligibleCandidatesResponse().obs;
  Rx<RequestedRejoinResponse> requestedRejoinsResp =
      RequestedRejoinResponse().obs;
  RxList<EligibleCandidateModel> candidates = <EligibleCandidateModel>[].obs;
  RxList<RequestedRejoinModel> requestedRejoins = <RequestedRejoinModel>[].obs;
  RxInt totalPages = 0.obs;
  RxInt currentPage = 0.obs;
  final q = Rxn<String>();

  @override
  Future<void> onInit() async {
    prefs = await SharedPreferences.getInstance();
    level = prefs.getString(kStorageUserLevel) ?? '';
    isReqNew.value = Get.arguments['is_req_new'];

    if (Get.arguments['rejoin_level'] != null) {
      rejoinLevel.value = Get.arguments['rejoin_level'];
    }

    reqList(isRefresh: true);

    super.onInit();
  }

  void reqList({bool isRefresh = false}) {
    if (isReqNew.value) {
      reqGetEligibleCandidates(isRefresh: isRefresh);
    } else {
      reqGetRequestedRejoin(isRefresh: isRefresh);
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqRejoinEligibleCandidates:
        _onSuccessGetCandidates(response);
      case kReqRequestedRejoin:
        _onSuccessGetReqRejoin(response);
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    Get.snackbar(
      'Gagal',
      response.body['message'] ??
          '${response.body['error_description'] ?? 'Terjadi Kesalahan harap ulangi kembali'}',
      colorText: Colors.white,
      backgroundColor: Colors.red,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  void _onSuccessGetCandidates(response) {
    try {
      eligibleResp.value = EligibleCandidatesResponse.fromJson(response);
      final tempCandidates =
          (eligibleResp.value.content ?? [])
              .where((element) => element.previousLevel == rejoinLevel.value)
              .toList();
      candidates.addAll(tempCandidates);

      totalPages.value =
          (eligibleResp.value.totalPages ?? 1) -
          1; // dikurangi 1 karena current page nya dimulai dari 0, tapi total pages dimulai dari 1
    } catch (e, st) {
      log('error get candidates $e $st');
    }
  }

  void _onSuccessGetReqRejoin(response) {
    try {
      requestedRejoinsResp.value = RequestedRejoinResponse.fromJson(response);
      requestedRejoins.addAll(requestedRejoinsResp.value.content ?? []);
      totalPages.value =
          (requestedRejoinsResp.value.totalPages ?? 1) -
          1; // dikurangi 1 karena current page nya dimulai dari 0, tapi total pages dimulai dari 1
    } catch (e, st) {
      log('error get requested rejoin $e $st');
    }
  }

  Future<void> reqGetEligibleCandidates({bool isRefresh = false}) async {
    setLoading(true);
    if (isRefresh) {
      currentPage.value = 0;
      totalPages.value = 0;
      candidates.clear();
    }

    Map<String, dynamic> queryParams = {'page': currentPage.value, 'size': 20};

    if (q.value != null && q.value!.trim().isNotEmpty) {
      queryParams['searchQuery'] = q.value;
    }

    await api.getEligibleCandidatesRejoin(
      controllers: this,
      query: queryParams,
      code: kReqRejoinEligibleCandidates,
    );
  }

  Future<void> reqGetRequestedRejoin({bool isRefresh = false}) async {
    setLoading(true);
    if (isRefresh) {
      currentPage.value = 0;
      totalPages.value = 0;
      requestedRejoins.clear();
    }

    Map<String, dynamic> queryParams = {'page': currentPage.value, 'size': 20};

    if (q.value != null && q.value!.trim().isNotEmpty) {
      queryParams['searchQuery'] = q.value;
    }
    await api.getRequestedRejoin(
      controllers: this,
      code: kReqRequestedRejoin,
      query: queryParams,
    );
  }
}
