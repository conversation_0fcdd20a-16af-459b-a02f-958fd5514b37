import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiTeamController extends BaseControllers {
  RxList<TerminationCandidateModel> terminasiCandidateList =
      <TerminationCandidateModel>[].obs;
  @override
  void onInit() {
    super.onInit();
    getTerminasiCandidateList();
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    switch (requestCode) {
      case kReqGetEligibleCandidateTermination:
        terminasiCandidateList.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.from<PERSON>son(datas[i]);
          terminasiCandidateList.add(terminasiData);
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  getTerminasiCandidateList() async {
    await api.getTerminationEligibleCandidates(
      controllers: this,
      params: null,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  void refreshData() {}
}
