import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/models/termination_candidate_model.dart';
import 'package:pdl_superapp/models/termination_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/utils/keys.dart';

class TerminasiController extends BaseControllers {
  final TextEditingController reasonTextController = TextEditingController();
  final TextEditingController polisTextController = TextEditingController();
  final RxBool hasChanges = false.obs;
  final RxBool isSelfTermination = false.obs;
  final RxString agentName = ''.obs;
  final RxString agentCode = ''.obs;
  final RxString submissionDate = ''.obs;
  final RxString level = ''.obs;
  final RxString selectedValue = '0001'.obs;
  late SharedPreferences prefs;

  final Map<String, String> dropdownOptions = {};

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();

    reasonTextController.addListener(() {
      if (reasonTextController.text.isNotEmpty) {
        hasChanges.value = true;
      } else {
        hasChanges.value = false;
      }
    });

    polisTextController.text = dropdownOptions[selectedValue.value] ?? '';

    var isSelf = Get.arguments[kArgsSelfTermination] ?? true;

    isSelfTermination(isSelf);

    if (isSelfTermination.isTrue) {
      agentCode.value = prefs.getString(kStorageAgentCode) ?? '';
      agentName.value = prefs.getString(kStorageAgentName) ?? '';
      level.value = prefs.getString(kStorageUserLevel) ?? '';
      submissionDate.value = '03/01/2025';
      await getTerminasiList();
    } else {
      TerminationCandidateModel terminationData =
          Get.arguments[kArgsTerminationData] ?? {};

      agentCode.value = terminationData.agentCode ?? '';
      agentName.value = terminationData.agentName ?? '';
      level.value = terminationData.level ?? '';
      submissionDate.value = '03/01/2025';

      getAgentList();
    }
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    switch (requestCode) {
      case kReqSubmitTermination:
        final terminasiData = TerminationModel.fromJson(response);
        onSuccessTermination(terminasiData);
        break;
      case kReqSubmitPolicyTransfer:
        break;
      case kReqGetTerminationList:
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationModel.fromJson(datas[i]);

          if ((terminasiData.target?.agentCode) == agentCode.value &&
              terminasiData.status != 'CANCELLED') {
            Get.offNamed(
              Routes.TERMINASI_DETAIL,
              arguments: {
                kArgsTerminationData: terminasiData,
                kArgsSelfTermination: isSelfTermination.isTrue,
              },
            );
            break;
          }
        }
        break;
      case kReqGetEligibleCandidateTermination:
        dropdownOptions.clear();
        var datas = response['content'];
        for (int i = 0; i < datas.length; i++) {
          final terminasiData = TerminationCandidateModel.fromJson(datas[i]);
          dropdownOptions[terminasiData.agentCode ?? ''] =
              terminasiData.agentName ?? '';
        }
        break;
      default:
    }
  }

  @override
  void loadFailed({required int requestCode, required Response response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
  }

  onSuccessTermination(TerminationModel terminasiData) async {
    if (isSelfTermination.isFalse) {
      await submitPolicyTransfer(terminasiData.id ?? 0);
    }

    Get.offNamed(
      Routes.TERMINASI_DETAIL,
      arguments: {
        kArgsTerminationData: terminasiData,
        kArgsTerminationReason: reasonTextController.text,
        kArgsSelfTermination: isSelfTermination.isTrue,
      },
    );
  }

  submitTerminasi() async {
    var params = {
      "targetAgentCode": agentCode.value,
      "reason": reasonTextController.text,
    };

    setLoading(true);
    await api.performSubmitTerminasi(
      controllers: this,
      data: params,
      code: kReqSubmitTermination,
    );
  }

  submitPolicyTransfer(int trxId) async {
    var params = {
      "targetAgentCode": selectedValue.value,
      "trxTerminationId": trxId,
    };
    setLoading(true);
    await api.performSubmitPolicyTransfer(
      controllers: this,
      data: params,
      code: kReqSubmitPolicyTransfer,
    );
  }

  getTerminasiList() async {
    await api.getTerminationList(
      controllers: this,
      params: null,
      code: kReqGetTerminationList,
    );
  }

  getAgentList() async {
    await api.getTerminationEligibleCandidates(
      controllers: this,
      params: null,
      code: kReqGetEligibleCandidateTermination,
    );
  }

  void refreshData() {}
}
