import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Middleware untuk memastikan user sudah login
/// Redirect ke login page jika belum login
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    return _checkAuth() ? null : const RouteSettings(name: Routes.LOGIN);
  }

  bool _checkAuth() {
    // Untuk development, bisa di-comment jika ingin bypass auth
    try {
      // Try to get SharedPreferences from GetX dependency injection
      SharedPreferences? prefs;
      try {
        prefs = Get.find<SharedPreferences>();
      } catch (e) {
        // If not found in GetX, return false (not authenticated)
        return false;
      }

      final token = prefs.getString(kStorageToken);
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}

/// Middleware untuk halaman public yang tidak memerlukan authentication
/// Tidak melakukan redirect apapun
class PublicMiddleware extends GetMiddleware {
  @override
  int? get priority => 0;

  @override
  RouteSettings? redirect(String? route) {
    // Halaman public tidak memerlukan authentication check
    return null;
  }
}

/// Middleware untuk redirect ke home jika sudah login
/// Digunakan untuk halaman login, register, dll
class LoginGuardMiddleware extends GetMiddleware {
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    return _isLoggedIn() ? const RouteSettings(name: Routes.MAIN) : null;
  }

  bool _isLoggedIn() {
    try {
      // Try to get SharedPreferences from GetX dependency injection
      SharedPreferences? prefs;
      try {
        prefs = Get.find<SharedPreferences>();
      } catch (e) {
        // If not found in GetX, return false (not logged in)
        return false;
      }

      final token = prefs.getString(kStorageToken);
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
