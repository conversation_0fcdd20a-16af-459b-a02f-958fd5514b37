// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_workers/utils/debouncer.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar_initial.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/controllers/rejoin/choose_user_rejoin_controller.dart';
import 'package:pdl_superapp/models/response/requested_rejoin_response.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_widgets/shimmer_list_inbox.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RejoinListPage extends StatefulWidget {
  const RejoinListPage({super.key});

  @override
  State<RejoinListPage> createState() => _RejoinListPageState();
}

class _RejoinListPageState extends State<RejoinListPage> {
  final ChooseUserRejoinController controller = Get.put(
    ChooseUserRejoinController(),
  );

  late ScrollController scrollController;
  final debouncer = Debouncer(delay: const Duration(milliseconds: 700));

  void _onSearchChanged(String query) {
    debouncer(() {
      FocusScope.of(context).unfocus();
      controller.q.value = query;
      controller.reqList(isRefresh: true);
    });
  }

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController()..addListener(_loadMore);
    Future.delayed(Duration.zero, () => FocusScope.of(context).unfocus());
  }

  void _loadMore() {
    if (scrollController.position.pixels >=
            (scrollController.position.maxScrollExtent - 100) &&
        !controller.isLoading.value) {
      if (controller.currentPage.value < controller.totalPages.value) {
        controller.currentPage.value += 1;
        controller.reqList();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Pengajuan Bergabung Kembali',
      controller: controller,
      onRefresh: () {
        controller.reqList(isRefresh: true);
      },
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          children: [
            SizedBox(
              width: Get.width,
              child: PdlTextField(
                hint: 'Cari nama atau kode agen',
                onChanged: _onSearchChanged,
                prefixIcon: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Utils.cachedSvgWrapper(
                    'icon/ic-linear-search -2.svg',
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                ),
              ),
            ),
            SizedBox(height: paddingMedium),
            Obx(() {
              if (controller.isLoading.value &&
                  controller.currentPage.value == 0) {
                return ShimmerListInbox();
              }

              if (controller.requestedRejoins.isEmpty) {
                return EmptyStateView(
                  msg: 'Hmm.. belum ada permintaan bergabung',
                );
              }
              return Column(
                children: [
                  RefreshIndicator(
                    onRefresh: () async {
                      controller.reqList(isRefresh: true);
                      return;
                    },
                    child: ListView.builder(
                      controller: scrollController,
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(vertical: paddingMedium),
                      itemBuilder: (context, index) {
                        var data = controller.requestedRejoins[index];
                        return _RejoinCard(data: data);
                      },

                      itemCount: controller.requestedRejoins.length,
                    ),
                  ),
                  if (controller.isLoading.value &&
                      controller.currentPage.value > 0)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(),
                    ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}

class _RejoinCard extends StatelessWidget {
  const _RejoinCard({required this.data});
  final RequestedRejoinModel data;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => Get.toNamed(
            Routes.DETAIL_REJOIN,
            arguments: {
              'id_rejoin': data.id,
              'id_approval_header': data.approvalHeader?.id,
            },
          ),
      child: Container(
        width: Get.width,
        padding: EdgeInsets.only(
          left: paddingMedium,
          right: paddingMedium,
          top: paddingMedium,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                PdlCircleAvatar(
                  source: data.agentPicture ?? '',
                  border: true,
                  height: 44,
                  width: 44,
                  errorWidget: PdlCircleAvatarInitial(
                    name: data.agentName ?? '',
                  ),
                ),

                SizedBox(width: paddingMedium),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Bergabung Kembali'),
                            SizedBox(height: paddingSmall),
                            Text(
                              '${data.previousLevel} ${data.agentName}',
                              style: Theme.of(context).textTheme.bodyLarge
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            SizedBox(height: paddingSmall),
                            Text(
                              '${data.branch?.branchName} - ${data.agentCode}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(fontWeight: FontWeight.w700),
                            ),
                            SizedBox(height: paddingMedium),
                            StatusLabel(status: data.status ?? ''),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Divider(),
          ],
        ),
      ),
    );
  }
}
