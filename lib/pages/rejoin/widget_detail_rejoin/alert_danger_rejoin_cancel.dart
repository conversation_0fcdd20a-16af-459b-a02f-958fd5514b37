import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AlertDangerRejoinCancel extends StatelessWidget {
  const AlertDangerRejoinCancel({
    super.key,
    this.isExpired = false,
    this.cancelReason,
  });
  final bool isExpired;
  final String? cancelReason;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: kColorGlobalBgRed,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Utils.cachedSvgWrapper('icon/ic-exclamation-triangle.svg'),
          SizedBox(width: paddingMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isExpired ? 'Pengaju<PERSON>' : '<PERSON>asa<PERSON>',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: kColorGlobalRed,
                  ),
                ),

                Text(
                  isExpired
                      ? 'Silakan melakukan pengajuan ulang atau hubungi tim terkait.'
                      : cancelReason ?? 'Tidak sengaja melakukan pengajuan',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: kColorGlobalRed),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
