import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/components/vertical_dash_line.dart';
import 'package:pdl_superapp/models/approval_detail.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';

class HistoryApprovalRejoinBottomSheet {
  static show({required List<ApprovalDetail> approvals}) {
    PdlBottomSheet(
      content: Container(
        margin: EdgeInsets.only(top: paddingExtraLarge),
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: approvals.length,
          itemBuilder: (context, index) {
            return _ItemHistoryApproval(
              isLast: (index + 1) == 3,
              data: approvals[index],
            );
          },
        ),
      ),
      title: 'title_approval_history'.tr,
    );
  }
}

class _ItemHistoryApproval extends StatelessWidget {
  const _ItemHistoryApproval({required this.isLast, required this.data});
  final ApprovalDetail data;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator and line
          SizedBox(
            width: 40,
            child: Column(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: kColorPaninBlue,
                    shape: BoxShape.circle,
                    border: Border.all(color: kColorGlobalBlue100, width: 3),
                  ),
                ),
                Expanded(child: VerticalDashLine()),
                if (isLast)
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: kColoTuatara400,
                      shape: BoxShape.circle,
                      border: Border.all(color: kColorBorderLight, width: 3),
                    ),
                  ),
              ],
            ),
          ),
          // Content card
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Card(
                color: Colors.transparent,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade200),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            (data.createdAt ?? '').formatDate(
                              format: 'dd/MM/yyyy HH:mm',
                            ),
                            style: textTheme.bodyMedium,
                          ),
                          StatusLabel(status: data.approvalStatus ?? ''),
                        ],
                      ),
                      const SizedBox(height: paddingSmall),
                      Text(
                        '${data.actionBy?.agentLevel ?? (data.actionBy?.roles?.first.code ?? '').getRoleOnly} ${data.actionBy?.name}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: paddingSmall),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${'notes_str'.tr} : ',
                            style: textTheme.bodyMedium,
                          ),
                          const SizedBox(width: paddingExtraSmall),
                          Expanded(
                            child: Text(
                              '${data.remarks}',
                              style: textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// class _EmptyHistory extends StatelessWidget {
//   const _EmptyHistory();

//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       width: Get.width,
//       child: Column(
//         children: [
//           Utils.cachedSvgWrapper(
//             'icon/illustration-empty-no-found.svg',
//             width: 150,
//             height: 150,
//           ),
//           SizedBox(height: paddingLarge),
//           Text(
//             'Belum ada riwayat persetujuan',
//             textAlign: TextAlign.center,
//             style: Theme.of(context).textTheme.bodyMedium?.copyWith(
//               color:
//                   Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
//             ),
//           ),
//           SizedBox(height: paddingExtraLarge),
//         ],
//       ),
//     );
//   }
// }
