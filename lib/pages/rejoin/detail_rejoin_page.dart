// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar.dart';
import 'package:pdl_superapp/components/pdl_circle_avatar_initial.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/rejoin/detail_rejoin_controller.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/alert_danger_rejoin_cancel.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/bottom_sheet_rejoin.dart';
import 'package:pdl_superapp/pages/rejoin/widget_detail_rejoin/history_approval_rejoin.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:path/path.dart' as path;

class DetailRejoinPage extends StatelessWidget {
  DetailRejoinPage({super.key});

  final DetailRejoinController controller = Get.put(DetailRejoinController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'form_relying_str'.tr,
      controller: controller,
      onRefresh: () {
        controller.getDetailRejoin();
      },
      bottomWidget: _bottomButton(context),
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Obx(() {
          if (controller.isLoadingGetDetail.value) {
            return Center(child: CircularProgressIndicator());
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Obx(
                () => TitleWidget(
                  title: 'form_relying_str'.tr,
                  action:
                      controller.detailRejoinResp.value == null
                          ? SizedBox.shrink()
                          : StatusLabel(
                            status:
                                controller.detailRejoinResp.value!.status ?? '',
                            isCancelable:
                                !controller.historyApprovals.any(
                                  (e) =>
                                      e.approvalStatus ==
                                      StatusApproval.DISETUJUI.name,
                                ),
                          ),
                ),
              ),
              SizedBox(height: paddingLarge),
              if (controller.detailRejoinResp.value?.status ==
                      Status.CANCELLED.name ||
                  controller.detailRejoinResp.value?.status ==
                      Status.EXPIRED.name) ...[
                AlertDangerRejoinCancel(
                  isExpired:
                      controller.detailRejoinResp.value?.status ==
                      Status.EXPIRED.name,
                  cancelReason:
                      controller.detailRejoinResp.value?.status ==
                              Status.CANCELLED.name
                          ? controller
                                  .detailRejoinResp
                                  .value
                                  ?.approvalHeader
                                  ?.remarks ??
                              'no_reason_str'.tr
                          : null,
                ),
                SizedBox(height: paddingLarge),
              ],
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: Colors.grey),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: CachedNetworkImage(
                    width: double.infinity,
                    imageUrl: '${controller.agentData.value?.agentPicture}',
                    placeholder:
                        (context, url) => Container(
                          color: Colors.grey[300],
                          height: 200,
                          child: Icon(Icons.image, color: Colors.grey[600]),
                        ),
                    errorWidget:
                        (context, url, error) => Container(
                          color: Colors.grey[300],
                          height: 200,
                          child: Icon(
                            Icons.image_not_supported,
                            color: Colors.grey[600],
                          ),
                        ),
                  ),
                ),
              ),
              SizedBox(height: paddingMedium),
              _agentInformation(context),
              _levelRequested(context),
              SizedBox(height: paddingMedium),
              Divider(),
              SizedBox(height: paddingMedium),
              _rejoinReason(context),
              _cadidateIdentity(context),
              _statusApprovalApprover(context),
              _pemantauanPersetujuan(context),
              SizedBox(height: paddingLarge),
            ],
          );
        }),
      ),
    );
  }

  Widget _levelRequested(BuildContext context) {
    if (controller.idRejoin.value == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'level_requested_str'.tr,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: paddingSmall),
          Theme(
            data: Utils.dropDownThemeData(context),
            child: CustomDropdown(
              items:
                  controller.levelProposeAvailables
                      .map((e) => e['code'])
                      .toList(),
              initialItem:
                  controller.levelProposeAvailables
                      .map((e) => e['code'])
                      .toList()
                      .first,
              onChanged: (val) {
                controller.selectedLevelProposed.value = val;
              },
              closedHeaderPadding: EdgeInsets.all(12),
              decoration: Utils.dropdownDecoration(context),
            ),
          ),
        ],
      );
    }

    return Obx(
      () => _titleValue(
        context,
        title: 'level_requested_str'.tr,
        value: controller.detailRejoinResp.value?.proposedLevel ?? '',
      ),
    );
  }

  Widget _pemantauanPersetujuan(BuildContext context) {
    return Obx(() {
      if (controller.historyApprovals.isEmpty) return SizedBox.shrink();
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'title_approval_monitoring'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  HistoryApprovalRejoinBottomSheet.show(
                    approvals: controller.historyApprovals,
                  );
                },
                child: Text(
                  'label_view_history'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorPaninBlue,
                  ),
                ),
              ),
            ],
          ),
          ListView.builder(
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final data = controller.historyApprovals[index];
              return Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color:
                            Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight,
                      ),
                    ),
                    padding: EdgeInsets.all(12),
                    child: Row(
                      children: [
                        PdlCircleAvatar(
                          source: data.actionBy?.picture ?? '',
                          width: 44,
                          height: 44,
                          border: true,
                          errorWidget: PdlCircleAvatarInitial(
                            name: data.actionBy?.name ?? '',
                          ),
                        ),
                        SizedBox(width: paddingSmall),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${data.actionBy?.agentLevel ?? (data.actionBy?.roles?.first.code ?? '').getRoleOnly} ${data.actionBy?.name}',
                                style: Theme.of(context).textTheme.bodyLarge
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                              StatusLabel(status: data.approvalStatus ?? ''),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.symmetric(vertical: paddingSmall),
                    child: Icon(Icons.keyboard_arrow_down),
                  ),
                ],
              );
            },
            itemCount:
                controller.historyApprovals.length > 3
                    ? 3
                    : controller.historyApprovals.length,
            shrinkWrap: true,
          ),
        ],
      );
    });
  }

  Widget _bottomButton(BuildContext context) {
    return Obx(() {
      bool isNotCancelable = false;

      if (controller.idRejoin.value == null) {
        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }

        return PdlButton(
          controller: controller,
          onPressed:
              !controller.isValid.value
                  ? null
                  : () {
                    if (controller.reasonRejoinCtrl.text.trim().isEmpty ||
                        controller.ktpImage.value == null) {
                      Get.snackbar(
                        'fill_forms_str'.tr,
                        'fill_reason_and_id_card'.tr,
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: kColorGlobalBgRed,
                        colorText: kColorErrorText,
                      );
                      return;
                    }
                    BottomSheetRejoin.confirmRejoin(
                      context,
                      onTap: () async {
                        await controller.reqPostUploadDocRejoin();
                        FocusScope.of(context).unfocus();
                      },
                    );
                  },
          title: 'label_send_document'.tr,
        );
      } else if (controller.isApprover.value == true) {
        if (!controller.showButtonApprove) {
          return SizedBox.shrink();
        }

        if (controller.isLoading.value) {
          return Center(child: CircularProgressIndicator());
        }

        return PdlButton(
          controller: controller,
          onPressed: () {
            if (controller.selectedStatusApproval.value == 'Tolak' &&
                controller.remarksApproval.text.trim().isEmpty) {
              Get.snackbar(
                'fill_forms_str'.tr,
                'fill_remarks'.tr,
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: kColorGlobalBgRed,
                colorText: kColorErrorText,
              );
              return;
            }
            controller.reqPostApproval(
              body: ApprovalBody(
                approvalHeaderId: controller.idApprovalHeader.value,
                action:
                    controller.selectedStatusApproval.value == 'Setuju'
                        ? StatusApproval.DISETUJUI.name
                        : StatusApproval.DITOLAK.name,
                remarks: controller.remarksApproval.text,
              ),
            );
          },
          title: 'button_submit_str'.tr,
        );
      } else {
        isNotCancelable =
            controller.historyApprovals.any(
              (element) =>
                  element.approvalStatus == StatusApproval.DISETUJUI.name,
            ) ||
            controller.detailRejoinResp.value?.status ==
                Status.CANCELLED.name ||
            controller.detailRejoinResp.value?.status ==
                Status.DIKEMBALIKAN.name ||
            controller.detailRejoinResp.value?.status == Status.EXPIRED.name ||
            controller.detailRejoinResp.value?.status == Status.REJECTED.name;

        if (!isNotCancelable) {
          return PdlButton(
            backgroundColor: Colors.transparent,
            borderColor: Colors.red,
            foregorundColor: Colors.red,
            controller: controller,
            onPressed:
                () => BottomSheetRejoin.cancelRejoin(
                  context,
                  onTap: (String reason) {
                    controller.reqCancelRejoin(
                      id: controller.idRejoin.value!,
                      reason: reason,
                    );
                  },
                ),
            title: 'button_cancel_application'.tr,
          );
        }
      }

      return SizedBox.shrink();
    });
  }

  Obx _agentInformation(BuildContext context) {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleHeader(
            context,
            title: 'agent_information'.tr,
            subtitle: '(${'rejoin_str'.tr})',
          ),
          SizedBox(height: paddingMedium),
          _titleValue(
            context,
            title: 'label_form_agent_code'.tr,
            value: '${controller.agentData.value?.agentCode}',
          ),
          _titleValue(
            context,
            title: 'branch_name'.tr,
            value: '${controller.agentData.value?.branchName}',
          ),
          _titleValue(
            context,
            title: 'branch_location'.tr,
            value: '${controller.agentData.value?.branchCity}',
          ),
          _titleValue(
            context,
            title: 'label_form_full_name'.tr,
            value: '${controller.agentData.value?.agentName}',
          ),
          _titleValue(
            context,
            title: 'previous_level'.tr,
            value: '${controller.agentData.value?.previousLevel}',
          ),
          _titleValue(context, title: 'feasibility'.tr, value: 'worthy'.tr),
        ],
      ),
    );
  }

  Column _rejoinReason(BuildContext context) {
    if (controller.idRejoin.value == null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _titleHeader(
            context,
            title: 'reason_rejoin'.tr,
            subtitle: '(${'title_required_fields'.tr})',
          ),
          SizedBox(height: paddingMedium),
          PdlTextField(
            hint: 'reason_rejoin'.tr,
            textController: controller.reasonRejoinCtrl,
            maxLength: 200,
            onChanged: (p0) {
              controller.checkValid();
            },
            height: 100,
            isTextArea: true,
            keyboardType: TextInputType.multiline,
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _titleHeader(context, title: 'reason_rejoin'.tr),
        SizedBox(height: paddingSmall),
        Obx(
          () => Text(
            controller.detailRejoinResp.value?.remarks ?? '',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        SizedBox(height: paddingMedium),
        Divider(),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Widget _statusApprovalApprover(BuildContext context) {
    if (!controller.showButtonApprove) {
      return SizedBox.shrink();
    }
    return Column(
      children: [
        Container(
          margin: EdgeInsets.symmetric(vertical: paddingMedium),
          width: Get.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: kLine),
          ),
          child: Column(
            children: [
              Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingSmall),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10),
                    topRight: Radius.circular(10),
                  ),
                  color: kLine,
                ),
                child: Text(
                  'approval_status'.tr,
                  style: TextTheme.of(
                    context,
                  ).bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(paddingSmall),
                child: Column(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'approval_status'.tr,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        SizedBox(height: paddingSmall),
                        Theme(
                          data: Utils.dropDownThemeData(context),
                          child: CustomDropdown(
                            items: ['Setuju', 'Tolak'],
                            initialItem: 'Setuju',
                            onChanged: (val) {
                              controller.selectedStatusApproval.value =
                                  val ?? '';
                            },
                            closedHeaderPadding: EdgeInsets.all(12),
                            decoration: Utils.dropdownDecoration(context),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: paddingMedium),
                    Obx(
                      () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                '${'notes_str'.tr} ',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              if (controller.selectedStatusApproval.value ==
                                  'Setuju')
                                Text(
                                  '(${'optional'.tr})',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                            ],
                          ),
                          SizedBox(height: paddingMedium),
                          PdlTextField(
                            hint: 'add_note_or_info'.tr,
                            textController: controller.remarksApproval,
                            maxLength: 200,
                            height: 100,
                            isTextArea: true,
                            borderColor:
                                controller.selectedStatusApproval.value ==
                                        'Tolak'
                                    ? kColorError
                                    : null,
                            errorText:
                                controller.selectedStatusApproval.value ==
                                        'Tolak'
                                    ? 'title_required_fields'.tr
                                    : null,
                            keyboardType: TextInputType.multiline,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: paddingMedium),
        Divider(),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  Widget _cadidateIdentity(BuildContext context) {
    final theme = Theme.of(context).textTheme;
    if (controller.idRejoin.value == null) {
      return Container(
        margin: EdgeInsets.only(top: paddingMedium),
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _titleHeader(context, title: 'photo_ktp'.tr),
            SizedBox(height: paddingSmall),
            Obx(() {
              if (controller.ktpImage.value != null) {
                // Display the KTP image if available
                return Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color:
                          Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    ),
                  ),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(radiusMedium - 2),
                        child: GestureDetector(
                          onTap:
                              () => showImagePreview(
                                path: controller.ktpImage.value!.path,
                              ),
                          child:
                              kIsWeb
                                  ? Image.network(
                                    controller.ktpImage.value!.path,
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                  )
                                  : Image.file(
                                    controller.ktpImage.value!,
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                  ),
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              path.basename(controller.ktpImage.value!.path),
                            ),
                            Text(
                              '${controller.selectedKtpSize.value!.toStringAsFixed(2)}MB',
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: paddingSmall),
                      GestureDetector(
                        onTap: () {
                          controller.deleteSelectedKtp();
                          controller.checkValid();
                        },
                        child: Utils.cachedSvgWrapper(
                          'icon/ic-linear-trash-bin.svg',
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                // Show the upload/take photo button if no image
                return GestureDetector(
                  onTap: () async {
                    await controller.pickKtpImage(context);
                  },
                  child: Utils.customDottedBorder(
                    child: Container(
                      width: Get.width,
                      color: Colors.transparent,
                      padding: EdgeInsets.all(paddingMedium),
                      child: Column(
                        children: [
                          Utils.cachedSvgWrapper(
                            'icon/ic-linear-image.svg',
                            color: kColorGlobalBlue,
                            width: 40,
                          ),
                          Text(
                            'upload_or_take_photo_ktp'.tr,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: kColorGlobalBlue,
                              decoration: TextDecoration.underline,
                              decorationThickness: 2,
                              decorationColor: kColorGlobalBlue,
                              decorationStyle: TextDecorationStyle.solid,
                              height: 2,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }
            }),
            SizedBox(height: paddingSmall),
            Text(
              'label_image_warning'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                height: 2,
                color:
                    Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            border: Border.all(color: kColorBorderLight),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(3),
                child: GestureDetector(
                  onTap:
                      () => showImagePreview(
                        path:
                            controller
                                .detailRejoinResp
                                .value
                                ?.uploadedKtpPath ??
                            '',
                      ),
                  child: CachedNetworkImage(
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    imageUrl:
                        controller.detailRejoinResp.value?.uploadedKtpPath ??
                        '',
                  ),
                ),
              ),
              SizedBox(width: paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'KTP saya.png',
                      style: theme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: paddingExtraSmall),
                    Text('2MB', style: theme.bodyMedium),
                  ],
                ),
              ),
              if (controller.isApprover.value == true) ...[
                SizedBox(width: paddingSmall),
                IconButton(
                  onPressed: () {
                    controller.downloadFile(
                      controller.detailRejoinResp.value?.uploadedKtpPath,
                    );
                  },
                  icon: Icon(
                    Icons.file_download_outlined,
                    color: kColorGlobalBlue,
                  ),
                ),
              ],
            ],
          ),
        ),
        SizedBox(height: paddingMedium),
        Divider(),
        SizedBox(height: paddingMedium),
      ],
    );
  }

  void showImagePreview({required String path}) {
    bool isOnline = path.contains('http');
    Get.dialog(
      Stack(
        children: [
          Center(
            child: Padding(
              padding: EdgeInsets.all(25),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child:
                    isOnline
                        ? CachedNetworkImage(imageUrl: path)
                        : kIsWeb
                        ? Image.network(path)
                        : Image.file(File(path)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Row _titleHeader(
    BuildContext context, {
    required String title,
    String? subtitle,
  }) {
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        if (subtitle != null && subtitle.isNotEmpty) ...[
          SizedBox(width: paddingExtraSmall),
          Expanded(
            child: Text(
              subtitle,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Color(0xFF888888)),
            ),
          ),
        ],
      ],
    );
  }

  Container _titleValue(
    BuildContext context, {
    required String title,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: paddingMedium),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 1,
            child: Text(':', style: Theme.of(context).textTheme.bodyMedium),
          ),
          Expanded(
            flex: 4,
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }
}
