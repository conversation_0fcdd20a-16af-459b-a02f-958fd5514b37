import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/table_card.dart';
import 'package:pdl_superapp/controllers/agent_detail_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class AgentDetailPage extends StatelessWidget {
  AgentDetailPage({super.key});

  final AgentDetailController controller = Get.put(
    AgentDetailController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'agent_detail_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingMedium),

              // Agent Profile Section
              _buildProfileSection(),

              SizedBox(height: paddingLarge),

              // Individual Persistency Section
              _buildIndividualSection(),

              SizedBox(height: paddingLarge),

              // Team Persistency Section
              _buildTeamSection(),

              SizedBox(height: paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Obx(() {
      if (controller.isLoadingProfile.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.hasProfileError.value) {
        return _buildErrorWidget(
          controller.profileErrorMessage.value,
          () => controller.loadAgentProfile(),
        );
      }

      final profile = controller.agentProfile.value;
      if (profile == null) {
        return Center(child: Text('no_profile_data_str'.tr));
      }

      return Card(
        child: Padding(
          padding: EdgeInsets.all(paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'profile_str'.tr,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: paddingMedium),

              // Profile photo and basic info
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage:
                        profile.photo != null && profile.photo!.isNotEmpty
                            ? NetworkImage(profile.photo!)
                            : null,
                    child:
                        profile.photo == null || profile.photo!.isEmpty
                            ? Text(
                              Utils.getInitials(profile.name ?? ''),
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                            : null,
                  ),
                  SizedBox(width: paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          profile.name ?? '',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${profile.agentLevel ?? ''} - ${profile.agentCode ?? ''}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (profile.branchName != null)
                          Text(
                            profile.branchName!,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildIndividualSection() {
    return Obx(() {
      if (controller.isLoadingIndividu.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.hasIndividuError.value) {
        return _buildErrorWidget(
          controller.individuErrorMessage.value,
          () => controller.loadIndividuData(),
        );
      }

      final data = controller.individuData.value;
      if (data == null) {
        return Center(child: Text('no_individual_data_str'.tr));
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'individual_persistency_str'.tr,
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingMedium),

          TableCard(
            headers: ["P-13", "P-25", "P-37", "P-49", "P-61"],
            rows: [
              [
                controller.formatPercentage(data.persistency13),
                controller.formatPercentage(data.persistency25),
                controller.formatPercentage(data.persistency37),
                controller.formatPercentage(data.persistency49),
                controller.formatPercentage(data.persistency61),
              ],
            ],
            horizontalScrollable: true,
            minColumnWidths: {
              0: (Get.width * .92) / 5,
              1: (Get.width * .92) / 5,
              2: (Get.width * .92) / 5,
              3: (Get.width * .92) / 5,
              4: (Get.width * .92) / 5,
            },
          ),
        ],
      );
    });
  }

  Widget _buildTeamSection() {
    return Obx(() {
      if (controller.isLoadingTeam.value) {
        return Center(child: CircularProgressIndicator());
      }

      if (controller.hasTeamError.value) {
        return _buildErrorWidget(
          controller.teamErrorMessage.value,
          () => controller.loadTeamData(),
        );
      }

      if (controller.teamData.isEmpty) {
        return Center(child: Text('no_team_data_str'.tr));
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'team_persistency_str'.tr,
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingMedium),

          // Team table with clickable names
          _buildTeamTable(),
        ],
      );
    });
  }

  Widget _buildTeamTable() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0075BD),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radiusSmall),
                topRight: Radius.circular(radiusSmall),
              ),
            ),
            child: Row(
              children:
                  ["name_str".tr, "P-13", "P-25", "P-37", "P-49", "P-61"].map((
                    header,
                  ) {
                    return Expanded(
                      flex: header == "name_str".tr ? 2 : 1,
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          header,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),

          // Data rows
          ...controller.teamData.map((agent) {
            if (agent.name == '') return Container();

            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  // Name column (clickable)
                  Expanded(
                    flex: 2,
                    child: InkWell(
                      onTap: () => _navigateToSubAgentDetail(agent),
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          agent.name,
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),

                  // Persistency columns
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.formatPercentage(agent.persistency13),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.formatPercentage(agent.persistency25),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.formatPercentage(agent.persistency37),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.formatPercentage(agent.persistency49),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(paddingSmall),
                      child: Text(
                        controller.formatPercentage(agent.persistency61),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Center(
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red),
          SizedBox(height: paddingSmall),
          Text(message),
          SizedBox(height: paddingMedium),
          ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
        ],
      ),
    );
  }

  void _navigateToSubAgentDetail(agent) {
    // Navigate to another agent detail page
    Get.toNamed('/agent-detail?agentCode=${agent.agentCode}');
  }
}
