import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/auth/forgot_password_sent_controller.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ForgotPasswordSentPage extends StatefulWidget {
  const ForgotPasswordSentPage({super.key});

  @override
  State<ForgotPasswordSentPage> createState() => _ForgotPasswordSentPageState();
}

class _ForgotPasswordSentPageState extends State<ForgotPasswordSentPage> {
  final ForgotPasswordSentController controller = Get.put(
    ForgotPasswordSentController(),
  );

  @override
  void initState() {
    super.initState();
    // Set email data from arguments for resend functionality
    var args = Get.arguments;
    if (args != null) {
      String email = args['email'] ?? '';
      String agentCode = args['agentCode'] ?? '';
      controller.setEmailData(agentCode: agentCode, email: email);
    }
  }

  @override
  Widget build(BuildContext context) {
    var args = Get.arguments;
    String email = args['email_encrypted'] ?? '';
    return BaseFirstLogin(
      backEnabled: true,
      title: 'title_forgot_password'.tr,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingExtraLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: Get.width / 1.5,
              child: Utils.cachedSvgWrapper(
                'icon/illustration-empty-inbox.svg',
              ),
            ),
            Text(
              'title_email_sent'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 20,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            mailText(context, email),
            SizedBox(height: paddingMedium),
            Text(
              'title_verif_not_received'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: kColorTextTersier),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            // Countdown timer and resend functionality
            Obx(() {
              if (controller.isCountdownActive.value) {
                // Show countdown timer
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Mohon Menunggu',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: kColoTuatara400,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(width: paddingExtraSmall),
                    Text(
                      controller.formattedCountdown,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: kColoTuatara400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              } else {
                // Show resend button
                return Obx(() {
                  return GestureDetector(
                    onTap:
                        controller.isLoading.value
                            ? null
                            : controller.onTapResend,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: paddingMedium,
                        vertical: paddingSmall,
                      ),
                      decoration: BoxDecoration(
                        color:
                            controller.isLoading.value
                                ? kColorTextTersier.withValues(alpha: 0.3)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child:
                          controller.isLoading.value
                              ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: paddingSmall),
                                  Text(
                                    'Mengirim...',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              )
                              : Text(
                                'title_resent'.tr,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                textAlign: TextAlign.center,
                              ),
                    ),
                  );
                });
              }
            }),
          ],
        ),
      ),
    );
  }
}

Text mailText(context, String email) {
  final parts = 'sub_title_email_sent'.tr.split('@email');
  return Text.rich(
    TextSpan(
      children: [
        TextSpan(
          text: parts[0].replaceAll('"', ""),
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        TextSpan(
          text: email,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        TextSpan(
          text: parts.length > 1 ? parts[1].replaceAll('"', '') : '',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    ),
    textAlign: TextAlign.center,
  );
}
