import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/auth/forogot_password_controller.dart';
import 'package:pdl_superapp/pages/authentication/first_login/base_first_login.dart';
import 'package:pdl_superapp/utils/constants.dart';

class ForgotPasswordPage extends StatelessWidget {
  ForgotPasswordPage({super.key});

  final ForogotPasswordController controller = Get.put(
    ForogotPasswordController(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseFirstLogin(
      backEnabled: true,
      title: 'title_forgot_password'.tr,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: paddingMedium,
          vertical: paddingExtraLarge,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'title_forgot_password'.tr,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontSize: 20),
            ),
            Text(
              'sub_title_forgot_password'.tr,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(height: 1.2),
            ),
            SizedBox(height: paddingLarge),
            Obx(
              () => PdlTextField(
                label: 'label_form_agent_code'.tr,
                hint: 'hint_agent_code'.tr,
                textController: controller.agentCodeTextController,
                onChanged: (val) => controller.onChangeText(),
                borderColor: controller.isInvalid.isTrue ? kColorError : null,
              ),
            ),
            SizedBox(height: paddingMedium),
            Obx(
              () => PdlTextField(
                label: 'label_form_email'.tr,
                hint: 'hint_email'.tr,
                textController: controller.emailTextController,
                onChanged: (val) {
                  controller.onChangeText();
                },
                borderColor:
                    (controller.isInvalid.isTrue ||
                            controller.isEmailInvalid.isTrue)
                        ? kColorError
                        : null,
              ),
            ),
            // Email validation error
            Obx(() {
              if (controller.emailErrorText.value == '') {
                return Container();
              }
              return Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingSmall),
                margin: EdgeInsets.only(top: paddingSmall),
                decoration: BoxDecoration(
                  color: kColorGlobalBgRed,
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                child: Text(
                  controller.emailErrorText.value,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                ),
              );
            }),
            // General error message
            Obx(() {
              if (controller.errorText.value == '') {
                return Container();
              }
              return Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingMedium),
                margin: EdgeInsets.only(top: paddingLarge),
                decoration: BoxDecoration(
                  color: kColorGlobalBgRed,
                  borderRadius: BorderRadius.circular(radiusMedium),
                ),
                child: Text(
                  controller.errorText.value,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: kColorGlobalRed),
                ),
              );
            }),
            Container(
              padding: EdgeInsets.only(top: paddingLarge),
              width: Get.width,
              child: Obx(
                () => PdlButton(
                  controller: controller,
                  onPressed:
                      controller.isBtnAvailabel.isTrue
                          ? () => controller.onTapNext()
                          : null,
                  title: 'button_next'.tr,
                  analyticsName: 'Next Button Clicked',
                  analyticsSection: 'Forgot Password Page',
                  analyticsData: {
                    'form_valid': controller.isBtnAvailabel.value,
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
