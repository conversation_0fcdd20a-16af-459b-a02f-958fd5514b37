import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/login/login_header.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class BaseFirstLogin extends StatelessWidget {
  final String title;
  final Widget child;
  final bool? backEnabled;
  const BaseFirstLogin({
    super.key,
    required this.title,
    required this.child,
    this.backEnabled,
  });

  @override
  Widget build(BuildContext context) {
    // Define breakpoint for wide screens (tablets, desktops)
    final bool isWideScreen = kIsWeb;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      bottomSheet:
          isWideScreen
              ? null
              : AnimatedContainer(
                duration: Duration(milliseconds: 300),
                child: BottomSheet(
                  onClosing: () {},
                  enableDrag: false,
                  builder: (context) {
                    return Container(
                      width: Get.width,
                      height: calculateBottomSheetHeight(context),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20),
                        ),
                      ),
                      child: SingleChildScrollView(child: child),
                    );
                  },
                ),
              ),
      body: isWideScreen ? _layoutWeb(context) : _layoutMobile(context),
    );
  }

  Widget _layoutMobile(context) {
    return Column(
      children: [
        LoginHeader(
          child: Container(
            width: Get.width,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            margin: EdgeInsets.only(top: 50),
            alignment: Alignment.center,
            child: Row(
              children: [
                GestureDetector(
                  onTap: backEnabled == true ? () => Get.back() : null,
                  child: Icon(
                    Icons.arrow_back,
                    color:
                        backEnabled == true ? Colors.white : Colors.transparent,
                  ),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Icon(Icons.arrow_back, color: Colors.transparent),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _layoutWeb(context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Utils.cachedImageWrapper(
            'image/img-bg-web.png',
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: paddingLarge,
                  horizontal: paddingLarge,
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: backEnabled == true ? () => Get.back() : null,
                      child: Icon(
                        Icons.arrow_back,
                        color:
                            backEnabled == true
                                ? Colors.white
                                : Colors.transparent,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        title,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    Icon(Icons.arrow_back, color: Colors.transparent),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  color: Theme.of(context).colorScheme.surface,
                  width: Get.width,
                  child: Column(
                    children: [SizedBox(width: Get.width / 2, child: child)],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  double calculateBottomSheetHeight(BuildContext context) {
    double defaultHeight =
        Get.height - 120; // Default height when keyboard is hidden
    double keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    double maxHeight = Get.height - 400; // Limit height
    double adjustedHeight = defaultHeight - keyboardHeight;

    return adjustedHeight < maxHeight ? maxHeight : adjustedHeight;
  }
}
