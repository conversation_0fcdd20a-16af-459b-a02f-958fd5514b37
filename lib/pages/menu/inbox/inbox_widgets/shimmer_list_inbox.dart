import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerListInbox extends StatelessWidget {
  const ShimmerListInbox({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 6,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return _ItemShimmerInbox();
      },
    );
  }
}

class _ItemShimmerInbox extends StatelessWidget {
  const _ItemShimmerInbox();

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.only(bottom: paddingLarge),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 150,
                    height: 10,
                    decoration: BoxDecoration(color: Colors.white),
                  ),
                  SizedBox(height: paddingSmall),
                  Container(
                    width: double.infinity,
                    height: 10,
                    decoration: BoxDecoration(color: Colors.white),
                  ),
                  SizedBox(height: paddingSmall),
                  Container(
                    width: double.infinity,
                    height: 10,
                    decoration: BoxDecoration(color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
