import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:shimmer/shimmer.dart';

class ShimmerDetailInbox extends StatelessWidget {
  const ShimmerDetailInbox({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.only(bottom: paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingMedium),
            <PERSON><PERSON>(
              alignment: Alignment.topRight,
              child: Container(
                width: 100,
                height: 10,
                decoration: BoxDecoration(color: Colors.white),
              ),
            ),
            SizedBox(height: paddingMedium),
            Container(
              width: 150,
              height: 15,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingLarge),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),

            Container(
              margin: EdgeInsets.symmetric(vertical: paddingMedium),
              width: Get.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: kLine),
              ),
              child: Column(
                children: [
                  Container(
                    width: Get.width,
                    padding: EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                      color: kLine,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(paddingSmall),
                    child: Column(
                      children: [
                        _itemKeagenan(),
                        _itemKeagenan(),
                        _itemKeagenan(),
                        _itemKeagenan(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
            SizedBox(height: paddingSmall),
          ],
        ),
      ),
    );
  }

  Container _itemKeagenan() {
    return Container(
      margin: EdgeInsets.only(bottom: paddingMedium),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              height: 10,
              decoration: BoxDecoration(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
