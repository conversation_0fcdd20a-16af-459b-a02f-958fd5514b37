import 'dart:developer';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/photo_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class PhotoPage extends StatefulWidget {
  const PhotoPage({super.key});

  @override
  State<PhotoPage> createState() => _PhotoPageState();
}

class _PhotoPageState extends State<PhotoPage> {
  final PdlPHotoController controller = Get.put(PdlPHotoController());

  // KTP card dimensions ratio (85.6 x 53.98 mm)
  double ktpAspectRatio = 85.6 / 53.98;

  String title = '';
  String type = '';

  @override
  void initState() {
    super.initState();
    title = Get.arguments['title'] ?? '';
    type = Get.arguments['type'] ?? '';
    controller.type = type;

    if (type == kPhotoTypeSelfieKtp) {
      ktpAspectRatio = 3 / 4;
    } else if (type == kPhotoTypePasFoto) {
      ktpAspectRatio = 1;
    }

    // Initialize camera when page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeCameraWithPermissionCheck();
    });
  }

  Future<void> _initializeCameraWithPermissionCheck() async {
    log('📱 Photo page: Starting camera initialization...');
    final cameraInitialized = await controller.initializeCamera();
    log('📱 Photo page: Camera initialization result: $cameraInitialized');

    if (!cameraInitialized && controller.cameraPermissionDenied.value) {
      log('📱 Photo page: Permission denied, going back...');
      // Use controller's reset method instead of local disposal
      controller.resetCameraState();
      Get.back();
    } else if (!cameraInitialized) {
      log(
        '📱 Photo page: Camera initialization failed but no permission issue',
      );
    } else {
      log('📱 Photo page: Camera initialization successful');
    }
  }

  @override
  void dispose() {
    // Use controller's reset method for proper disposal
    controller.resetCameraState();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // Use controller's reset method for proper disposal
          controller.resetCameraState();
        }
      },
      child: BaseDetailPage(
        title: 'Foto $title',
        backEnabled: true,
        controller: controller,
        onRefresh: () {},
        onBack: () {
          // Use controller's reset method for proper disposal
          controller.resetCameraState();
          Get.back();
        },
        bottomAction: () {
          controller.isTakingPicture.value ? null : controller.takePicture();
        },
        bottomText: 'Ambil Foto',
        child: SizedBox(
          height: Get.height - 180,
          child: Column(
            children: [
              SizedBox(height: paddingMedium),
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: TitleWidget(title: 'Ambil Foto $title'),
              ),
              Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: Text('Ambil foto sesuai tanda yang tersedia'),
              ),
              kIsWeb
                  ? SizedBox(height: paddingMedium)
                  : Expanded(child: Container()),
              kIsWeb
                  ? Expanded(
                    child: Container(
                      width: Get.width - paddingMedium,
                      margin: EdgeInsets.symmetric(
                        horizontal: paddingMedium / 2,
                      ),
                      child: AspectRatio(
                        aspectRatio:
                            3 / 4, // Mobile-like 3:4 aspect ratio for web
                        child: Container(
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(radiusMedium),
                            color:
                                Colors
                                    .black, // Black background for better camera appearance
                          ),
                          child: _buildCameraUI(context),
                        ),
                      ),
                    ),
                  )
                  : Container(
                    clipBehavior: Clip.hardEdge,
                    width: Get.width - paddingMedium,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(radiusMedium),
                    ),
                    height: Get.height / 1.5,
                    child: _buildCameraUI(context),
                  ),
              kIsWeb
                  ? SizedBox(height: paddingMedium)
                  : Expanded(child: Container()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCameraUI(BuildContext context) {
    return Obx(() {
      // Check if camera permission is denied
      if (controller.cameraPermissionDenied.value) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.camera_alt_outlined, size: 64, color: Colors.grey),
              SizedBox(height: paddingMedium),
              Text(
                'Camera Permission Required',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingSmall),
              Text(
                'Please allow camera access to take photos',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingMedium),
              ElevatedButton(
                onPressed: () async {
                  // Reset camera state before trying again
                  controller.resetCameraState();
                  await _initializeCameraWithPermissionCheck();
                },
                child: Text('Try Again'),
              ),
            ],
          ),
        );
      }

      // Show loading while initializing or switching camera
      if (!controller.isCameraInitialized.value ||
          controller.isInitializing.value ||
          controller.isSwitchingCamera.value) {
        // For web, show more informative loading with retry option after some time
        if (kIsWeb &&
            !controller.isInitializing.value &&
            !controller.isSwitchingCamera.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.camera_alt_outlined, size: 64, color: Colors.grey),
                SizedBox(height: paddingMedium),
                Text(
                  'Camera Not Available',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: paddingSmall),
                Text(
                  'Unable to access camera. Please check browser permissions.',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: paddingMedium),
                ElevatedButton(
                  onPressed: () async {
                    // Reset camera state before trying again
                    controller.resetCameraState();
                    await _initializeCameraWithPermissionCheck();
                  },
                  child: Text('Retry Camera'),
                ),
              ],
            ),
          );
        }
        return const Center(child: CircularProgressIndicator());
      }

      // Ensure camera controller is not null before building preview
      if (controller.cameraController.value == null) {
        return const Center(child: CircularProgressIndicator());
      }

      return Stack(
        children: [
          // Camera preview with web-optimized display
          Positioned.fill(
            child:
                kIsWeb ? _buildWebCameraPreview() : _buildMobileCameraPreview(),
          ),

          // Semi-transparent overlay with cutout
          if (type != '') _buildOverlayWithCutout(context),

          // Camera flip button
          Positioned(
            bottom: 5,
            right: 0,
            left: 0,
            child: Center(child: _buildCameraFlipButton()),
          ),
        ],
      );
    });
  }

  Widget _buildWebCameraPreview() {
    // For web, optimize for mobile-like camera experience while maintaining overlay compatibility
    final cameraController = controller.cameraController.value;

    // Additional null check for web stability with better loading state
    if (cameraController == null || !cameraController.value.isInitialized) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              controller.isInitializing.value
                  ? 'Initializing camera...'
                  : 'Loading camera...',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
            ),
            if (kIsWeb) ...[
              const SizedBox(height: 8),
              Text(
                'Please allow camera access when prompted',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.white54),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    final previewSize = cameraController.value.previewSize;
    if (previewSize == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate camera aspect ratio
        final cameraAspectRatio = previewSize.width / previewSize.height;
        final containerAspectRatio =
            constraints.maxWidth / constraints.maxHeight;

        // For web, we want to fill more of the container for better quality
        // but still maintain compatibility with overlay
        double previewWidth;
        double previewHeight;

        if (cameraAspectRatio > containerAspectRatio) {
          // Camera is wider - fit by height and allow some cropping on sides
          previewHeight = constraints.maxHeight;
          previewWidth = previewHeight * cameraAspectRatio;
        } else {
          // Camera is taller - fit by width and allow some cropping on top/bottom
          previewWidth = constraints.maxWidth;
          previewHeight = previewWidth / cameraAspectRatio;
        }

        return Center(
          child: ClipRect(
            child: SizedBox(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: OverflowBox(
                maxWidth: previewWidth,
                maxHeight: previewHeight,
                child: SizedBox(
                  width: previewWidth,
                  height: previewHeight,
                  child: CameraPreview(cameraController),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMobileCameraPreview() {
    // For mobile, keep the original FittedBox approach
    return FittedBox(
      fit: BoxFit.cover,
      child: SizedBox(
        width: controller.cameraController.value!.value.previewSize!.height,
        height: controller.cameraController.value!.value.previewSize!.width,
        child: CameraPreview(controller.cameraController.value!),
      ),
    );
  }

  Widget _buildOverlayWithCutout(BuildContext context) {
    // Calculate KTP cutout dimensions
    final screenWidth = MediaQuery.of(context).size.width;
    final cutoutWidth = screenWidth * 0.85; // 85% of screen width
    final cutoutHeight = cutoutWidth / ktpAspectRatio;

    return Stack(
      children: [
        // Semi-transparent gray overlay with cutout
        ClipPath(
          clipper: CutoutClipper(
            cutoutWidth: cutoutWidth,
            cutoutHeight: cutoutHeight,
            borderRadius: 8,
          ),
          child: Container(
            width: Get.width,
            height: Get.height,
            color: Colors.black.withValues(alpha: 0.5),
          ),
        ),

        // Blue border for the cutout
        Center(
          child: Container(
            width: cutoutWidth,
            height: cutoutHeight,
            decoration: BoxDecoration(
              color: Colors.transparent,
              border: Border.all(color: kColorGlobalBlue, width: 2.0),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                // Corner guides
                _buildCornerGuide(Alignment.topLeft),
                _buildCornerGuide(Alignment.topRight),
                _buildCornerGuide(Alignment.bottomLeft),
                _buildCornerGuide(Alignment.bottomRight),

                // Instruction text
                if (type == kPhotoTypeKtp)
                  Positioned(
                    bottom: 10,
                    left: 0,
                    right: 0,
                    child: Text(
                      'Posisikan KTP di dalam bingkai',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 3,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCornerGuide(Alignment alignment) {
    // Corner guide size
    const double size = 20;
    const double thickness = 3;

    // Determine position based on alignment
    double? top = alignment.y < 0 ? 0 : null;
    double? bottom = alignment.y > 0 ? 0 : null;
    double? left = alignment.x < 0 ? 0 : null;
    double? right = alignment.x > 0 ? 0 : null;

    return Positioned(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: SizedBox(
        width: size,
        height: size,
        child: CustomPaint(
          painter: CornerPainter(
            color: kColorGlobalBlue,
            thickness: thickness,
            isTopLeft: alignment == Alignment.topLeft,
            isTopRight: alignment == Alignment.topRight,
            isBottomLeft: alignment == Alignment.bottomLeft,
            isBottomRight: alignment == Alignment.bottomRight,
          ),
        ),
      ),
    );
  }

  Widget _buildCameraFlipButton() {
    return Obx(() {
      // Only show flip button if there are multiple cameras available
      if (controller.availableCamerasList.length <= 1) {
        return const SizedBox.shrink();
      }

      return Center(
        child: Container(
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(25),
              onTap:
                  controller.isSwitchingCamera.value
                      ? null
                      : controller.switchCamera,
              child: Container(
                width: 50,
                height: 50,
                padding: const EdgeInsets.all(12),
                child:
                    controller.isSwitchingCamera.value
                        ? const CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        )
                        : const Icon(
                          Icons.flip_camera_ios,
                          color: kColorGlobalBlue,
                          size: 26,
                        ),
              ),
            ),
          ),
        ),
      );
    });
  }
}

// Custom painter for corner guides
class CornerPainter extends CustomPainter {
  final Color color;
  final double thickness;
  final bool isTopLeft;
  final bool isTopRight;
  final bool isBottomLeft;
  final bool isBottomRight;

  CornerPainter({
    required this.color,
    required this.thickness,
    this.isTopLeft = false,
    this.isTopRight = false,
    this.isBottomLeft = false,
    this.isBottomRight = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = thickness
          ..strokeCap = StrokeCap.round;

    final path = Path();

    if (isTopLeft) {
      path.moveTo(0, size.height / 2);
      path.lineTo(0, 0);
      path.lineTo(size.width / 2, 0);
    } else if (isTopRight) {
      path.moveTo(size.width / 2, 0);
      path.lineTo(size.width, 0);
      path.lineTo(size.width, size.height / 2);
    } else if (isBottomLeft) {
      path.moveTo(0, size.height / 2);
      path.lineTo(0, size.height);
      path.lineTo(size.width / 2, size.height);
    } else if (isBottomRight) {
      path.moveTo(size.width / 2, size.height);
      path.lineTo(size.width, size.height);
      path.lineTo(size.width, size.height / 2);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom clipper for creating a cutout in the overlay
class CutoutClipper extends CustomClipper<Path> {
  final double cutoutWidth;
  final double cutoutHeight;
  final double borderRadius;

  CutoutClipper({
    required this.cutoutWidth,
    required this.cutoutHeight,
    this.borderRadius = 0,
  });

  @override
  Path getClip(Size size) {
    // Calculate the position of the cutout
    final cutoutLeft = (size.width - cutoutWidth) / 2;
    final cutoutTop = (size.height - cutoutHeight) / 2;
    final cutoutRight = cutoutLeft + cutoutWidth;
    final cutoutBottom = cutoutTop + cutoutHeight;

    final path = Path()..addRect(Rect.fromLTWH(0, 0, size.width, size.height));

    if (borderRadius > 0) {
      // Create a rounded rectangle cutout
      path.addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTRB(cutoutLeft, cutoutTop, cutoutRight, cutoutBottom),
          Radius.circular(borderRadius),
        ),
      );
    } else {
      // Create a rectangle cutout
      path.addRect(
        Rect.fromLTRB(cutoutLeft, cutoutTop, cutoutRight, cutoutBottom),
      );
    }

    // Use even-odd fill type to create a cutout
    return path..fillType = PathFillType.evenOdd;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => true;
}
