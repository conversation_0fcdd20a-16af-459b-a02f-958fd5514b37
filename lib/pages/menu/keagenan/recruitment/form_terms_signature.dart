import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/share_content.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/components/webview.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_terms_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:signature/signature.dart';
import 'dart:convert';
import 'dart:typed_data';

class FormTermsSignature extends StatelessWidget {
  final RecruitmentFormController controller;

  const FormTermsSignature({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    final termsController = controller.termsController;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TitleWidget(title: 'Tanda Tangan Perjanjian Keagenan'),

        // PKAJ Document
        Obx(
          () => _documentCard(
            context,
            title: 'Perjanjian Keagenan Asuransi Jiwa (PKAJ)',
            onTap: () {
              _navigateToDocumentDetail(
                context,
                url: termsController.pkajUrl,
                isViewed: termsController.isPkajDocumentViewed.value,
                title: 'Perjanjian Keagenan Asuransi Jiwa (PKAJ)',
                onUnderstood: () {
                  termsController.markPkajDocumentViewed();
                  Get.back();
                },
              );
            },
            isViewed: termsController.isPkajDocumentViewed,
            canOpen: termsController.canOpenDocument('PKAJ').value,
          ),
        ),

        if (controller.verificationController.candidateLevelController.text !=
            'BP')
          // PMKAJ Document
          Obx(
            () => _documentCard(
              context,
              title: 'Perjanjian Manajemen Keagenan Asuransi Jiwa (PMKAJ)',
              onTap: () {
                _navigateToDocumentDetail(
                  context,
                  url: termsController.pmkajUrl,
                  isViewed: termsController.isPmkajDocumentViewed.value,
                  title: 'Perjanjian Manajemen Keagenan Asuransi Jiwa (PMKAJ)',
                  onUnderstood: () {
                    termsController.markPmkajDocumentViewed();
                    Get.back();
                  },
                );
              },
              isViewed: termsController.isPmkajDocumentViewed,
              canOpen: termsController.canOpenDocument('PMKAJ').value,
            ),
          ),

        // Kode Etik Document
        Obx(
          () => _documentCard(
            context,
            title: 'Kode Etik Agen Asuransi',
            onTap: () {
              _navigateToDocumentDetail(
                context,
                url: termsController.etikUrl,
                isViewed: termsController.isKodeEtikDocumentViewed.value,
                title: 'Kode Etik Agen Asuransi',
                onUnderstood: () {
                  termsController.markKodeEtikDocumentViewed();
                  Get.back();
                },
              );
            },
            isViewed: termsController.isKodeEtikDocumentViewed,
            canOpen: termsController.canOpenDocument('KODE_ETIK').value,
          ),
        ),

        // Anti Twisting Document
        Obx(
          () => _documentCard(
            context,
            title: 'Peraturan Anti Twisting',
            onTap: () {
              _navigateToDocumentDetail(
                context,
                url: termsController.antiTwistUrl,
                isViewed: termsController.isAntiTwistingDocumentViewed.value,
                title: 'Peraturan Anti Twisting',
                onUnderstood: () {
                  termsController.markAntiTwistingDocumentViewed();
                  Get.back();
                },
              );
            },
            isViewed: termsController.isAntiTwistingDocumentViewed,
            canOpen: termsController.canOpenDocument('ANTI_TWISTING').value,
          ),
        ),

        _padding(
          Text(
            'Paraf dan Tanda Tangan',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
        ),
        // Warning when not all TNC are checked
        Obx(
          () =>
              termsController.isAllTermsRead.isTrue
                  ? Container()
                  : _padding(
                    Container(
                      width: Get.width,
                      padding: EdgeInsets.all(paddingMedium),
                      decoration: BoxDecoration(
                        color: kColorGlobalBgRed,
                        borderRadius: BorderRadius.circular(radiusSmall),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber_rounded,
                            color: kColorGlobalRed,
                          ),
                          SizedBox(width: paddingSmall),
                          Expanded(
                            child: Text(
                              'Mohon membaca seluruh dokumen diatas sebelum menandatangani dokumen',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(color: kColorGlobalRed),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
        ),
        // Paraf & ttd muncul ketika all the TNC are checked
        _padding(
          Obx(
            () =>
                termsController.isAllTermsRead.value
                    ? SizedBox(
                      width: Get.width,
                      child: Row(
                        children: [
                          _parafCard(context, termsController),
                          SizedBox(width: paddingMedium),
                          _signatureCard(context, termsController),
                        ],
                      ),
                    )
                    : SizedBox.shrink(),
          ),
        ),
        _padding(
          Obx(
            () => GestureDetector(
              onTap:
                  (controller.isFormDisabled.isTrue ||
                          !termsController.canEnableCheckbox())
                      ? null
                      : () => termsController.toggleAgreement(
                        !termsController.isAgreementChecked.value,
                      ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    termsController.isAgreementChecked.value
                        ? Icons.check_box_rounded
                        : Icons.check_box_outline_blank_rounded,
                    color:
                        termsController.isAgreementChecked.value
                            ? kColorGlobalBlue
                            : termsController.canEnableCheckbox()
                            ? (Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight)
                            : (Get.isDarkMode
                                ? kColorTextTersier
                                : kColorTextTersierLight),
                  ),
                  SizedBox(width: paddingSmall),
                  Expanded(
                    child: Text(
                      'Saya mengetahui dan menyetujui bahwa data pribadi (termasuk namun tidak terbatas pada foto dan/atau dokumen yang telah diunggah) yang saya berikan akan dikumpulkan, diproses, dan disimpan oleh Panin Dai-ichi Life untuk tujuan rekrutmen sesuai dengan hukum pelindungan data yang berlaku. Saya memahami bahwa data saya dapat dibagikan dengan departemen internal yang relevan dan pihak ketiga yang berwenang dalam proses rekrutmen. Informasi pribadi saya hanya akan disimpan selama diperlukan untuk tujuan rekrutmen dan/atau sesuai dengan ketentuan hukum yang berlaku.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            termsController.canEnableCheckbox()
                                ? (Get.isDarkMode
                                    ? kColorTextTersier
                                    : kColorTextTersierLight)
                                : (Get.isDarkMode
                                    ? kColorTextTersier.withValues(alpha: 0.5)
                                    : kColorTextTersierLight.withValues(
                                      alpha: 0.5,
                                    )),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (controller.isFormDisabled.isFalse) _padding(Divider()),
        if (controller.isFormDisabled.isFalse)
          _padding(
            Text(
              'Anda dapat membagikan halaman ini untuk ditandatangani secara mandiri oleh kandidat.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
              ),
            ),
          ),
        if (controller.isFormDisabled.isFalse)
          _padding(
            SizedBox(
              width: Get.width,
              child: Obx(
                () => PdlButton(
                  title: 'share_str'.tr,
                  suffixIcon: Icon(Icons.share_outlined),
                  onPressed:
                      termsController.isSharing.value
                          ? null
                          : () => PdlBottomSheet(
                            title: 'share_str'.tr,
                            radius: radiusSmall,
                            titleStyle: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w700),
                            content: ShareContent(
                              text:
                                  'Anda sedang mengajukan pendaftaran sebagai tenaga pemasar PT. Panin Dai-ichi Life, silakan buka link berikut ini untuk menyelesaikan pendaftaran:',
                              url: controller.termsController.shareLink(),
                              onSuccess: () {
                                Get.offNamedUntil(
                                  Routes.MENU_KEAGENAN,
                                  (route) => false,
                                );
                              },
                            ),
                          ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Padding _documentCard(
    BuildContext context, {
    required String title,
    required Function() onTap,
    required RxBool isViewed,
    required bool canOpen,
  }) {
    return _padding(
      Obx(
        () => GestureDetector(
          onTap: (controller.isFormDisabled.isTrue || !canOpen) ? null : onTap,
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  isViewed.isTrue
                      ? Icons.check_circle
                      : Icons.check_circle_outline,
                  color:
                      isViewed.isTrue
                          ? kColorGlobalGreen
                          : (Get.isDarkMode
                              ? kColorBorderDark
                              : kColorBorderLight),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color:
                          canOpen
                              ? null
                              : (Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight),
                    ),
                  ),
                ),
                SizedBox(width: paddingSmall),
                Icon(
                  Icons.chevron_right,
                  color:
                      canOpen
                          ? null
                          : (Get.isDarkMode
                              ? kColorTextTersier
                              : kColorTextTersierLight),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }

  Expanded _parafCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Paraf',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isParafUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (termsController.isParafCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap:
                    controller.isFormDisabled.isTrue
                        ? null
                        : () {
                          _showSignaturePad(
                            context,
                            title: 'Paraf',
                            onSave: (signatureData) {
                              termsController.setParafData(signatureData);
                            },
                            currentData: termsController.parafData.value,
                          );
                        },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: kColorBgLight,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isParafCompleted.value
                            ? termsController.parafData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(
                                      termsController.parafData.value,
                                    ),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Paraf\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : termsController.parafUrl.value != ''
                                ? Utils.cachedImageWrapper(
                                  termsController.parafUrl.value,
                                  isFullUrl: true,
                                  fit: BoxFit.contain,
                                )
                                : Center(
                                  child: Text(
                                    'Paraf\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.parafError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.parafError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _signatureCard(
    BuildContext context,
    FormTermsController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Tanda Tangan',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (termsController.isSignatureUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (termsController.isSignatureCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap:
                    controller.isFormDisabled.isTrue
                        ? null
                        : () {
                          _showSignaturePad(
                            context,
                            title: 'Tanda Tangan',
                            onSave: (signatureData) {
                              termsController.setSignatureData(signatureData);
                            },
                            currentData: termsController.signatureData.value,
                          );
                        },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: kColorBgLight,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        termsController.isSignatureCompleted.value
                            ? termsController.signatureData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(
                                      termsController.signatureData.value,
                                    ),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Tanda Tangan\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : termsController.signatureUrl.value != ''
                                ? Utils.cachedImageWrapper(
                                  termsController.signatureUrl.value,
                                  isFullUrl: true,
                                  fit: BoxFit.contain,
                                )
                                : Center(
                                  child: Text(
                                    'Tanda Tangan\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (termsController.signatureError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    termsController.signatureError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSignaturePad(
    BuildContext context, {
    required String title,
    required Function(String) onSave,
    required String currentData,
  }) {
    final SignatureController signatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );

    // Note: The signature package doesn't support loading from base64 directly
    // We'll show empty pad but indicate there's existing data in the UI

    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  // color: kColorGlobalBgBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusMedium),
                    topRight: Radius.circular(radiusMedium),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          color: kColorPaninBlue,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Get.back(),
                      child: Icon(Icons.close, color: kColorPaninBlue),
                    ),
                  ],
                ),
              ),

              // Signature pad area
              Container(
                margin: EdgeInsets.all(paddingMedium),
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                  ),
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(radiusSmall),
                  child: Signature(
                    controller: signatureController,
                    backgroundColor: Colors.white,
                  ),
                ),
              ),

              // Current signature indicator
              if (currentData.isNotEmpty)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                  padding: EdgeInsets.all(paddingSmall),
                  decoration: BoxDecoration(
                    color: kColorGlobalBgGreen,
                    borderRadius: BorderRadius.circular(radiusSmall),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Text(
                          'Terdapat $title yang tersimpan. Buat baru untuk mengganti.',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: kColorGlobalGreen),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action buttons
              Container(
                padding: EdgeInsets.all(paddingMedium),
                child: Row(
                  children: [
                    Expanded(
                      child: PdlButton(
                        title: 'Ulangi',
                        onPressed: () {
                          signatureController.clear();
                        },
                        backgroundColor: Colors.transparent,
                        foregorundColor:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                        borderColor:
                            Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight,
                      ),
                    ),
                    SizedBox(width: paddingMedium),
                    Expanded(
                      child: PdlButton(
                        title: 'Simpan',
                        onPressed: () async {
                          if (signatureController.isNotEmpty) {
                            final Uint8List? signature =
                                await signatureController.toPngBytes();
                            if (signature != null) {
                              final String base64String = base64Encode(
                                signature,
                              );
                              onSave(base64String);
                              Get.back();
                            }
                          } else {
                            // If empty but there was existing data, clear it
                            if (currentData.isNotEmpty) {
                              onSave('');
                              Get.back();
                            } else {
                              Get.snackbar(
                                'Peringatan',
                                'Silakan buat $title terlebih dahulu',
                                backgroundColor: kColorGlobalBgRed,
                                colorText: kColorGlobalRed,
                              );
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDocumentDetail(
    BuildContext context, {
    required String title,
    required RxString url,
    required bool isViewed,
    required Function() onUnderstood,
  }) {
    RxBool isScrolledToBottom = false.obs;

    Get.to(
      () => BaseDetailPage(
        title: title,
        width: Get.width,
        controller: controller,
        onRefresh: () {},
        bottomAction: onUnderstood,
        backEnabled: true,
        bottomText: 'Sudah Mengerti',
        scrollPhysics: NeverScrollableScrollPhysics(),
        isBottomActionEnabled: isScrolledToBottom,
        child: SizedBox(
          width: Get.width,
          height: Get.height / 1.25,
          child: WebviewPage(
            fullUrl: url.value,
            onScrolledToBottom: (scrolled) {
              if (isViewed == true) {
                isScrolledToBottom.value = false;
              } else {
                isScrolledToBottom.value = scrolled;
              }
            },
          ),
        ),
      ),
    );
  }
}
