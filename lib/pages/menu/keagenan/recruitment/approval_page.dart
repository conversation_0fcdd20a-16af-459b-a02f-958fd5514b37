import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/riwayat_approval_content_dialog.dart';
import 'package:pdl_superapp/components/status_label.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/approval_controller.dart';
import 'package:pdl_superapp/models/body/approval_body.dart';
import 'package:pdl_superapp/models/recruitment_api_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ApprovalPage extends StatelessWidget {
  ApprovalPage({super.key});

  final ApprovalController controller = Get.put(ApprovalController());

  @override
  Widget build(BuildContext context) {
    return Obx(() => _body(context));
  }

  BaseDetailPage _body(BuildContext context) {
    return BaseDetailPage(
      title: 'Formulir Rekrutment',
      backEnabled: true,
      controller: controller,
      isBottomActionEnabled: controller.isSubmitButtonEnabled,
      bottomAction:
          // Only show bottom action for API data, not for draft data
          !controller.isDraftData.value && controller.isButtonAvailable.isTrue
              ? () {
                if ((controller.selectedStatusApproval.value == 'Tolak' &&
                    controller.remarksApproval.text.trim().isEmpty)) {
                  Get.snackbar(
                    'Lengkapi isian',
                    'Harap isi catatan',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: kColorGlobalBgRed,
                    colorText: kColorErrorText,
                  );
                  return;
                }
                if (controller.selectedStatusApproval.value == '') {
                  Get.snackbar(
                    'Lengkapi isian',
                    'Harap pilih status persetujuan',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: kColorGlobalBgRed,
                    colorText: kColorErrorText,
                  );
                  return;
                }
                controller.reqPostApproval(
                  body: ApprovalBody(
                    approvalHeaderId:
                        controller.recruitmentDetail.value.approvalHeader?.id,
                    action:
                        controller.selectedStatusApproval.value == 'Setuju'
                            ? StatusApproval.DISETUJUI.name
                            : controller.selectedStatusApproval.value == 'Tolak'
                            ? StatusApproval.DITOLAK.name
                            : StatusApproval.TERTUNDA.name,
                    remarks: controller.remarksApproval.text,
                  ),
                );
              }
              : null,
      bottomText: 'Submit',
      onRefresh: () async {
        await controller.refreshData();
      },
      child: Obx(() {
        return Container(
          padding: EdgeInsets.all(paddingMedium),
          child: Column(
            children: [
              _headerSection(context),
              SizedBox(height: paddingMedium),
              _recruiterSection(context),
              _passPhotoSection(),
              SizedBox(height: paddingMedium),
              _infoCandidateSection(context),
              SizedBox(height: paddingMedium),
              Divider(),
              SizedBox(height: paddingMedium),
              // Only show approval section for API data, not for draft data
              if (!controller.isDraftData.value &&
                  controller.isButtonAvailable.isTrue)
                Container(
                  width: Get.width,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(radiusSmall),
                    border: Border.all(
                      color:
                          Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: Get.width,
                        padding: EdgeInsets.all(paddingSmall),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(radiusSmall),
                            topRight: Radius.circular(radiusSmall),
                          ),
                          color:
                              Get.isDarkMode
                                  ? kColorBorderDark
                                  : kColorBorderLight,
                        ),
                        child: Text(
                          'Status Persetujuan',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                      SizedBox(height: paddingSmall),
                      Padding(
                        padding: EdgeInsetsGeometry.all(paddingSmall),
                        child: PdlDropDown(
                          title: 'Status Persetujuan',
                          item:
                              controller
                                          .recruitmentDetail
                                          .value
                                          .approvalHeader
                                          ?.approverRole ==
                                      'RECRUITER:${controller.currentAgentCode}'
                                  ? ['Setuju', 'Tolak']
                                  : ['Setuju', 'Tolak', 'Kembalikan'],
                          enabled: true,
                          selectedItem:
                              controller.selectedStatusApproval.value != ''
                                  ? controller.selectedStatusApproval.value
                                  : null,
                          disableSearch: true,
                          onChanged: (val) {
                            controller.selectedStatusApproval.value = val!;
                          },
                        ),
                      ),
                      SizedBox(height: paddingSmall),
                      Padding(
                        padding: EdgeInsetsGeometry.all(paddingSmall),
                        child: PdlTextField(
                          hint: 'Tambah catatan atau keterangan',
                          textController: controller.remarksApproval,
                          label:
                              'Catatan ${controller.selectedStatusApproval.value != 'Tolak' ? '(Opsional)' : ''}',
                          maxLength: 200,
                          height: 100,
                          isTextArea: true,
                          borderColor:
                              controller.selectedStatusApproval.value == 'Tolak'
                                  ? kColorError
                                  : null,
                          errorText:
                              controller.selectedStatusApproval.value == 'Tolak'
                                  ? 'Wajib diisi'
                                  : null,
                          keyboardType: TextInputType.multiline,
                        ),
                      ),
                      SizedBox(height: paddingMedium),
                    ],
                  ),
                ),
              // Only show approval history for API data
              Obx(() {
                if (controller.state.value == ControllerState.loading) {
                  return CircularProgressIndicator();
                } else if (!controller.isDraftData.value) {
                  if (controller.recruitmentDetail.value.approvalHeader !=
                      null) {
                    if (controller
                        .recruitmentDetail
                        .value
                        .approvalHeader!
                        .approvalDetails!
                        .isNotEmpty) {
                      return _approvalSection(context);
                    }
                  }
                  return Container();
                }
                return Container();
              }),
            ],
          ),
        );
      }),
    );
  }

  SizedBox _approvalSection(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(child: TitleSecondary(title: 'Pemantauan Persetujuan')),
              GestureDetector(
                onTap:
                    () => PdlBottomSheet(
                      title: 'Riwayat Persetujuan',
                      content: SizedBox(
                        width: Get.width,
                        child: RiwayatDialogContent(
                          approvalDetails:
                              controller
                                  .recruitmentDetail
                                  .value
                                  .approvalHeader!
                                  .approvalDetails!,
                        ),
                      ),
                    ),
                child: Text(
                  'Lihat Riwayat',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorPaninBlue,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
          if (controller
              .recruitmentDetail
              .value
              .approvalHeader!
              .approvalDetails!
              .isNotEmpty)
            ...controller
                .recruitmentDetail
                .value
                .approvalHeader!
                .approvalDetails!
                .map((detail) {
                  int index = controller
                      .recruitmentDetail
                      .value
                      .approvalHeader!
                      .approvalDetails!
                      .indexOf(detail);
                  bool isLast =
                      index ==
                      controller
                              .recruitmentDetail
                              .value
                              .approvalHeader!
                              .approvalDetails!
                              .length -
                          1;
                  return _approvalCard(detail, context, isLast);
                }),
        ],
      ),
    );
  }

  Widget _approvalCard(
    ApprovalDetail detail,
    BuildContext context,
    bool isLast,
  ) {
    return SizedBox(
      width: Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                CircleAvatar(),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        detail.actionBy?.name ?? '-',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Utils.getApprovalStatus(
                        context,
                        (detail.approvalStatus ?? '') == 'TERTUNDA'
                            ? 'DIKEMBALIKAN'
                            : detail.approvalStatus ?? '',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (isLast == false)
            Padding(
              padding: EdgeInsetsGeometry.symmetric(vertical: paddingSmall),
              child: Utils.cachedSvgWrapper(
                'icon/ic-linear-chevron down.svg',
                color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
              ),
            ),
        ],
      ),
    );
  }

  SizedBox _infoCandidateSection(context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TitleSecondary(title: 'Informasi Kandidat'),
          SizedBox(height: paddingSmall),
          _infoCandidateTextCard(
            title: 'Nama Lengkap',
            value:
                controller.isDraftData.value
                    ? controller.draftDetail.value.namaKtp ?? '-'
                    : controller.recruitmentDetail.value.fullName ?? '-',
          ),
          _infoCandidateTextCard(
            title: 'Level',
            value:
                controller.isDraftData.value
                    ? controller.draftDetail.value.candidateLevel ?? '-'
                    : controller.recruitmentDetail.value.positionLevel ?? '-',
          ),
          _infoCandidateTextCard(
            title: 'Kantor Cabang',
            value:
                controller.isDraftData.value
                    ? controller.draftDetail.value.candidateBranch ?? '-'
                    : controller.recruitmentDetail.value.branch?.branchName ??
                        '-',
          ),
          _infoCandidateTextCard(
            title: 'Nomor HP',
            value:
                controller.isDraftData.value
                    ? getPhoneNumber(controller.draftDetail.value.nomorHp ?? '')
                    : getPhoneNumber(
                      controller.recruitmentDetail.value.phoneNumber ?? '',
                    ),
          ),
          _infoCandidateTextCard(
            title: 'Email',
            value:
                controller.isDraftData.value
                    ? controller.draftDetail.value.email ?? '-'
                    : controller.recruitmentDetail.value.email ?? '-',
          ),
          _formCard(
            context,
            title: 'Verifikasi Identitas',
            sectionIndex: 0,
            onTap: () {
              _navigateToFormWithApiData(page: 0);
            },
          ),
          _formCard(
            context,
            title: 'Data Diri Sesuai KTP',
            sectionIndex: 1,
            onTap: () {
              _navigateToFormWithApiData(page: 1);
            },
          ),
          _formCard(
            context,
            title: 'Kelengkapan Data Pribadi',
            sectionIndex: 2,
            onTap: () {
              _navigateToFormWithApiData(page: 2);
            },
          ),
          _formCard(
            context,
            title: 'Perjanjian Keagenan',
            sectionIndex: 3,
            onTap: () {
              _navigateToFormWithApiData(page: 3);
            },
          ),
          // Only show interview form for BM and BD candidates and not from recruitment_list
          if (_isBMOrBDCandidate() && !_isFromRecruitmentList())
            _formCard(
              context,
              title: 'Hasil Interview',
              sectionIndex: 4,
              onTap: () {
                Get.toNamed(
                  Routes.FORM_INTERVIEW,
                  parameters: {
                    'uuid': controller.recruitmentDetail.value.uuid ?? '',
                  },
                );
              },
            ),
        ],
      ),
    );
  }

  String getPhoneNumber(String text) {
    String result = text;

    if (text.startsWith('08')) {
      result = text;
    } else {
      if (text.startsWith('8')) {
        result = '0$text';
      } else {
        result = '08$text';
      }
    }

    return result;
  }

  // Check if candidate is BM or BD level
  bool _isBMOrBDCandidate() {
    String candidateLevel = '';

    if (controller.isDraftData.value) {
      candidateLevel = controller.draftDetail.value.candidateLevel ?? '';
    } else {
      candidateLevel = controller.recruitmentDetail.value.positionLevel ?? '';
    }

    return candidateLevel == 'BM' || candidateLevel == 'BD';
  }

  // Check if page is accessed from recruitment_list_page
  bool _isFromRecruitmentList() {
    return controller.source == 'recruitment_list';
  }

  Widget _formCard(
    BuildContext context, {
    required String title,
    required Function() onTap,
    required int sectionIndex,
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: Get.width,
          padding: EdgeInsets.symmetric(
            vertical: paddingSmall,
            horizontal: paddingMedium,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
            ),
            borderRadius: BorderRadius.circular(radiusSmall),
          ),
          child: Row(
            children: [
              Obx(() {
                bool isCompleted;
                switch (sectionIndex) {
                  case 0:
                    isCompleted =
                        controller.isVerificationSectionCompleted.value;
                    break;
                  case 1:
                    isCompleted =
                        controller.isIdentificationSectionCompleted.value;
                    break;
                  case 2:
                    isCompleted =
                        controller.isSelfIdentificationSectionCompleted.value;
                    break;
                  case 3:
                    isCompleted = controller.isTermsSectionCompleted.value;
                    break;
                  case 4:
                    isCompleted = controller.isInterviewSectionCompleted.value;
                    break;
                  default:
                    isCompleted = false;
                }
                return Icon(
                  isCompleted
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  color: isCompleted ? kColorGlobalGreen : Colors.grey,
                );
              }),
              SizedBox(width: paddingSmall),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                ),
              ),
              SizedBox(width: paddingSmall),
              Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoCandidateTextCard({
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: paddingSmall),
      child: Row(
        children: [
          SizedBox(width: 130, child: Text(title)),
          Padding(
            padding: EdgeInsetsGeometry.symmetric(horizontal: paddingSmall),
            child: Text(':'),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  AspectRatio _passPhotoSection() {
    return AspectRatio(
      aspectRatio: 1,
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: kColorPaninBlue,
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
        clipBehavior: Clip.hardEdge,
        child: Obx(
          () =>
              controller.state.value == ControllerState.loading
                  ? Center(child: CircularProgressIndicator())
                  : controller.isDraftData.value
                  ? _buildDraftPassPhoto()
                  : Utils.cachedImageWrapper(
                    controller.recruitmentDetail.value.passPhoto,
                    isFullUrl: true,
                    fit: BoxFit.cover,
                  ),
        ),
      ),
    );
  }

  Widget _buildDraftPassPhoto() {
    final draftDetail = controller.draftDetail.value;

    // If pasFotoImageUrl is not empty, use it as network image
    if (draftDetail.pasFotoImageUrl != null &&
        draftDetail.pasFotoImageUrl!.isNotEmpty) {
      return Utils.cachedImageWrapper(
        draftDetail.pasFotoImageUrl,
        isFullUrl: true,
        fit: BoxFit.cover,
      );
    }

    // If pasFotoImagePath exists, use it as local file
    if (draftDetail.pasFotoImagePath != null &&
        draftDetail.pasFotoImagePath!.isNotEmpty) {
      try {
        return Image.file(
          File(draftDetail.pasFotoImagePath!),
          fit: BoxFit.cover,
        );
      } catch (e) {
        // If file doesn't exist or can't be loaded, show placeholder
        return Container(
          color: Colors.grey[300],
          child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
        );
      }
    }

    // If no image available, show placeholder
    return Container(
      color: Colors.grey[300],
      child: Icon(Icons.image_not_supported, color: Colors.grey[600]),
    );
  }

  Widget _recruiterSection(BuildContext context) {
    return Obx(
      () => SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TitleSecondary(title: 'Informasi Perekrut'),
            SizedBox(height: paddingSmall),
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.grey,
                  radius: 20,
                  child: Text(
                    Utils.getInitials(
                      controller.isDraftData.value
                          ? controller.draftDetail.value.recruiterName ?? '-'
                          : controller
                                  .recruitmentDetail
                                  .value
                                  .recruiter
                                  ?.name ??
                              '-',
                    ),
                  ),
                ),
                SizedBox(width: paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color:
                                  Get.isDarkMode
                                      ? kColorGlobalBgDarkBlue
                                      : kColorBorderLight,
                              borderRadius: BorderRadius.circular(
                                paddingMedium,
                              ),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: paddingSmall,
                              vertical: paddingExtraSmall,
                            ),
                            child: Text(
                              controller.isDraftData.value
                                  ? controller
                                          .draftDetail
                                          .value
                                          .recruiterLevel ??
                                      '-'
                                  : controller
                                          .recruitmentDetail
                                          .value
                                          .recruiter
                                          ?.agentLevel ??
                                      '-',
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                color: kColorGlobalBlue,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          SizedBox(width: paddingSmall),
                          Text(
                            controller.isDraftData.value
                                ? controller.draftDetail.value.recruiterName ??
                                    '-'
                                : controller
                                        .recruitmentDetail
                                        .value
                                        .recruiter
                                        ?.name ??
                                    '-',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w700),
                          ),
                        ],
                      ),
                      SizedBox(height: paddingSmall),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  controller.isDraftData.value
                                      ? controller
                                          .draftDetail
                                          .value
                                          .recruiterCode
                                      : controller
                                          .recruitmentDetail
                                          .value
                                          .recruiter
                                          ?.agentCode,
                            ),
                            if (controller.isDraftData.value)
                              if (controller
                                      .draftDetail
                                      .value
                                      .recruiterBranch !=
                                  '-')
                                TextSpan(text: ' - '),
                            if (controller.isDraftData.value)
                              if (controller
                                      .draftDetail
                                      .value
                                      .recruiterBranch !=
                                  '-')
                                TextSpan(
                                  text:
                                      controller
                                          .draftDetail
                                          .value
                                          .recruiterBranch,
                                ),
                            if (controller.isDraftData.isFalse)
                              if (controller
                                      .recruitmentDetail
                                      .value
                                      .recruiter
                                      ?.branches
                                      ?.first
                                      .branchName !=
                                  '-')
                                TextSpan(text: ' - '),
                            if (controller.isDraftData.isFalse)
                              if (controller
                                      .recruitmentDetail
                                      .value
                                      .recruiter
                                      ?.branches
                                      ?.first
                                      .branchName !=
                                  '-')
                                TextSpan(
                                  text:
                                      controller
                                          .recruitmentDetail
                                          .value
                                          .recruiter
                                          ?.branches
                                          ?.first
                                          .branchName,
                                ),
                          ],
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingMedium),
            Divider(),
            SizedBox(height: paddingMedium),
          ],
        ),
      ),
    );
  }

  Row _headerSection(BuildContext context) {
    return Row(
      children: [
        Expanded(child: TitleWidget(title: 'Formulir Rekruitment')),
        Obx(
          () => Utils.getApprovalStatus(
            context,
            controller.isDraftData.value
                ? 'DRAFT' // Draft data always has DRAFT status
                : controller
                        .recruitmentDetail
                        .value
                        .approvalHeader
                        ?.approvalStatus ??
                    '',
          ),
        ),
      ],
    );
  }

  // Navigate to recruitment form with data for prefill
  void _navigateToFormWithApiData({required int page}) async {
    if (controller.isDraftData.value) {
      // For draft data, navigate with form ID to continue editing
      await Get.toNamed(
        Routes.KEAGENAN_FORM,
        parameters: {'formId': controller.id.value},
        arguments: {'page': page},
      );
    } else {
      // For API data, navigate with API model for viewing
      await Get.toNamed(
        Routes.KEAGENAN_FORM,
        arguments: {
          'apiData': controller.recruitmentDetail.value,
          'page': page,
        },
      );
    }

    // Update completion status when returning from form
    controller.onReturnFromForm();
  }
}
