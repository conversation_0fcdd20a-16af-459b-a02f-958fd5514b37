import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/recruitment_card.dart';
import 'package:pdl_superapp/components/recruitment_api_card.dart';
import 'package:pdl_superapp/components/share_content.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_list_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentListPage extends StatefulWidget {
  const RecruitmentListPage({super.key});

  @override
  State<RecruitmentListPage> createState() => _RecruitmentListPageState();
}

class _RecruitmentListPageState extends State<RecruitmentListPage>
    with WidgetsBindingObserver {
  final RecruitmentListController controller = Get.put(
    RecruitmentListController(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // super.didChangeAppLifecycleState(state);
    // if (state == AppLifecycleState.resumed) {
    //   // Refresh data when app comes back to foreground
    //   controller.refreshData();
    // }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Rekrut',
      controller: controller,
      scrollController: controller.scrollController,
      onRefresh: () => controller.refreshData(),
      bottomAction:
          () => PdlBottomSheet(
            content: _methodBottomSheet(context),
            title: 'Pilih Metode',
          ),
      bottomText: 'Mulai Rekrut',
      child: Padding(
        padding: const EdgeInsets.all(paddingMedium),
        child: Column(
          children: [
            TitleWidget(title: 'Daftar Kandidat'),
            SizedBox(height: paddingMedium),
            SizedBox(
              width: Get.width,
              child: PdlTextField(
                hint: 'Cari nama kandidat',
                onChanged: (value) {
                  controller.updateSearchQuery(value);
                },
                prefixIcon: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Utils.cachedSvgWrapper(
                    'icon/ic-linear-search -2.svg',
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
                ),
              ),
            ),
            SizedBox(height: paddingMedium),

            // Tampilkan status loading
            Obx(() {
              final isLoading =
                  controller.isLoadingForms.value ||
                  controller.isLoadingApiData.value;

              // Ambil data yang sudah difilter
              final draftForms = controller.filteredForms;
              final apiRecruitments = controller.filteredApiRecruitment;

              if (isLoading && draftForms.isEmpty && apiRecruitments.isEmpty) {
                return Center(child: CircularProgressIndicator());
              }

              // Jika tidak ada data sama sekali
              if (draftForms.isEmpty && apiRecruitments.isEmpty) {
                return EmptyStateView(
                  msg: 'Anda belum merekrut siapapun tahun ini',
                );
              }

              // Tampilkan semua data
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tampilkan form draft dari firebase jika ada
                  if (draftForms.isNotEmpty) ...[
                    ...draftForms.map((form) {
                      return RecruitmentCard(
                        form: form,
                        // onTap: () => controller.continueForm(form.id!),
                        onTap: () {
                          Get.toNamed(
                            Routes.APPROVAL,
                            arguments: {
                              'id': form.id,
                              'source': 'recruitment_list',
                            },
                          );
                        },
                      );
                    }),
                  ],

                  // Tampilkan data API recruitment jika ada
                  if (apiRecruitments.isNotEmpty) ...[
                    ...apiRecruitments.map((recruitment) {
                      return RecruitmentApiCard(
                        recruitment: recruitment,
                        onTap: () {
                          Get.toNamed(
                            Routes.APPROVAL,
                            arguments: {
                              'uuid': recruitment.uuid,
                              'source': 'recruitment_list',
                            },
                          );
                        },
                      );
                    }),
                  ],
                  if (isLoading)
                    SizedBox(
                      width: Get.width,
                      child: Center(child: CircularProgressIndicator()),
                    ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  SizedBox _methodBottomSheet(context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          SizedBox(height: paddingMedium),
          _methodBottomSheetCard(
            context,
            iconUrl: 'icon/ic-dual-note-dualtone.svg',
            title: 'Isi Form',
            description:
                'Bantu kandidat melengkapi formulir melalui device Anda',
            ontap: () {
              Get.back();
              controller.createNewForm();
            },
          ),
          SizedBox(height: paddingMedium),
          _methodBottomSheetCard(
            context,
            iconUrl: 'icon/ic-dual-qr-code.svg',
            title: 'Kirim QR Code',
            description:
                'Kandidat dapat melengkapi formulir secara mandiri melalui tautan yang Anda kirimkan',
            ontap: () {
              Get.back();
              PdlBottomSheet(
                content: _levelBottomSheet(context),
                title: 'Level Keagenan Kandidat',
              );
            },
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  Widget _methodBottomSheetCard(
    context, {
    required String title,
    required String description,
    required String iconUrl,
    required Function() ontap,
  }) {
    return GestureDetector(
      onTap: ontap,
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                color: kColorGlobalBgBlue,
                shape: BoxShape.circle,
              ),
              width: 50,
              height: 50,
              padding: EdgeInsets.all(12),
              child: Utils.cachedSvgWrapper(iconUrl, color: kColorGlobalBlue),
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  SizedBox(height: paddingSmall),
                  Text(
                    description,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  SizedBox _levelBottomSheet(context) {
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
    );
    return SizedBox(
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: paddingMedium),
          Text('Level', style: Theme.of(context).textTheme.bodyMedium),
          SizedBox(height: paddingSmall),
          Theme(
            data: dropdownTheme,
            child: CustomDropdown(
              items: controller.roleCandidate,
              initialItem:
                  controller.selectedLevel.value != ''
                      ? controller.selectedLevel.value
                      : null,
              onChanged: (val) {
                controller.selectedLevel.value = val!;
              },
              closedHeaderPadding: EdgeInsets.all(12),
              decoration: dropdownDecoration,
            ),
          ),

          SizedBox(height: paddingMedium),
          Obx(
            () => PdlTextField(
              textController: controller.candidateBranchController,
              hint: 'Cari Kantor Cabang',
              label: 'Kantor Cabang',
              onChanged: (val) async {
                Future.delayed(Duration(microseconds: 300)).then((x) {
                  controller.onBranchTextChanged(val);
                });
              },
              prefixIcon: Icon(Icons.search),
              hasError: controller.candidateBranchError.value.isNotEmpty,
              errorText:
                  controller.candidateBranchError.value.isEmpty
                      ? null
                      : controller.candidateBranchError.value,
              items: [
                // Only show branch list if user has typed something
                if (controller.candidateBranchText.value.isNotEmpty)
                  for (int i = 0; i < controller.branchList.length; i++)
                    GestureDetector(
                      onTap: () {
                        final selectedBranchName =
                            controller.branchList[i].branchName ?? '-';
                        controller.candidateBranchController.text =
                            selectedBranchName;
                        controller.candidateBranchCode.value =
                            controller.branchList[i].id ?? 0;
                        controller.branchList.clear();
                        // Update reactive variable to trigger UI update
                        controller.candidateBranchText.value =
                            selectedBranchName;
                      },
                      child: Container(
                        width: Get.width,
                        color: Colors.transparent,
                        padding: EdgeInsets.only(top: paddingSmall),
                        child: Text(controller.branchList[i].branchName ?? '-'),
                      ),
                    ),
              ],
            ),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: Obx(
              () => PdlButton(
                title: 'Lihat QR',
                onPressed:
                    controller.selectedLevel.value == '' ||
                            controller.candidateBranchCode.value == 0
                        ? null
                        : () {
                          Get.back();
                          _showQRCodeDialog(
                            context,
                            candidateLevel: controller.selectedLevel.value,
                            candidateBranch:
                                controller.candidateBranchController.text,
                            candidateBranchCode:
                                controller.candidateBranchCode.value.toString(),
                          );
                          controller.selectedLevel.value = '';
                          controller.candidateBranchController.clear();
                          controller.candidateBranchCode.value = 0;
                        },
              ),
            ),
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  void _showQRCodeDialog(
    BuildContext context, {
    required String candidateLevel,
    required String candidateBranch,
    required String candidateBranchCode,
  }) {
    // Get recruiter information from SharedPreferences
    final prefs = controller.prefs;
    final recruiterName = prefs.getString(kStorageAgentName) ?? '';
    final recruiterId = prefs.getString(kStorageUserId) ?? '';
    final recruiterBranch = prefs.getString(kStorageAgentBranch) ?? '';
    final recruiterCode = prefs.getString(kStorageAgentCode) ?? '';
    final recruiterLevel = prefs.getString(kStorageUserLevel) ?? '';
    final recruiterPhoto = ''; // Agent photo is not stored in SharedPreferences

    // Get candidate information from controller

    // Build QR URL
    final qrUrl =
        'http://sandbox-quantumx.panindai-ichilife.co.id/public/form-keagenan?'
        'recruiterName=${Uri.encodeComponent(recruiterName)}&'
        'recruiterId=${Uri.encodeComponent(recruiterId)}&'
        'recruiterBranch=${Uri.encodeComponent(recruiterBranch)}&'
        'recruiterCode=${Uri.encodeComponent(recruiterCode)}&'
        'recruiterLevel=${Uri.encodeComponent(recruiterLevel)}&'
        'recruiterPhoto=${Uri.encodeComponent(recruiterPhoto)}&'
        'candidateLevelController=${Uri.encodeComponent(candidateLevel)}&'
        'candidateBranchController=${Uri.encodeComponent(candidateBranch)}&'
        'candidateBranchCode=${Uri.encodeComponent(candidateBranchCode)}';

    PdlBaseDialog(
      context: context,
      title: 'qr_code_str'.tr,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(paddingSmall),
            margin: EdgeInsets.symmetric(horizontal: paddingMedium),
            decoration: BoxDecoration(
              color: kLine,
              borderRadius: BorderRadius.circular(radiusMedium),
            ),
            child: AspectRatio(
              aspectRatio: 1,
              child: Container(
                width: Get.width,
                padding: EdgeInsets.all(paddingSmall),
                decoration: BoxDecoration(
                  color: kLine,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: QrImageView(
                  data: qrUrl,
                  version: QrVersions.auto,
                  size: 200.0,
                  backgroundColor: kLine,
                  dataModuleStyle: QrDataModuleStyle(
                    dataModuleShape: QrDataModuleShape.square,
                    color: Colors.black,
                  ),
                  eyeStyle: QrEyeStyle(
                    eyeShape: QrEyeShape.square,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: paddingMedium),
          Text(
            controller.agentName.value,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
          ),
          SizedBox(height: paddingMedium),
          Text('qr_scan_form_desc_str'.tr, textAlign: TextAlign.center),
          Container(
            padding: EdgeInsets.only(top: paddingMedium),
            child: Text(
              'qr_scan_form_sub_desc_str'.tr,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: Get.width,
            child: PdlButton(
              title: 'share_str'.tr,
              suffixIcon: Icon(Icons.share_outlined),
              onPressed: () {
                Get.back();
                PdlBottomSheet(
                  title: 'share_str'.tr,
                  radius: radiusSmall,
                  titleStyle: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                  content: ShareContent(url: qrUrl, onSuccess: () {}),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
