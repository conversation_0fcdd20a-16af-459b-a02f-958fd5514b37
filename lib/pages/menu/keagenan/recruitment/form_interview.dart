import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pd_radio_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/approval_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_interview_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class FormInterviewPage extends StatelessWidget {
  FormInterviewPage({super.key});

  final FormInterviewController controller = Get.put(FormInterviewController());

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        // Call onReturnFromForm when user navigates back
        if (Get.isRegistered<ApprovalController>()) {
          Get.find<ApprovalController>().onReturnFromForm();
        }
      },
      child: BaseDetailPage(
        title: 'Hasil Interview',
        controller: controller,
        onRefresh: () {},
        bottomText: 'button_save'.tr,
        isBottomActionEnabled: controller.isFormDisabled,
        bottomAction: () => controller.performSubmitInterview(),
        child: Padding(
          padding: const EdgeInsets.all(paddingMedium),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TitleWidget(title: 'Hasil Interview'),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: paddingSmall),
                child: Text(
                  'Mohon mengisi data dengan benar, sesuai hasil interview dengan kandidat.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color:
                        Get.isDarkMode
                            ? kColorTextTersier
                            : kColorTextTersierLight,
                  ),
                ),
              ),
              SizedBox(height: paddingSmall),
              questionWrapper(
                context,
                question: 'Apakah kandidat dari asuransi jiwa?',
                selectedValue: controller.selectedIsAsuransiJiwa,
                onTapYes: () {
                  controller.selectedIsAsuransiJiwa.value = 0;
                },
                onTapNo: () {
                  controller.selectedIsAsuransiJiwa.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki riwayat kerja 5 tahun?',
                selectedValue: controller.selectedHasWorkHistory5Years,
                onTapYes: () {
                  controller.selectedHasWorkHistory5Years.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasWorkHistory5Years.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki produksi 2 tahun?',
                selectedValue: controller.selectedHasProduction2Years,
                onTapYes: () {
                  controller.selectedHasProduction2Years.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasProduction2Years.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memenuhi persyaratan tenaga kerja?',
                selectedValue: controller.selectedMeetsManpowerRequirement,
                onTapYes: () {
                  controller.selectedMeetsManpowerRequirement.value = 0;
                },
                onTapNo: () {
                  controller.selectedMeetsManpowerRequirement.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki penghargaan perusahaan?',
                selectedValue: controller.selectedHasCompanyAwards,
                onTapYes: () {
                  controller.selectedHasCompanyAwards.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasCompanyAwards.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki surat terminasi?',
                selectedValue: controller.selectedHasTerminationLetter,
                onTapYes: () {
                  controller.selectedHasTerminationLetter.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasTerminationLetter.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki surat komitmen?',
                selectedValue: controller.selectedHasCommitmentLetter,
                onTapYes: () {
                  controller.selectedHasCommitmentLetter.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasCommitmentLetter.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki surat komitmen referral?',
                selectedValue: controller.selectedHasReferralCommitmentLetter,
                onTapYes: () {
                  controller.selectedHasReferralCommitmentLetter.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasReferralCommitmentLetter.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki sertifikasi AAJI?',
                selectedValue: controller.selectedHasAAJICertification,
                onTapYes: () {
                  controller.selectedHasAAJICertification.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasAAJICertification.value = 1;
                },
              ),
              questionWrapper(
                context,
                question: 'Apakah kandidat memiliki surat pengecualian?',
                selectedValue: controller.selectedHasExemptionLetter,
                onTapYes: () {
                  controller.selectedHasExemptionLetter.value = 0;
                },
                onTapNo: () {
                  controller.selectedHasExemptionLetter.value = 1;
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget questionWrapper(
    context, {
    required String question,
    required RxInt selectedValue,
    required Function() onTapYes,
    required Function() onTapNo,
  }) {
    return Padding(
      padding: EdgeInsetsGeometry.only(bottom: paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: paddingMedium),
          Obx(
            () => Row(
              children: [
                PdlRadioButton(
                  index: 0,
                  selectedIndex: selectedValue.value,
                  onTap: () => onTapYes(),
                  widget: SizedBox(width: 80, child: Text('Ya')),
                ),
                PdlRadioButton(
                  index: 1,
                  selectedIndex: selectedValue.value,
                  title: 'Tidak',
                  onTap: () => onTapNo(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
