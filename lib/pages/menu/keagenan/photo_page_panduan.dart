import 'dart:io' show File;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/photo_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PhotoPagePanduan extends StatelessWidget {
  PhotoPagePanduan({super.key});

  final PdlPHotoController controller = Get.put(PdlPHotoController());

  @override
  Widget build(BuildContext context) {
    String title = Get.parameters['title'] ?? '';
    String type = Get.parameters['type'] ?? '';
    bool galeryDisabled = Get.parameters['galeryDisabled'] == 'true';
    controller.type = type;

    // Reset camera state when entering this page
    // This ensures permission will be requested again if previously denied
    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.resetCameraState();
    });
    return BaseDetailPage(
      title: 'Panduan Foto $title',
      controller: controller,
      onRefresh: () {},
      backEnabled: true,
      bottomWidget: Row(
        children: [
          Expanded(
            child: PdlButton(
              title: 'Unggah Foto',
              onPressed:
                  galeryDisabled == true
                      ? null
                      : () {
                        // Reset camera state when using gallery
                        controller.resetCameraState();
                        Utils.imagePicker(
                          context,
                          isCamera: false,
                          onSuccess: (XFile image) async {
                            // Handle the selected image - web compatible
                            dynamic imageFile;
                            if (kIsWeb) {
                              // On web, always use XFile
                              imageFile = image;
                            } else {
                              // On mobile, convert to File
                              imageFile = File(image.path);
                            }

                            // Crop the image before returning
                            final croppedImage = await controller.cropImage(
                              imageFile,
                            );
                            if (croppedImage != null) {
                              Get.back(result: croppedImage);
                            }
                          },
                        );
                      },
              backgroundColor: Colors.transparent,
              foregorundColor: kColorPaninBlue,
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            child: PdlButton(
              title: 'Ambil Foto',
              onPressed: () async {
                // Initialize camera and check if permission is granted
                final cameraInitialized = await controller.initializeCamera();

                if (!cameraInitialized) {
                  // If camera initialization failed (likely due to permission denial)
                  if (controller.cameraPermissionDenied.value) {
                    // Go back to previous page when permission is denied
                    Get.back();
                    return;
                  }
                  // For other errors, just return without navigating
                  return;
                }

                final result = await Get.toNamed(
                  Routes.PHOTO_PAGE,
                  arguments: {'title': title, 'type': type},
                );

                // Always dispose camera when returning from photo page
                controller.resetCameraState();

                if (result != null) {
                  // Pass the captured image back to the form page - web compatible
                  Get.back(result: result);
                }
              },
            ),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TitleWidget(title: 'Panduan Foto $title'),
            SizedBox(height: paddingSmall),
            Text(
              'Pastikan foto yang kamu ambil atau unggah sesuai dengan panduan dibawah ini.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: paddingMedium),
            Row(
              children: [
                _infoCard(context, isTrue: true),
                SizedBox(width: paddingMedium),
                _infoCard(context, isTrue: false),
              ],
            ),
            SizedBox(height: paddingMedium),
            if (type == kPhotoTypeKtp)
              _instructionBox(
                context,
                content: [
                  'Siapkan KTP asli, bukan fotokopian',
                  'Ikuti bingkai saat mengambil foto KTP',
                  'Pastikan foto KTP jelas. Tidak blur, berbayang atau memantulkan cahaya',
                ],
              ),
            if (type == kPhotoTypeSelfieKtp)
              _instructionBox(
                context,
                content: [
                  'Posisikan HP secara tegak saat mengambil foto',
                  'KTP harus terlihat jelas',
                  'Lepas aksesoris yang dapat menutupi wajah seperti topi atau masker',
                ],
              ),
            if (type == kPhotoTypePasFoto)
              _instructionBox(
                context,
                content: [
                  'Pastikan Anda berada di tempat terang',
                  'Posisikan wajah ke arah kamera dengan posisi badan tegak',
                  'Lepas aksesoris yang dapat menutupi wajah seperti topi atau masker',
                  'Gunakan pakaian rapih atau formal',
                ],
              ),
          ],
        ),
      ),
    );
  }

  Container _instructionBox(
    BuildContext context, {
    required List<String> content,
  }) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        borderRadius: BorderRadius.circular(radiusSmall),
      ),
      child: Column(
        children: [
          for (int i = 0; i < content.length; i++)
            _ktpInstruction(context, content: content[i]),
        ],
      ),
    );
  }

  Row _ktpInstruction(BuildContext context, {required String content}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          alignment: Alignment.topCenter,
          width: 8,
          height: 8,
          margin: EdgeInsets.only(top: 5),
          decoration: BoxDecoration(
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: paddingSmall),
        Expanded(
          child: Text(content, style: Theme.of(context).textTheme.bodyMedium),
        ),
      ],
    );
  }

  Expanded _infoCard(BuildContext context, {required bool isTrue}) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(paddingMedium),
        decoration: BoxDecoration(
          color: isTrue ? kColorGlobalBgGreen : kColorGlobalBgRed,
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  color: isTrue ? kColorGlobalGreen : kColorGlobalRed,
                ),
                SizedBox(width: paddingSmall),
                Text(
                  isTrue ? 'Benar' : 'Salah',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isTrue ? kColorGlobalGreen : kColorGlobalRed,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
            SizedBox(height: paddingSmall),
            Container(
              width: Get.width,
              padding: EdgeInsets.zero,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                color: kColorBorderLight,
                borderRadius: BorderRadius.circular(radiusMedium),
              ),
              child: AspectRatio(
                aspectRatio: 1,
                child: imagePanduan(controller.type, isTrue),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget imagePanduan(String type, bool isTrue) {
    if (type == kPhotoTypeKtp) {
      return Image.asset(
        isTrue
            ? 'assets/images/panduan-ktp-benar.png'
            : 'assets/images/panduan-ktp-salah.png',
        fit: BoxFit.cover,
      );
    } else if (type == kPhotoTypeSelfieKtp) {
      return Image.asset(
        isTrue
            ? 'assets/images/panduan-selfie-ktp.png'
            : 'assets/images/panduan-selfie-ktp-salah.png',
        fit: BoxFit.cover,
      );
    } else if (type == kPhotoTypePasFoto) {
      return Image.asset(
        isTrue
            ? 'assets/images/panduan-pas-foto.png'
            : 'assets/images/panduan-pas-foto-salah.png',
        fit: BoxFit.cover,
      );
    }
    return Container();
  }
}
