import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_success_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PublicSuccessPage extends StatelessWidget {
  PublicSuccessPage({super.key});

  final PublicSuccessController controller = Get.put(PublicSuccessController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Berhasil',
      controller: controller,
      onRefresh: () {},
      backEnabled: false, // Disable back button to prevent going back
      bottomWidget: _buildBottomActions(context),
      child: _buildSuccessContent(context),
    );
  }

  Widget _buildSuccessContent(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: paddingExtraLarge),

          // Success illustration
          SizedBox(
            width: Get.width / 1.5,
            height: Get.width / 2,
            child: Utils.cachedSvgWrapper(
              'icon/illustration-empty-check.svg',
              fit: BoxFit.contain,
            ),
          ),

          SizedBox(height: paddingLarge),

          // Success title
          Text(
            'Formulir Berhasil Dikirim!',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: 24,
              color: kColorGlobalGreen,
            ),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: paddingMedium),

          // Success message
          Text(
            'Terima kasih telah mengisi formulir rekrutmen keagenan. '
            'Data Anda telah berhasil dikirim dan akan diproses oleh tim kami.',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontSize: 16, height: 1.5),
            textAlign: TextAlign.center,
          ),

          SizedBox(height: paddingLarge),

          // Additional info
          Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              color: kColorGlobalGreen.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: kColorGlobalGreen.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Icon(Icons.info_outline, color: kColorGlobalGreen, size: 24),
                SizedBox(height: paddingSmall),
                Text(
                  'Informasi Selanjutnya',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: kColorGlobalGreen,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: paddingSmall),
                Text(
                  'Tim kami akan menghubungi Anda melalui email atau telepon '
                  'dalam 1-3 hari kerja untuk proses selanjutnya.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: kColorGlobalGreen.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Obx(
      () => Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary action - Go to home
            SizedBox(
              width: Get.width,
              child: PdlButton(
                controller: controller,
                title: 'Selesai',
                onPressed:
                    controller.isNavigating.value
                        ? null
                        : controller.navigateToHome,
                backgroundColor: kColorGlobalGreen,
              ),
            ),

            SizedBox(height: paddingSmall),

            // Secondary action - Share (optional)
            SizedBox(
              width: Get.width,
              child: PdlButton(
                controller: controller,
                title: 'Bagikan',
                onPressed: controller.shareSuccess,
                backgroundColor: Colors.transparent,
                foregorundColor: kColorGlobalGreen,
                borderColor: kColorGlobalGreen,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
