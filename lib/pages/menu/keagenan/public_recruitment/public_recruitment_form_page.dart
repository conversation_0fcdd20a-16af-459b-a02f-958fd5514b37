import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_recruitment_form_controller.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_form_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_form_self_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_form_terms_signature.dart';
import 'package:pdl_superapp/pages/menu/keagenan/public_recruitment/public_form_verification.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PublicRecruitmentFormPage extends StatelessWidget {
  PublicRecruitmentFormPage({super.key});

  final PublicRecruitmentFormController controller = Get.put(
    PublicRecruitmentFormController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Stack(
        children: [
          _formPages(context),
          if (controller.isReadyToSubmit.isTrue) readyToSubmitPage(context),
          if (controller.isVerificationEmailSent.isTrue)
            verificationPages(context),
        ],
      ),
    );
  }

  BaseDetailPage readyToSubmitPage(BuildContext context) {
    return BaseDetailPage(
      title: 'remarks_str'.tr,
      controller: controller,
      backEnabled: true,
      onRefresh: () {},
      onBack: () {
        controller.isReadyToSubmit.value = false;
      },
      bottomText: 'button_submit_str'.tr,
      bottomAction: () {
        controller.submitDraft();
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: paddingMedium),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(text: 'remarks_str'.tr),
                TextSpan(
                  text: ' (',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: 'optional_str'.tr,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
                TextSpan(
                  text: ')',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
          ),
          PdlTextField(
            hint: 'remarks_helper_str'.tr,
            isTextArea: true,
            textController: controller.notesTextController,
            height: 100,
          ),
        ],
      ),
    );
  }

  BaseDetailPage verificationPages(BuildContext context) {
    return BaseDetailPage(
      title: 'Verifikasi Email Kandidat',
      controller: controller,
      onRefresh: () {},
      onBack: () {
        controller.isVerificationEmailSent.value = false;
      },
      child:
          controller.selfIdentificationController.isEmailVerified.value
              ? Container(
                width: Get.width,
                padding: EdgeInsets.symmetric(horizontal: paddingMedium),
                child: Column(
                  children: [
                    SizedBox(height: paddingExtraLarge),
                    Utils.cachedSvgWrapper('icon/ic-dialog-check.svg'),
                    SizedBox(height: paddingLarge),
                    Text(
                      'Terima kasih telah mendaftar sebagai agen Panin Dai-ichi Life',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: paddingMedium),
                    Text(
                      'Formulir pendaftaran Anda berhasil dikirim dan sedang diproses oleh perusahaan. Harap menunggu informasi lebih lanjut dari rekruter Anda.',
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: paddingMedium),
                  ],
                ),
              )
              : _verifSentPage(context),
    );
  }

  Container _verifSentPage(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        children: [
          SizedBox(height: paddingExtraLarge),
          Utils.cachedSvgWrapper(
            'icon/illustration-empty-inbox.svg',
            width: 200,
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Segera Verifikasi Email Kandidat',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: paddingMedium),
          Text('Tautan verifikasi telah dikirimkan ke'),
          Obx(
            () => Text(
              controller.selfIdentificationController.emailVerif.value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Belum mendapatkan tautan verifikasi?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
            ),
          ),
          SizedBox(height: paddingSmall),
          // Countdown here
          Obx(() {
            if (controller
                .selfIdentificationController
                .isCountdownActive
                .value) {
              return Column(
                children: [
                  Text(
                    'Kirim ulang dalam ${controller.selfIdentificationController.countdownDisplay.value}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color:
                          Get.isDarkMode
                              ? kColorTextTersierLight
                              : kColorTextTersier,
                    ),
                  ),
                  SizedBox(height: paddingSmall),
                ],
              );
            }
            // Show resend button when countdown is finished
            else {
              return GestureDetector(
                onTap: () {
                  // Restart countdown and resend verification email
                  controller.selfIdentificationController.restartCountdown();
                  // You can add resend email logic here if needed
                  controller.selfIdentificationController.sendVerification(
                    controller.selfIdentificationController.formUuId.value,
                  );
                },
                child: Text(
                  'Kirim Ulang',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: kColorGlobalBlue),
                ),
              );
            }
          }),
          // end countdown
        ],
      ),
    );
  }

  BaseDetailPage _formPages(BuildContext context) {
    return BaseDetailPage(
      title: 'Formulir Rekrutmen',
      controller: controller,
      onRefresh: () {},
      bottomWidget: Row(
        children: [
          Expanded(
            child: Obx(
              () => PdlButton(
                controller: controller,
                title: 'button_next_str'.tr,
                onPressed:
                    controller.activePage.value < 3
                        ? () async {
                          // Validate current page before proceeding
                          controller.checkWebInternetConnection();
                        }
                        : (controller
                                .termsController
                                .isAgreementChecked
                                .isFalse ||
                            !controller.termsController.canEnableCheckbox())
                        ? null
                        : () async {
                          // Validate final page before submission
                          await controller.validateCurrentPage();
                        },
              ),
            ),
          ),
        ],
      ),

      child: Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _pageCounter(context),
            SizedBox(height: paddingMedium),
            Obx(
              () =>
                  controller.isReady.isFalse
                      ? Center(child: CircularProgressIndicator())
                      : ExpandablePageView(
                        controller: controller.pageController,
                        physics: NeverScrollableScrollPhysics(),
                        children: [
                          PublicFormVerification(
                            controller: controller.verificationController,
                          ),
                          PublicFormIdentification(
                            controller: controller.identificationController,
                          ),
                          PublicFormSelfIdentification(
                            controller: controller.selfIdentificationController,
                          ),
                          PublicFormTermsSignature(controller: controller),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }

  SizedBox _pageCounter(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Obx(
        () => Row(
          children: [
            for (int i = 0; i < 4; i++)
              controller.activePage.value == i
                  ? Expanded(child: _pageCounterItem(context, i))
                  : _pageCounterItem(context, i),
          ],
        ),
      ),
    );
  }

  Widget _pageCounterItem(BuildContext context, int index) {
    return Obx(() {
      String title = 'indentity_verification_str'.tr; //Verifikasi Identitas
      switch (index) {
        case 1:
          title = 'personal_information_match_str'.tr; //Data Diri Sesuai KTP
          break;
        case 2:
          title = 'data_complete_str'.tr; //Kelengkapan data
          break;
        case 3:
          title = 'agent_contract_str'.tr; //Perjanjian Keagenan
          break;
        default:
      }
      return Row(
        children: [
          if (index < controller.activePage.value)
            GestureDetector(
              onTap: () {
                controller.activePage.value = index;
                controller.pageController.animateToPage(
                  index,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Icon(Icons.check_circle, color: kColorGlobalGreen),
            ),
          if (index >= controller.activePage.value)
            Container(
              padding: EdgeInsets.all(paddingSmall),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                shape: BoxShape.circle,
              ),
              child: Text(
                '${index + 1}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
              ),
            ),
          SizedBox(width: paddingSmall),
          if (controller.activePage.value == index)
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
          if (controller.activePage.value == index) Expanded(child: Divider()),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
        ],
      );
    });
  }
}
