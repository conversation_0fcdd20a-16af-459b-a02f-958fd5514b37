import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/components/webview.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_form_terms_only_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:signature/signature.dart';
import 'dart:convert';
import 'dart:typed_data';

class PublicFormTermsSignatureOnly extends StatelessWidget {
  PublicFormTermsSignatureOnly({super.key});

  final PublicFormTermsOnlyController controller = Get.put(
    PublicFormTermsOnlyController(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: '<PERSON><PERSON>',
      controller: controller,
      onRefresh: () {},
      bottomText: 'Simpan',
      bottomAction: () => controller.safeForm(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: paddingMedium),
          TitleWidget(title: 'Tanda Tangan Perjanjian Keagenan'),

          // PKAJ Document
          Obx(
            () => _documentCard(
              context,
              title: 'Perjanjian Keagenan Asuransi Jiwa (PKAJ)',
              onTap: () {
                _navigateToDocumentDetail(
                  context,
                  url: controller.pkajUrl,
                  isViewed: controller.isPkajDocumentViewed.value,
                  title: 'Perjanjian Keagenan Asuransi Jiwa (PKAJ)',
                  onUnderstood: () {
                    controller.markPkajDocumentViewed();
                    Get.back();
                  },
                );
              },
              isViewed: controller.isPkajDocumentViewed,
              canOpen: controller.canOpenDocument('PKAJ').value,
            ),
          ),

          // PMKAJ Document
          Obx(
            () => _documentCard(
              context,
              title: 'Perjanjian Manajemen Keagenan Asuransi Jiwa (PMKAJ)',
              onTap: () {
                _navigateToDocumentDetail(
                  context,
                  url: controller.pmkajUrl,
                  isViewed: controller.isPmkajDocumentViewed.value,
                  title: 'Perjanjian Manajemen Keagenan Asuransi Jiwa (PMKAJ)',
                  onUnderstood: () {
                    controller.markPmkajDocumentViewed();
                    Get.back();
                  },
                );
              },
              isViewed: controller.isPmkajDocumentViewed,
              canOpen: controller.canOpenDocument('PMKAJ').value,
            ),
          ),

          // Kode Etik Document
          Obx(
            () => _documentCard(
              context,
              title: 'Kode Etik Agen Asuransi',
              onTap: () {
                _navigateToDocumentDetail(
                  context,
                  url: controller.etikUrl,
                  isViewed: controller.isKodeEtikDocumentViewed.value,
                  title: 'Kode Etik Agen Asuransi',
                  onUnderstood: () {
                    controller.markKodeEtikDocumentViewed();
                    Get.back();
                  },
                );
              },
              isViewed: controller.isKodeEtikDocumentViewed,
              canOpen: controller.canOpenDocument('KODE_ETIK').value,
            ),
          ),

          // Anti Twisting Document
          Obx(
            () => _documentCard(
              context,
              title: 'Peraturan Anti Twisting',
              onTap: () {
                _navigateToDocumentDetail(
                  context,
                  url: controller.antiTwistUrl,
                  isViewed: controller.isAntiTwistingDocumentViewed.value,
                  title: 'Peraturan Anti Twisting',
                  onUnderstood: () {
                    controller.markAntiTwistingDocumentViewed();
                    Get.back();
                  },
                );
              },
              isViewed: controller.isAntiTwistingDocumentViewed,
              canOpen: controller.canOpenDocument('ANTI_TWISTING').value,
            ),
          ),

          _padding(
            Text(
              'Paraf dan Tanda Tangan',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
          ),
          // Warning when not all TNC are checked
          Obx(
            () =>
                controller.isAllTermsRead.isTrue
                    ? Container()
                    : _padding(
                      Container(
                        width: Get.width,
                        padding: EdgeInsets.all(paddingMedium),
                        decoration: BoxDecoration(
                          color: kColorGlobalBgRed,
                          borderRadius: BorderRadius.circular(radiusSmall),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning_amber_rounded,
                              color: kColorGlobalRed,
                            ),
                            SizedBox(width: paddingSmall),
                            Expanded(
                              child: Text(
                                'Mohon membaca seluruh dokumen diatas sebelum menandatangani dokumen',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(color: kColorGlobalRed),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
          ),
          // Paraf & ttd muncul ketika all the TNC are checked
          _padding(
            Obx(
              () =>
                  controller.isAllTermsRead.value
                      ? SizedBox(
                        width: Get.width,
                        child: Row(
                          children: [
                            _parafCard(context, controller),
                            SizedBox(width: paddingMedium),
                            _signatureCard(context, controller),
                          ],
                        ),
                      )
                      : SizedBox.shrink(),
            ),
          ),
          _padding(
            Obx(
              () => GestureDetector(
                onTap:
                    controller.canEnableCheckbox()
                        ? () => controller.toggleAgreement(
                          !controller.isAgreementChecked.value,
                        )
                        : null,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      controller.isAgreementChecked.value
                          ? Icons.check_box_rounded
                          : Icons.check_box_outline_blank_rounded,
                      color:
                          controller.isAgreementChecked.value
                              ? kColorGlobalBlue
                              : controller.canEnableCheckbox()
                              ? (Get.isDarkMode
                                  ? kColorBorderDark
                                  : kColorBorderLight)
                              : (Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight),
                    ),
                    SizedBox(width: paddingSmall),
                    Expanded(
                      child: Text(
                        'Dengan menandatangani dokumen berikut, saya (kandidat) telah membaca dan menyetujui seluruh dokumen perjanjian keagenan ini.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color:
                              controller.canEnableCheckbox()
                                  ? (Get.isDarkMode
                                      ? kColorTextTersier
                                      : kColorTextTersierLight)
                                  : (Get.isDarkMode
                                      ? kColorTextTersier.withValues(alpha: 0.5)
                                      : kColorTextTersierLight.withValues(
                                        alpha: 0.5,
                                      )),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }

  Padding _documentCard(
    BuildContext context, {
    required String title,
    required Function() onTap,
    required RxBool isViewed,
    required bool canOpen,
  }) {
    return _padding(
      Obx(
        () => GestureDetector(
          onTap: canOpen ? onTap : null,
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              ),
              borderRadius: BorderRadius.circular(radiusSmall),
            ),
            child: Row(
              children: [
                Icon(
                  isViewed.isTrue
                      ? Icons.check_circle
                      : Icons.check_circle_outline,
                  color:
                      isViewed.isTrue
                          ? kColorGlobalGreen
                          : (Get.isDarkMode
                              ? kColorBorderDark
                              : kColorBorderLight),
                ),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color:
                          canOpen
                              ? null
                              : (Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight),
                    ),
                  ),
                ),
                SizedBox(width: paddingSmall),
                Icon(
                  Icons.chevron_right,
                  color:
                      canOpen
                          ? null
                          : (Get.isDarkMode
                              ? kColorTextTersier
                              : kColorTextTersierLight),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding _padding(Widget child) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: child,
    );
  }

  Expanded _parafCard(
    BuildContext context,
    PublicFormTermsOnlyController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Paraf',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (controller.isParafUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (controller.isParafCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  _showSignaturePad(
                    context,
                    title: 'Paraf',
                    onSave: (signatureData) {
                      controller.setParafData(signatureData);
                    },
                    currentData: controller.parafData.value,
                  );
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: kColorBgLight,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        controller.isParafCompleted.value
                            ? controller.parafData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(controller.parafData.value),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Paraf\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : Center(
                                  child: Text(
                                    'Paraf\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (controller.parafError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    controller.parafError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Expanded _signatureCard(
    BuildContext context,
    PublicFormTermsOnlyController termsController,
  ) {
    return Expanded(
      child: Obx(
        () => Container(
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: kColorGlobalBgBlue,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    'Tanda Tangan',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: kColorPaninBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (controller.isSignatureUploading.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: kColorGlobalBlue,
                        ),
                      ),
                    )
                  else if (controller.isSignatureCompleted.value)
                    Padding(
                      padding: EdgeInsets.only(left: paddingSmall),
                      child: Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                    ),
                ],
              ),
              SizedBox(height: paddingSmall),
              GestureDetector(
                onTap: () {
                  _showSignaturePad(
                    context,
                    title: 'Tanda Tangan',
                    onSave: (signatureData) {
                      controller.setSignatureData(signatureData);
                    },
                    currentData: controller.signatureData.value,
                  );
                },
                child: Container(
                  width: Get.width,
                  padding: EdgeInsets.all(paddingMedium),
                  decoration: BoxDecoration(
                    color: kColorBgLight,
                    borderRadius: BorderRadius.circular(radiusMedium),
                  ),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        controller.isSignatureCompleted.value
                            ? controller.signatureData.value.isNotEmpty
                                ? ClipRRect(
                                  borderRadius: BorderRadius.circular(
                                    radiusMedium,
                                  ),
                                  child: Image.memory(
                                    base64Decode(
                                      controller.signatureData.value,
                                    ),
                                    fit: BoxFit.contain,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Center(
                                        child: Text(
                                          'Tanda Tangan\nTersimpan',
                                          textAlign: TextAlign.center,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color: kColorGlobalGreen,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                                : Center(
                                  child: Text(
                                    'Tanda Tangan\nTersimpan',
                                    textAlign: TextAlign.center,
                                    style: Theme.of(context).textTheme.bodySmall
                                        ?.copyWith(color: kColorGlobalGreen),
                                  ),
                                )
                            : Center(
                              child: Icon(
                                Icons.edit,
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                            ),
                  ),
                ),
              ),
              if (controller.signatureError.value.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: paddingSmall),
                  child: Text(
                    controller.signatureError.value,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: kColorGlobalRed),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSignaturePad(
    BuildContext context, {
    required String title,
    required Function(String) onSave,
    required String currentData,
  }) {
    final SignatureController signatureController = SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );

    // Note: The signature package doesn't support loading from base64 directly
    // We'll show empty pad but indicate there's existing data in the UI

    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  // color: kColorGlobalBgBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(radiusMedium),
                    topRight: Radius.circular(radiusMedium),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          color: kColorPaninBlue,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Get.back(),
                      child: Icon(Icons.close, color: kColorPaninBlue),
                    ),
                  ],
                ),
              ),

              // Signature pad area
              Container(
                margin: EdgeInsets.all(paddingMedium),
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(
                    color:
                        Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                  ),
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(radiusSmall),
                  child: Signature(
                    controller: signatureController,
                    backgroundColor: Colors.white,
                  ),
                ),
              ),

              // Current signature indicator
              if (currentData.isNotEmpty)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: paddingMedium),
                  padding: EdgeInsets.all(paddingSmall),
                  decoration: BoxDecoration(
                    color: kColorGlobalBgGreen,
                    borderRadius: BorderRadius.circular(radiusSmall),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: kColorGlobalGreen,
                        size: 16,
                      ),
                      SizedBox(width: paddingSmall),
                      Expanded(
                        child: Text(
                          'Terdapat $title yang tersimpan. Buat baru untuk mengganti.',
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: kColorGlobalGreen),
                        ),
                      ),
                    ],
                  ),
                ),

              // Action buttons
              Container(
                padding: EdgeInsets.all(paddingMedium),
                child: Row(
                  children: [
                    Expanded(
                      child: PdlButton(
                        title: 'Ulangi',
                        onPressed: () {
                          signatureController.clear();
                        },
                        backgroundColor: Colors.transparent,
                        foregorundColor:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                        borderColor:
                            Get.isDarkMode
                                ? kColorBorderDark
                                : kColorBorderLight,
                      ),
                    ),
                    SizedBox(width: paddingMedium),
                    Expanded(
                      child: PdlButton(
                        title: 'Simpan',
                        onPressed: () async {
                          if (signatureController.isNotEmpty) {
                            final Uint8List? signature =
                                await signatureController.toPngBytes();
                            if (signature != null) {
                              final String base64String = base64Encode(
                                signature,
                              );
                              onSave(base64String);
                              Get.back();
                            }
                          } else {
                            // If empty but there was existing data, clear it
                            if (currentData.isNotEmpty) {
                              onSave('');
                              Get.back();
                            } else {
                              Get.snackbar(
                                'Peringatan',
                                'Silakan buat $title terlebih dahulu',
                                backgroundColor: kColorGlobalBgRed,
                                colorText: kColorGlobalRed,
                              );
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDocumentDetail(
    BuildContext context, {
    required String title,
    required RxString url,
    required bool isViewed,
    required Function() onUnderstood,
  }) {
    RxBool isScrolledToBottom = false.obs;

    Get.to(
      () => BaseDetailPage(
        title: title,
        width: Get.width,
        controller: controller,
        onRefresh: () {},
        bottomAction: onUnderstood,
        backEnabled: true,
        bottomText: 'Sudah Mengerti',
        isBottomActionEnabled: isScrolledToBottom,
        child: SizedBox(
          width: Get.width,
          height: Get.height / 1.25,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingLarge),
              Expanded(
                child: Obx(
                  () =>
                      url.value != ''
                          ? SizedBox(
                            width: Get.width,
                            height: Get.height,
                            child: WebviewPage(
                              fullUrl: url.value,
                              onScrolledToBottom: (scrolled) {
                                if (isViewed == true) {
                                  isScrolledToBottom.value = false;
                                } else {
                                  isScrolledToBottom.value = scrolled;
                                }
                              },
                            ),
                          )
                          : Container(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
