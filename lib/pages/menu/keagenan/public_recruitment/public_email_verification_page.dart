import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/public_recruitment/public_email_verification_controller.dart';
import 'package:pdl_superapp/pages/widget/public_page_wrapper.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PublicEmailVerificationPage extends StatelessWidget {
  PublicEmailVerificationPage({super.key});

  final PublicEmailVerificationController controller = Get.put(
    PublicEmailVerificationController(),
  );

  @override
  Widget build(BuildContext context) {
    // Check if this is web platform only
    if (!kIsWeb) {
      return PublicPageWrapper(
        showAppBar: true,
        title: 'Verifikasi Email',
        onBack: () => Get.back(),
        child: _buildWebOnlyMessage(context),
      );
    }

    return PublicPageWrapper(
      showAppBar: true,
      title: 'Veri<PERSON><PERSON><PERSON> Email',
      onBack: () => controller.goBack(),
      child: _buildContent(context),
    );
  }

  Widget _buildWebOnlyMessage(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(paddingLarge),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.web,
                size: 80,
                color: kColorTextTersier,
              ),
              SizedBox(height: paddingLarge),
              Text(
                'Halaman Web Only',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingMedium),
              Text(
                'Halaman verifikasi email ini hanya dapat diakses melalui web browser.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: kColorTextTersier,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingExtraLarge),
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  title: 'Kembali',
                  onPressed: () => Get.back(),
                  backgroundColor: kColorGlobalBlue,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(paddingLarge),
          child: Column(
            children: [
              Expanded(
                child: Center(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Obx(() => _buildStatusContent(context)),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(() => _buildBottomActions(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusContent(BuildContext context) {
    if (controller.isVerifying.value) {
      return _buildLoadingState(context);
    } else if (controller.isVerified.value) {
      return _buildSuccessState(context);
    } else if (controller.hasError.value) {
      return _buildErrorState(context);
    } else {
      return _buildInitialState(context);
    }
  }

  Widget _buildLoadingState(BuildContext context) {
    return Column(
      children: [
        CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(kColorGlobalBlue),
        ),
        SizedBox(height: paddingLarge),
        Text(
          'Memverifikasi Email...',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: paddingMedium),
        Text(
          'Mohon tunggu sebentar, kami sedang memverifikasi email Anda.',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: kColorTextTersier,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSuccessState(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: kColorGlobalBgGreen,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.check,
            size: 40,
            color: kColorGlobalGreen,
          ),
        ),
        SizedBox(height: paddingLarge),
        Text(
          'Email Berhasil Diverifikasi!',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: kColorGlobalGreen,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: paddingMedium),
        Text(
          controller.successMessage.value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: kColorTextTersier,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: kColorGlobalBgRed,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.error_outline,
            size: 40,
            color: kColorGlobalRed,
          ),
        ),
        SizedBox(height: paddingLarge),
        Text(
          'Verifikasi Gagal',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: kColorGlobalRed,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: paddingMedium),
        Text(
          controller.errorMessage.value,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: kColorTextTersier,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Column(
      children: [
        Utils.cachedSvgWrapper(
          'icon/ic-linear-sms.svg',
          width: 80,
          height: 80,
          color: kColorGlobalBlue,
        ),
        SizedBox(height: paddingLarge),
        Text(
          'Verifikasi Email',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: paddingMedium),
        Text(
          'Mempersiapkan verifikasi email Anda...',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: kColorTextTersier,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    if (controller.isVerifying.value) {
      return SizedBox.shrink(); // No actions while loading
    } else if (controller.isVerified.value) {
      return SizedBox(
        width: double.infinity,
        child: PdlButton(
          title: 'Selesai',
          onPressed: controller.goToHome,
          backgroundColor: kColorGlobalGreen,
        ),
      );
    } else if (controller.hasError.value) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              title: 'Coba Lagi',
              onPressed: controller.retryVerification,
              backgroundColor: kColorGlobalBlue,
            ),
          ),
          SizedBox(height: paddingMedium),
          SizedBox(
            width: double.infinity,
            child: PdlButton(
              title: 'Kembali',
              onPressed: controller.goBack,
              backgroundColor: Colors.transparent,
              foregorundColor: kColorGlobalBlue,
            ),
          ),
        ],
      );
    } else {
      return SizedBox(
        width: double.infinity,
        child: PdlButton(
          title: 'Kembali',
          onPressed: controller.goBack,
          backgroundColor: Colors.transparent,
          foregorundColor: kColorGlobalBlue,
        ),
      );
    }
  }
}
