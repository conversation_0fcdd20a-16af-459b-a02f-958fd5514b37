import 'dart:io' show File;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class PhotoImageCropperPage extends StatefulWidget {
  const PhotoImageCropperPage({super.key});

  @override
  State<PhotoImageCropperPage> createState() => _PhotoImageCropperPageState();
}

class _PhotoImageCropperPageState extends State<PhotoImageCropperPage> {
  final dynamic imageFile = Get.arguments['imageFile']; // Can be File or XFile
  String type = Get.arguments['type'] ?? '';

  CroppedFile? croppedFile;
  bool isCropping = false;

  final PhotoImageCropperController controller = Get.put(
    PhotoImageCropperController(),
  );

  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Start cropping when the page loads, but only once
    if (!_hasInitialized) {
      _hasInitialized = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _cropImage();
      });
    }
  }

  Future<void> _cropImage() async {
    setState(() {
      isCropping = true;
    });

    try {
      // KTP card dimensions ratio (85.6 x 53.98 mm)
      double ktpAspectRatio = 3 / 4;

      if (type == kPhotoTypeKtp) {
        ktpAspectRatio = 85.6 / 53.98;
      } else if (type == kPhotoTypePasFoto) {
        ktpAspectRatio = 1;
      }

      // Get the correct path based on the file type
      String sourcePath;
      if (kIsWeb) {
        // On web, imageFile might be XFile or have different path handling
        sourcePath = imageFile.path;
      } else {
        // On mobile, ensure it's a File
        sourcePath = imageFile is File ? imageFile.path : imageFile.path;
      }

      final croppedImage = await ImageCropper().cropImage(
        sourcePath: sourcePath,
        aspectRatio: CropAspectRatio(ratioX: ktpAspectRatio, ratioY: 1),
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Sesuaikan Ukuran Foto',
            toolbarColor: kColorGlobalBlue,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: true,
            hideBottomControls: false,
          ),
          IOSUiSettings(
            title: 'Sesuaikan Ukuran Foto',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
          ),
          WebUiSettings(
            context: context,
            presentStyle: WebPresentStyle.dialog,
            size: CropperSize(
              width: (Get.width - paddingLarge).toInt(),
              height: (Get.height / 1.7).toInt(),
            ),
          ),
        ],
      );

      if (croppedImage != null) {
        setState(() {
          croppedFile = croppedImage;
          isCropping = false;
        });
      } else {
        // User canceled cropping, go back
        Get.back();
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to crop image: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
      setState(() {
        isCropping = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'Sesuaikan Ukuran Foto',
      onRefresh: () {},
      controller: controller,
      bottomWidget: Row(
        children: [
          Expanded(
            child: PdlButton(
              title: 'Ulangi',
              onPressed: () => _cropImage(),
              backgroundColor: Colors.transparent,
              foregorundColor: kColorPaninBlue,
            ),
          ),
          SizedBox(width: paddingSmall),
          Expanded(
            child: PdlButton(
              title: 'Gunakan Foto',
              onPressed: () {
                if (croppedFile != null) {
                  if (kIsWeb) {
                    // On web, return XFile
                    Get.back(result: XFile(croppedFile!.path));
                  } else {
                    // On mobile, return File
                    Get.back(result: File(croppedFile!.path));
                  }
                }
              },
            ),
          ),
        ],
      ),
      child:
          isCropping
              ? const Center(child: CircularProgressIndicator())
              : croppedFile != null
              ? Container(
                padding: EdgeInsets.all(paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Pastikan foto sudah sesuai dengan ukuran yang diinginkan',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    SizedBox(height: paddingMedium),
                    Container(
                      width: Get.width,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(radiusMedium),
                        border: Border.all(color: kColorGlobalBlue, width: 2.0),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(radiusMedium - 2),
                        child:
                            kIsWeb
                                ? Image.network(
                                  croppedFile!.path,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      color: Colors.grey[300],
                                      child: Icon(
                                        Icons.image_not_supported,
                                        color: Colors.grey[600],
                                      ),
                                    );
                                  },
                                )
                                : Image.file(
                                  File(croppedFile!.path),
                                  fit: BoxFit.cover,
                                ),
                      ),
                    ),
                  ],
                ),
              )
              : const SizedBox(),
    );
  }
}

class PhotoImageCropperController extends BaseControllers {}
