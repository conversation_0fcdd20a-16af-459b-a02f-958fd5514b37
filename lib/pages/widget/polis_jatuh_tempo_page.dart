import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/controllers/widget/polis_jatuh_tempo_detail_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class PolisJatuhTempoPage extends StatelessWidget {
  PolisJatuhTempoPage({super.key});

  final PolisJatuhTempoDetailController controller = Get.put(
    PolisJatuhTempoDetailController(
      agentCode: Get.parameters['agentCode'],
      isShowOtherAgent: Get.parameters['type'].toString() == "1",
    ),
    tag: "polis-jatuh-tempo-page-${DateTime.now().millisecondsSinceEpoch}",
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      title: 'expired_policy_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tab selector
              _buildTabSelector(context),

              // Search bar
              _buildSearchBar(),

              // Loading indicator
              if (controller.isLoading.value)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(paddingLarge),
                    child: CircularProgressIndicator(),
                  ),
                ),

              // Content based on selected tab
              if (!controller.isLoading.value) _buildContent(),
            ],
          );
        }),
      ),
    );
  }

  // Tab selector
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: paddingMedium),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToUpcoming(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'policy_due_soon_str'.tr,
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToPast(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'policy_expired_str'.tr,
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on selected tab
  Widget _buildContent() {
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildUpcomingContent();
      } else {
        return _buildPastContent();
      }
    });
  }

  // Upcoming Content
  Widget _buildUpcomingContent() {
    return Obx(() {
      final isLoading = controller.upcomingController.isLoading.value;
      final hasError = controller.upcomingController.hasError.value;
      final errorMessage = controller.upcomingController.errorMessage.value;

      void onRetry() {
        controller.upcomingController.fetchPolicyOverdueData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.upcomingController.policyOverdueList.isEmpty) {
        return Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("no_data_policy_str".tr),
          ),
        );
      }

      // Show all records
      final displayedPolicies = controller.upcomingController.policyOverdueList;

      return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: displayedPolicies.length,
        padding: EdgeInsets.zero,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final policy = displayedPolicies[index];
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: paddingMedium),
            child: PolicyHolderItem(
              name: policy.policyHolder,
              policyNumber: policy.policyNumber,
              dueDate: formatDate(policy.dueDate),
              status: "ENDING", // Upcoming policies are always ENDING
            ),
          );
        },
      );
    });
  }

  // Past Content
  Widget _buildPastContent() {
    return Obx(() {
      final isLoading = controller.pastController.isLoading.value;
      final hasError = controller.pastController.hasError.value;
      final errorMessage = controller.pastController.errorMessage.value;

      void onRetry() {
        controller.pastController.fetchPolicyOverdueData();
      }

      if (isLoading) {
        return const Center(child: CircularProgressIndicator());
      }

      if (hasError) {
        return Center(
          child: Column(
            children: [
              const Icon(Icons.error_outline, size: 48, color: Colors.red),
              const SizedBox(height: paddingSmall),
              Text('Error: $errorMessage'),
              const SizedBox(height: paddingMedium),
              ElevatedButton(onPressed: onRetry, child: Text('retry_str'.tr)),
            ],
          ),
        );
      }

      if (controller.pastController.policyOverdueList.isEmpty) {
        return Center(
          child: Padding(
            padding: EdgeInsets.all(paddingMedium),
            child: Text("no_data_policy_str".tr),
          ),
        );
      }

      // Show all records
      final displayedPolicies = controller.pastController.policyOverdueList;

      return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: displayedPolicies.length,
        padding: EdgeInsets.zero,
        separatorBuilder: (context, index) => const Divider(),
        itemBuilder: (context, index) {
          final policy = displayedPolicies[index];
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: paddingMedium),
            child: PolicyHolderItem(
              name: policy.policyHolder,
              policyNumber: policy.policyNumber,
              dueDate: formatDate(policy.dueDate),
              status: "LAPSE", // Past policies are always LAPSE
            ),
          );
        },
      );
    });
  }

  // Search bar
  Widget _buildSearchBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      spacing: paddingSmall,
      children: [
        // Search bar
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: paddingSmall,
              vertical: paddingSmall,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(radiusSmall),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              spacing: paddingSmall,
              children: [
                // Search mode dropdown (replacing search icon)
                DropdownButton<String>(
                  value: controller.searchMode.value,
                  underline: const SizedBox(),
                  icon: const Icon(Icons.arrow_drop_down, size: 18),
                  iconSize: 18,
                  isDense: true,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                  items: [
                    DropdownMenuItem(
                      value: 'policyHolderName',
                      child: Text('name_str'.tr, style: TextStyle(fontSize: 14)),
                    ),
                    DropdownMenuItem(
                      value: 'policyNumber',
                      child: Text('policy_no_str'.tr, style: TextStyle(fontSize: 14)),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      controller.setSearchMode(value);
                    }
                  },
                ),
                // Vertical divider
                Container(height: 24, width: 1, color: Colors.grey.shade300),
                // Search field
                Expanded(
                  child: TextField(
                    controller: controller.searchTextController,
                    decoration: InputDecoration(
                      hintText: _getSearchHint(controller.searchMode.value).tr,
                      border: InputBorder.none,
                      isDense: true,
                      isCollapsed: true,
                      contentPadding: const EdgeInsets.symmetric(
                        vertical: paddingExtraSmall,
                      ),
                      fillColor: Colors.transparent,
                    ),
                    onChanged: (value) {
                      // Just update the value but don't trigger search
                      controller.searchQuery.value = value;
                    },
                    onSubmitted: (value) {
                      // Apply search when Enter is pressed
                      controller.searchQuery.value = value;
                      controller.applySearch();
                    },
                  ),
                ),
                // Clear button if search query exists
                if (controller.searchQuery.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear, size: 16),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    onPressed: () {
                      controller.searchTextController.text = "";
                      controller.resetSearch();
                    },
                  ),
                // Search button
                IconButton(
                  icon: const Icon(Icons.search, size: 20),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: () {
                    controller.applySearch();
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Get search hint based on search mode
  String _getSearchHint(String searchMode) {
    switch (searchMode) {
      case 'policyHolderName':
        return 'search_name_policy_str';
      case 'policyNumber':
        return 'search_no_policy_str';
      default:
        return 'search_other_policy_str';
    }
  }

  // Format date
  String formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }
}

class PolicyHolderItem extends StatelessWidget {
  final String name;
  final String policyNumber;
  final String dueDate;
  final String status;

  const PolicyHolderItem({
    super.key,
    required this.name,
    required this.policyNumber,
    required this.dueDate,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    // Define the fields to display
    final List<Map<String, dynamic>> fields = [
      {'label': 'policy_holder_str'.tr, 'value': name, 'isStatus': false},
      {'label': 'policy_no_str'.tr, 'value': policyNumber, 'isStatus': false},
      {'label': 'expired_date_str'.tr, 'value': dueDate, 'isStatus': false},
      {'label': 'status_str'.tr, 'value': status, 'isStatus': true},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingSmall,
      children: [
        // Generate rows for each field using a loop
        for (int i = 0; i < fields.length; i++)
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: paddingMedium,
            children: [
              Expanded(
                child: Text(
                  fields[i]['label'],
                  style: TextStyle(
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                    fontSize: 16,
                  ),
                ),
              ),
              if (fields[i]['isStatus'] == true)
                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(fields[i]['value']),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getDisplayStatus(fields[i]['value']),
                    style: TextStyle(
                      color: _getStatusTextColor(fields[i]['value']),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                // Regular text value
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      fields[i]['value'],
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
      ],
    );
  }

  // Get status display text
  String _getDisplayStatus(String status) {
    switch (status) {
      case "ENDING":
        return "ending_str".tr;
      case "LAPSE":
        return "lapse_str".tr;
      case "ACTIVE":
        return "active_str".tr;
      default:
        return status;
    }
  }

  // Get status color
  Color _getStatusColor(String status) {
    switch (status) {
      case "ENDING":
        return const Color(0xFFFDF1E0); // Yellow background
      case "LAPSE":
        return const Color(0xFFFFE5E5); // Red background
      case "ACTIVE":
        return const Color(0xFFE0F2F1); // Green background
      default:
        return const Color(0xFFE3F2FD); // Blue background
    }
  }

  // Get status text color
  Color _getStatusTextColor(String status) {
    switch (status) {
      case "ENDING":
        return const Color(0xFFAD7B2A); // Yellow text
      case "LAPSE":
        return const Color(0xFFD32F2F); // Red text
      case "ACTIVE":
        return const Color(0xFF00897B); // Green text
      default:
        return const Color(0xFF1976D2); // Blue text
    }
  }
}
