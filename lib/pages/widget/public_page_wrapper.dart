import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/flavors.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// Wrapper khusus untuk halaman public yang tidak memerlukan authentication
/// Berbeda dengan PageWrapper biasa yang mengasumsikan user sudah login
class PublicPageWrapper extends StatelessWidget {
  final Widget child;
  final bool showAppBar;
  final String? title;
  final VoidCallback? onBack;
  
  PublicPageWrapper({
    super.key, 
    required this.child,
    this.showAppBar = false,
    this.title,
    this.onBack,
  });
  
  final RxString appVersion = ''.obs;

  getAppVersion() async {
    appVersion.value = await Utils.getFullVersionApp();
  }

  @override
  Widget build(BuildContext context) {
    getAppVersion();
    
    Widget content = Stack(
      children: [
        child,
        if (F.appFlavor != Flavor.production)
          Positioned(right: 0, top: 120, child: _flavorBanner()),
      ],
    );

    // Jika showAppBar true, wrap dengan Scaffold yang memiliki AppBar
    if (showAppBar) {
      content = Scaffold(
        appBar: AppBar(
          title: Text(title ?? 'PDL SuperApp'),
          leading: onBack != null 
            ? IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: onBack,
              )
            : null,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
        ),
        body: Stack(
          children: [
            child,
            if (F.appFlavor != Flavor.production)
              Positioned(right: 0, top: 50, child: _flavorBanner()),
          ],
        ),
      );
    }

    return content;
  }

  IgnorePointer _flavorBanner() {
    return IgnorePointer(
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: paddingExtraSmall,
            horizontal: paddingSmall,
          ),
          color: kColorError.withValues(alpha: 0.4),
          child: Obx(
            () => Text(
              '${F.name} $appVersion',
              textDirection: TextDirection.ltr,
              style: Theme.of(Get.context!).textTheme.bodySmall?.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
