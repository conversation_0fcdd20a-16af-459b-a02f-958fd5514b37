import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/network_controller.dart';
import 'package:pdl_superapp/flavors.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class PageWrapper extends StatelessWidget {
  final Widget child;

  PageWrapper({super.key, required this.child});

  final RxString appVersion = ''.obs;

  getAppVersion() async {
    appVersion.value = await Utils.getFullVersionApp();
  }

  @override
  Widget build(BuildContext context) {
    getAppVersion();

    // Get NetworkController instance, but only for mobile platforms
    NetworkController? networkController;
    if (!kIsWeb) {
      try {
        networkController = Get.find<NetworkController>();
      } catch (e) {
        // NetworkController might not be initialized yet
        networkController = null;
      }
    }

    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).padding.bottom,
          ),
          child: child,
        ),
        if (F.appFlavor != Flavor.production)
          Positioned(right: 0, top: 120, child: _flavorBanner()),

        // Only show connection banner when there's no connection (mobile only)
        if (!kIsWeb && networkController != null)
          Obx(() {
            if (!networkController!.isConnected.value) {
              return Positioned(left: 0, top: 110, child: _connectionBanner());
            }
            return const SizedBox.shrink();
          }),
      ],
    );
  }

  IgnorePointer _connectionBanner() {
    return IgnorePointer(
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: paddingExtraSmall,
            horizontal: paddingSmall,
          ),
          color: kColorBorderDark.withValues(alpha: 0.7),
          child: Text(
            'Device sedang offline dan tidak ada update.',
            textDirection: TextDirection.ltr,
            style: Theme.of(
              Get.context!,
            ).textTheme.bodySmall?.copyWith(color: kColorTextDark),
          ),
        ),
      ),
    );
  }

  IgnorePointer _flavorBanner() {
    return IgnorePointer(
      child: Material(
        color: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: paddingExtraSmall,
            horizontal: paddingSmall,
          ),
          color: kColorError.withValues(alpha: 0.4),
          child: Obx(
            () => Text(
              '${F.name} $appVersion',
              textDirection: TextDirection.ltr,
              style: Theme.of(Get.context!).textTheme.bodySmall,
            ),
          ),
        ),
      ),
    );
  }
}
