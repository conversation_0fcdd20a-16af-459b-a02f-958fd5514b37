import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

import '../../base/base_detail_page.dart';
import '../../components/table_card.dart';
import '../../controllers/widget/persistensi_page_controller.dart';
import '../../models/persistensi_model.dart';
import '../../utils/keys.dart';
import '../../utils/utils.dart';

class PersistensiPage extends StatelessWidget {
  PersistensiPage({super.key});

  final PersistensiPageController controller = Get.put(
    PersistensiPageController(
      isShowOtherAgent: (Get.parameters['mode'] ?? "0").toString() != "0",
    ),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      scrollController: controller.teamController.scrollController,
      title: 'persistency_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          if (controller.isLoading.isTrue) {
            return const Center(child: CircularProgressIndicator());
          }
          return Column(
            children: [
              SizedBox(height: 10),
              // Show tabs based on channel and role
              if (controller.shouldShowTabs()) _buildTabSelector(context),

              // Content based on role and selected tab
              _buildContent(),
            ],
          );
        }),
      ),
    );
  }

  // Tab selector based on available tabs
  Widget _buildTabSelector(BuildContext context) {
    final availableTabs = controller.getAvailableTabs();

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children:
              availableTabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tabName = entry.value;
                final isSelected = controller.selectedSection.value == index;

                return Expanded(
                  child: InkWell(
                    onTap: () {
                      switch (index) {
                        case 0:
                          controller.switchToIndividu();
                          break;
                        case 1:
                          controller.switchToTeam();
                          break;
                        case 2:
                          controller.switchToArea();
                          break;
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border:
                            isSelected
                                ? Border.all(color: kColorBgLight)
                                : null,
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.surface
                                : null,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        tabName,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role (AGE channel), show only individu content
    if (controller.channel != kUserChannelBan && controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      switch (controller.selectedSection.value) {
        case 0:
          return _buildIndividuContent();
        case 1:
          return _buildTeamContent();
        case 2:
          return _buildAreaContent();
        default:
          return _buildIndividuContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.teamController.isLoadingMore.value) {
                return const Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build tables for each agent in the team/group
  Widget _buildAgentPersistencyTables() {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          (!controller.showFullData.value &&
                      controller.teamController.persistensiDataList.length > 3
                  ? controller.teamController.persistensiDataList.take(3)
                  : controller.teamController.persistensiDataList)
              .map((agent) {
                return _buildAgentPersistencyTable(agent);
              })
              .toList(),
    );
  }

  // Build a table card for a single agent's persistency data
  Widget _buildAgentPersistencyTable(PersistensiModel agent) {
    String agentType = agent.agentLevel;
    // String agentType = "";
    // switch (agent.type) {
    //   case "leader":
    //     agentType = "BM";
    //     break;
    //   case "agent":
    //     agentType = "BD";
    //     break;
    //   default:
    //     agentType = "BP";
    //     break;
    // }
    if (agent.name == '') {
      return Container();
    }
    final agentTitle = "$agentType ${agent.name}";
    return SizedBox(
      width: Get.width,
      child: TableCard(
        avatar:
            controller.agentCode == agent.agentCode ? agent.agentPhoto : null,
        title: agentTitle,
        headers: ["P-13", "P-25", "P-37", "P-49", "P-61"],
        rows: [
          [
            controller.teamController.formatPercentage(agent.persistency13),
            controller.teamController.formatPercentage(agent.persistency25),
            controller.teamController.formatPercentage(agent.persistency37),
            controller.teamController.formatPercentage(agent.persistency49),
            controller.teamController.formatPercentage(agent.persistency61),
          ],
        ],
        // Enable horizontal scrolling
        horizontalScrollable: true,
        // Set minimum column widths
        minColumnWidths: {
          0: (Get.width * .92) / 5, // P-13 column
          1: (Get.width * .92) / 5, // P-25 column
          2: (Get.width * .92) / 5, // P-37 column
          3: (Get.width * .92) / 5, // P-49 column
          4: (Get.width * .92) / 5, // P-61 column
        },
      ),
    );
  }

  // Area Content (for Cabang view)
  Widget _buildAreaContent() {
    return Obx(() {
      final isLoading = controller.areaController.isLoading.value;
      final hasError = controller.areaController.hasError.value;
      final errorMessage = controller.areaController.errorMessage.value;

      void onRetry() {
        controller.areaController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.areaController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAreaPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.areaController.isLoadingMore.value) {
                return const Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build area persistency tables
  Widget _buildAreaPersistencyTables() {
    return Column(
      spacing: paddingMedium,
      children:
          controller.areaController.persistensiDataList.map((area) {
            return _buildAreaPersistencyTable(area);
          }).toList(),
    );
  }

  // Build a table card for a single area's persistency data
  Widget _buildAreaPersistencyTable(PersistensiModel area) {
    if (area.name == '') {
      return Container();
    }

    return TableCard(
      title: area.name, // Area/Team name
      headers: ["P-13", "P-25", "P-37"],
      rows: [
        [
          controller.areaController.formatPercentage(area.persistency13),
          controller.areaController.formatPercentage(area.persistency25),
          controller.areaController.formatPercentage(area.persistency37),
        ],
      ],
      // Enable horizontal scrolling
      horizontalScrollable: true,
      // Set minimum column widths
      minColumnWidths: {
        0: (Get.width * .92) / 3, // P-13 column
        1: (Get.width * .92) / 3, // P-25 column
        2: (Get.width * .92) / 3, // P-37 column
      },
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }
}
