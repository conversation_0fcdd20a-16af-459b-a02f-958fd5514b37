import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';

import '../../base/base_detail_page.dart';
import '../../components/table_card.dart';
import '../../components/filter_button.dart';
import '../../components/persistensi_filter.dart';
import '../../components/pdl_text_field.dart';
import '../../controllers/widget/persistensi_page_controller.dart';
import '../../models/persistensi_model.dart';
import '../../routes/app_routes.dart';
import '../../utils/keys.dart';
import '../../utils/utils.dart';

class PersistensiPage extends StatelessWidget {
  PersistensiPage({super.key});

  final PersistensiPageController controller = Get.put(
    PersistensiPageController(
      isShowOtherAgent: (Get.parameters['mode'] ?? "0").toString() != "0",
    ),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BaseDetailPage(
        onRefresh: () => controller.refreshData(),
        backEnabled: true,
        controller: controller,
        scrollController: controller.teamController.scrollController,
        title: controller.getPageTitle(),
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Obx(() {
            if (controller.isLoading.isTrue) {
              return const Center(child: CircularProgressIndicator());
            }
            return Column(
              children: [
                SizedBox(height: 10),
                // Show agent profile if viewing other agent
                if (controller.isShowOtherAgent) _buildAgentProfile(),

                // Show tabs based on channel and role
                if (controller.shouldShowTabs()) _buildTabSelector(context),

                // Show search and filter for team/group view
                if (controller.shouldShowTabs() &&
                    controller.selectedSection.value != 0)
                  _buildSearchAndFilter(),

                // Content based on role and selected tab
                _buildContent(),
              ],
            );
          }),
        ),
      ),
    );
  }

  // Tab selector based on available tabs
  Widget _buildTabSelector(BuildContext context) {
    final availableTabs = controller.getAvailableTabs();

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children:
              availableTabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tabName = entry.value;
                final isSelected = controller.selectedSection.value == index;

                return Expanded(
                  child: InkWell(
                    onTap: () {
                      switch (index) {
                        case 0:
                          controller.switchToIndividu();
                          break;
                        case 1:
                          controller.switchToTeam();
                          break;
                        case 2:
                          controller.switchToArea();
                          break;
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border:
                            isSelected
                                ? Border.all(color: kColorBgLight)
                                : null,
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.surface
                                : null,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        tabName,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role (AGE channel), show only individu content
    if (controller.channel != kUserChannelBan && controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      switch (controller.selectedSection.value) {
        case 0:
          return _buildIndividuContent();
        case 1:
          return _buildTeamContent();
        case 2:
          return _buildAreaContent();
        default:
          return _buildIndividuContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.teamController.isLoadingMore.value) {
                return const Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build single table for team/group persistency data
  Widget _buildAgentPersistencyTables() {
    final agents =
        (!controller.showFullData.value &&
                    controller.teamController.persistensiDataList.length > 3
                ? controller.teamController.persistensiDataList.take(3)
                : controller.teamController.persistensiDataList)
            .toList();

    if (agents.isEmpty) {
      return Container();
    }

    return _buildClickableTable(
      headers: ["name_str".tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: agents,
      isTeamData: true,
    );
  }

  // Build clickable table with name as first column (same as widget)
  Widget _buildClickableTable({
    required List<String> headers,
    required List<PersistensiModel> agents,
    required bool isTeamData,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: paddingMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0075BD),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radiusSmall),
                topRight: Radius.circular(radiusSmall),
              ),
            ),
            child: Row(
              children:
                  headers.map((header) {
                    return Expanded(
                      flex:
                          header == headers.first ? 2 : 1, // Name column wider
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          header,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),

          // Data rows
          ...agents.map((agent) {
            if (agent.name == '') return Container();

            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  // Name column (clickable)
                  Expanded(
                    flex: 2,
                    child: InkWell(
                      onTap: () => _navigateToAgentDetail(agent),
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          agent.name,
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),

                  // Persistency columns
                  if (isTeamData) ...[
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // Navigate to agent detail page
  void _navigateToAgentDetail(PersistensiModel agent) {
    // Determine mode based on current section
    int mode = controller.selectedSection.value;
    Get.toNamed(
      '${Routes.PERSISTENSI}?mode=$mode&agentCode=${agent.agentCode}',
    );
  }

  // Area Content (for Cabang view)
  Widget _buildAreaContent() {
    return Obx(() {
      final isLoading = controller.areaController.isLoading.value;
      final hasError = controller.areaController.hasError.value;
      final errorMessage = controller.areaController.errorMessage.value;

      void onRetry() {
        controller.areaController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.areaController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAreaPersistencyTables(),

            // Load more indicator
            Obx(() {
              if (controller.areaController.isLoadingMore.value) {
                return const Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child: Center(child: CircularProgressIndicator()),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      );
    });
  }

  // Build area persistency tables
  Widget _buildAreaPersistencyTables() {
    return Column(
      spacing: paddingMedium,
      children:
          controller.areaController.persistensiDataList.map((area) {
            return _buildAreaPersistencyTable(area);
          }).toList(),
    );
  }

  // Build a table card for a single area's persistency data
  Widget _buildAreaPersistencyTable(PersistensiModel area) {
    if (area.name == '') {
      return Container();
    }

    return TableCard(
      title: area.name, // Area/Team name
      headers: ["P-13", "P-25", "P-37"],
      rows: [
        [
          controller.areaController.formatPercentage(area.persistency13),
          controller.areaController.formatPercentage(area.persistency25),
          controller.areaController.formatPercentage(area.persistency37),
        ],
      ],
      // Enable horizontal scrolling
      horizontalScrollable: true,
      // Set minimum column widths
      minColumnWidths: {
        0: (Get.width * .92) / 3, // P-13 column
        1: (Get.width * .92) / 3, // P-25 column
        2: (Get.width * .92) / 3, // P-37 column
      },
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }

  // Build agent profile section for other agent view
  Widget _buildAgentProfile() {
    return Obx(() {
      if (controller.isLoadingProfile.value) {
        return Padding(
          padding: EdgeInsets.all(paddingMedium),
          child: Center(child: CircularProgressIndicator()),
        );
      }

      final profile = controller.agentProfile.value;
      if (profile == null) {
        return Container();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: paddingMedium),
        padding: EdgeInsets.all(paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(radiusSmall),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage:
                  profile.photo != null && profile.photo!.isNotEmpty
                      ? NetworkImage(profile.photo!)
                      : null,
              child:
                  profile.photo == null || profile.photo!.isEmpty
                      ? Text(
                        Utils.getInitials(profile.name ?? ''),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                      : null,
            ),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    profile.name ?? '',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${profile.agentLevel ?? ''} - ${profile.agentCode ?? ''}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  if (profile.branchName != null)
                    Text(
                      profile.branchName!,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  // Search and filter for team/group view
  Widget _buildSearchAndFilter() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        children: [
          SizedBox(height: paddingSmall),
          Row(
            children: [
              Expanded(
                child: PdlTextField(
                  hint: 'search_str'.tr,
                  prefixIcon: Icon(Icons.search),
                  enabled: true,
                  onChanged: (value) {
                    // TODO: Implement search functionality
                  },
                ),
              ),
              SizedBox(width: paddingSmall),
              FilterButton(
                content: PersistensiFilter(
                  selectedView: _getSelectedViewString(),
                  availableViews: _getAvailableViewStrings(),
                  branches: [], // Dummy branches
                  selectedBranches: [],
                  onViewChanged: (view) => _onViewChanged(view),
                  onBranchesChanged: (branches) => {},
                  onReset: () => _onResetFilter(),
                  onApply: () => _onApplyFilter(),
                ),
                title: 'filter_str'.tr,
              ),
            ],
          ),
          SizedBox(height: paddingMedium),
        ],
      ),
    );
  }

  // Helper methods for filter
  String _getSelectedViewString() {
    switch (controller.selectedSection.value) {
      case 0:
        return 'my_self_str'.tr;
      case 1:
        return controller.level == kLevelBD ? 'group_str'.tr : 'team_str'.tr;
      case 2:
        return 'cabang_str'.tr;
      default:
        return 'my_self_str'.tr;
    }
  }

  List<String> _getAvailableViewStrings() {
    return controller.getAvailableTabs();
  }

  void _onViewChanged(String view) {
    if (view == 'my_self_str'.tr) {
      controller.selectedSection.value = 0;
    } else if (view == 'team_str'.tr || view == 'group_str'.tr) {
      controller.selectedSection.value = 1;
    } else if (view == 'cabang_str'.tr) {
      controller.selectedSection.value = 2;
    }
    controller.refreshData();
  }

  void _onResetFilter() {
    controller.selectedSection.value = 0;
    controller.refreshData();
  }

  void _onApplyFilter() {
    // Filter is already applied in _onViewChanged
    controller.refreshData();
  }
}
