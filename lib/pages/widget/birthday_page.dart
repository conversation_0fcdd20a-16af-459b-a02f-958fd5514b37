import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/filter_month_year.dart';
import 'package:pdl_superapp/components/state/empty_state_view.dart';
import 'package:pdl_superapp/controllers/widget/birthday_page_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class BirthdayPage extends StatelessWidget {
  BirthdayPage({super.key}) {
    // Initialize date formatting for Indonesian locale
    initializeDateFormatting('id_ID', null);
  }

  final BirthdayPageController controller = Get.put(BirthdayPageController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      onRefresh: () => controller.refreshData(),
      backEnabled: true,
      controller: controller,
      scrollController: controller.scrollController,
      title: 'customer_birthday_str'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(() {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search and filter
              _buildSearchFilterBar(context),
              Padding(
                padding: EdgeInsets.symmetric(vertical: paddingMedium),
                child: Text(
                  "30_days_ahead".tr,
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              // Loading indicator for initial load
              if (controller.isLoading.value && controller.birthdayList.isEmpty)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(paddingLarge),
                    child: CircularProgressIndicator(),
                  ),
                ),

              // Error message
              if (controller.hasError.value)
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red,
                      ),
                      const SizedBox(height: paddingSmall),
                      Text('Error: ${controller.errorMessage.value}'),
                      const SizedBox(height: paddingMedium),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: () => controller.refreshData(),
                            child: Text('retry_str'.tr),
                          ),
                          const SizedBox(width: paddingMedium),
                          if (controller.activeFilters.value.isNotEmpty ||
                              controller.searchQuery.isNotEmpty)
                            OutlinedButton(
                              onPressed: () {
                                controller.resetFilters();
                                controller.refreshData();
                              },
                              child: const Text('Reset Filter & Coba Lagi'),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),

              // Empty state
              if (!controller.isLoading.value &&
                  !controller.hasError.value &&
                  controller.birthdayList.isEmpty)
                Column(
                  children: [
                    EmptyStateView(
                      msg: 'Tidak ada data ulang tahun nasabah',
                      imageUrl: 'icon/illustration-empty-birthday.svg',
                    ),
                    if (controller.activeFilters.value.isNotEmpty ||
                        controller.searchQuery.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: paddingMedium),
                        child: TextButton(
                          onPressed: () {
                            controller.textEditingController.clear();
                            controller.resetFilters();
                            controller.refreshData();
                          },
                          child: const Text('Reset Filter'),
                        ),
                      ),
                  ],
                ),

              // No results message (when search/filter returns empty)
              if (!controller.isLoading.value &&
                  !controller.hasError.value &&
                  controller.birthdayList.isNotEmpty &&
                  controller.filteredList.isEmpty)
                Column(
                  children: [
                    EmptyStateView(
                      msg: 'Tidak ada hasil yang sesuai dengan pencarian',
                      imageUrl: 'icon/illustration-empty-search.svg',
                    ),
                    if (controller.activeFilters.value.isNotEmpty ||
                        controller.searchQuery.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: paddingMedium),
                        child: TextButton(
                          onPressed: () {
                            controller.textEditingController.clear();
                            controller.resetFilters();
                            controller.refreshData();
                          },
                          child: const Text('Reset pencarian'),
                        ),
                      ),
                  ],
                ),

              // Birthday list
              if (!controller.hasError.value &&
                  controller.birthdayList.isNotEmpty &&
                  controller.filteredList.isNotEmpty)
                Column(
                  children: [
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemCount: controller.filteredList.length,
                      separatorBuilder:
                          (context, index) => const Divider(thickness: 1),
                      itemBuilder: (context, index) {
                        final birthday = controller.filteredList[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: BirthdayCustomerItem(
                            name: birthday.customerName,
                            date: controller.formatDate(birthday.birthday),
                            initial: controller.getInitial(birthday.relation),
                            id: birthday.policyNumber,
                            phoneNumber: birthday.phoneNumber,
                            emailAddress: birthday.emailAddress,
                            controller: controller,
                          ),
                        );
                      },
                    ),
                    // Load more indicator
                    if (controller.isLoading.value &&
                        controller.birthdayList.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.all(paddingMedium),
                        child: CircularProgressIndicator(),
                      ),
                  ],
                ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildSearchFilterBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: paddingMedium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        spacing: paddingMedium,
        children: [
          // Search bar
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: paddingSmall,
                vertical: paddingSmall,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Row(
                spacing: paddingSmall,
                children: [
                  // Search icon
                  Icon(Icons.search, size: 20, color: Colors.grey.shade600),
                  // Search field
                  Expanded(
                    child: TextField(
                      controller: controller.textEditingController,
                      decoration: InputDecoration(
                        hintText: 'find_birthday_dec_str'.tr,
                        border: InputBorder.none,
                        isDense: true,
                        isCollapsed: true,
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: paddingExtraSmall,
                        ),
                        fillColor: Colors.transparent,
                      ),
                      onChanged: (value) {
                        // Use debounced search
                        controller.updateSearchQuery(value);
                      },
                      onSubmitted: (value) {
                        // Apply search immediately when Enter is pressed
                        controller.searchQuery.value = value;
                        controller.applyFilters();
                      },
                    ),
                  ),
                  // Clear button if search query exists
                  if (controller.searchQuery.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear, size: 16),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      onPressed: () {
                        controller.textEditingController.clear();
                        controller.updateSearchQuery('');
                      },
                    ),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder:
                    (context) => FilterMonthYear(
                      initialFilters: controller.activeFilters.value,
                    ),
              ).then((result) {
                if (result != null) {
                  if (result.toString().toLowerCase() == "reset") {
                    controller.resetFilters();
                  } else {
                    controller.activeFilters.value = result;
                    // Apply filters after setting the new values
                  }
                  controller.applyFilters();
                }
              });
            },
            borderRadius: BorderRadius.circular(radiusSmall),
            child: Container(
              height: 48,
              padding: const EdgeInsets.symmetric(horizontal: paddingSmall),
              decoration: BoxDecoration(
                color:
                    controller.activeFilters.value.isNotEmpty
                        ? const Color(0xFFE3F2FD)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(radiusSmall),
                border: Border.all(
                  color:
                      controller.activeFilters.value.isNotEmpty
                          ? const Color(0xFF1976D2)
                          : Colors.grey.shade300,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 20,
                    color:
                        controller.activeFilters.value.isNotEmpty
                            ? const Color(0xFF1976D2)
                            : Colors.grey.shade700,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'filter_str'.tr,
                    style: TextStyle(
                      color:
                          controller.activeFilters.value.isNotEmpty
                              ? const Color(0xFF1976D2)
                              : Colors.grey.shade700,
                      fontWeight:
                          controller.activeFilters.value.isNotEmpty
                              ? FontWeight.w500
                              : FontWeight.normal,
                    ),
                  ),
                  if (controller.activeFilters.value.containsKey('startDate') &&
                      controller.activeFilters.value.containsKey('endDate'))
                    Container(
                      margin: const EdgeInsets.only(left: 6),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1976D2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _formatDateRange(controller.activeFilters.value),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Format date range for display in filter badge
  String _formatDateRange(Map<String, dynamic> filters) {
    try {
      DateTime startDate;
      DateTime endDate;

      // Use the DateTime objects if available, otherwise parse from string
      if (filters.containsKey('startDateTime') &&
          filters.containsKey('endDateTime')) {
        startDate = filters['startDateTime'];
        endDate = filters['endDateTime'];
      } else {
        startDate = DateTime.parse(filters['startDate']);
        endDate = DateTime.parse(filters['endDate']);
      }

      // Use a simpler format that doesn't require locale initialization
      final dateFormat = DateFormat('MM/yy');
      return '${dateFormat.format(startDate)} - ${dateFormat.format(endDate)}';
    } catch (e) {
      return 'Tanggal';
    }
  }
}

class BirthdayCustomerItem extends StatelessWidget {
  final String name;
  final String date;
  final String initial;
  final String id;
  final String phoneNumber;
  final String emailAddress;
  final BirthdayPageController controller;

  const BirthdayCustomerItem({
    super.key,
    required this.name,
    required this.date,
    required this.initial,
    required this.id,
    required this.phoneNumber,
    required this.emailAddress,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingMedium,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: paddingSmall,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(date, style: const TextStyle(fontSize: 16)),
              Row(
                spacing: paddingMedium,
                children: [
                  Container(
                    width: 36,
                    height: 36,
                    decoration: const BoxDecoration(
                      color: Color(0xFFF0F0F0),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        initial,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  Text(
                    id,
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
        Container(
          margin: const EdgeInsets.only(top: 8),
          child: OutlinedButton(
            onPressed: () => controller.sendBirthdayGreeting(phoneNumber, name),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.blue),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radiusSmall),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: Text("kirim_ucapan".tr),
          ),
        ),
      ],
    );
  }
}
