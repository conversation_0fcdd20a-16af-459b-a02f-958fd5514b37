import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/custom_switch.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/controllers/profile/setting_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class SettingPage extends StatelessWidget {
  SettingPage({super.key});

  final SettingController controller = Get.put(SettingController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      backEnabled: true,
      onRefresh: () {},
      controller: controller,
      title: 'label_app_setting'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingLarge),
            Obx(
              () => _card(
                context,
                title: 'label_language'.tr,
                value:
                    controller.locale.value == 'id_ID'
                        ? 'Bahasa Indonesia'
                        : 'English',
                onTap: () {
                  controller.tempLocale.value = controller.locale.value;
                  _flagBottomSheet(context);
                },
              ),
            ),
            // Theme setting with toggle switch
            Obx(
              () => _toggleCard(
                context,
                title: 'label_theme'.tr,
                toggleWidget: CustomSwitch(
                  value: controller.themeIsDark.value,
                  onChanged: (val) => controller.toggleTheme(),
                ),
              ),
            ),
            _card(
              context,
              title: 'label_connected_device'.tr,
              value: '',
              onTap: () => Get.toNamed(Routes.CONNECTED_DEVICES),
            ),
            // Logging toggle
            Obx(
              () => CommonWidgets.profileContentCard(
                context,
                title: 'Debug Logging',
                suffix: CustomSwitch(
                  value: controller.loggingEnabled.value,
                  onChanged: (val) => controller.toggleLogging(),
                ),
              ),
            ),
            // FCM Notification Test
            _card(
              context,
              title: 'FCM Notification Test',
              value: '',
              onTap: () => Get.toNamed(Routes.NOTIFICATION_TEST),
            ),
            SizedBox(
              width: Get.width,
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'PaninDai-ichiLife ${'label_version'.tr}',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color:
                                (Get.isDarkMode
                                    ? kColorTextDark
                                    : kColorTextLight),
                          ),
                        ),
                        Text(
                          '${'label_version'.tr} : 1.2.0',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color:
                                (Get.isDarkMode
                                    ? kColorTextTersier
                                    : kColorTextTersierLight),
                          ),
                        ),
                      ],
                    ),
                  ),
                  FilledButton(
                    onPressed: () => controller.updateApps(),
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(
                        Colors.transparent,
                      ),
                      side: WidgetStateProperty.all(
                        BorderSide(color: Color(0xFF0C9DEB)),
                      ),
                      foregroundColor: WidgetStateProperty.all(
                        Color(0xFF0C9DEB),
                      ),
                      padding: WidgetStateProperty.all(
                        EdgeInsets.symmetric(horizontal: paddingSmall),
                      ),
                    ),
                    child: Text('label_update_apps'.tr),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _flagBottomSheet(BuildContext context) {
    PdlBottomSheet(
      title: 'label_language'.tr,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: paddingMedium),
          Obx(
            () => CommonWidgets.bottomSheetRadioOptionCard(
              context,
              title: 'Bahasa Indonesia',
              suffix: SvgPicture.asset(kFlagIdSquare),
              isSelected: controller.tempLocale.value == 'id_ID',
              onTap: () => controller.tempLocale.value = 'id_ID',
            ),
          ),
          Divider(),
          Obx(
            () => CommonWidgets.bottomSheetRadioOptionCard(
              context,
              title: 'English',
              suffix: SvgPicture.asset(kFlagUsSquare),
              isSelected: controller.tempLocale.value == 'en_US',
              onTap: () => controller.tempLocale.value = 'en_US',
            ),
          ),
          SizedBox(height: paddingExtraLarge),
          SizedBox(
            width: Get.width,
            child: FilledButton(
              onPressed: () => controller.setLocale(),
              child: Text('button_save'.tr),
            ),
          ),
        ],
      ),
    );
  }

  Widget _toggleCard(
    BuildContext context, {
    required String title,
    required Widget toggleWidget,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: paddingLarge),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
              ),
            ),
          ),
          toggleWidget,
        ],
      ),
    );
  }

  Widget _card(
    context, {
    required String title,
    required String value,
    required Function() onTap,
  }) {
    return CommonWidgets.profileContentCard(
      context,
      title: title,
      onPressed: onTap,
      suffix: Wrap(
        children: [
          Text(value),
          Container(
            padding: EdgeInsets.only(left: paddingSmall),
            child: Utils.cachedSvgWrapper(
              'icon/ic-linear-chevron right.svg',
              color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
              height: 24,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
