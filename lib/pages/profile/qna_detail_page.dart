import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/controllers/profile/qna_detail_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

class QnaDetailPage extends StatelessWidget {
  QnaDetailPage({super.key});

  final QnaDetailController controller = Get.put(QnaDetailController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => BaseDetailPage(
        backEnabled: true,
        controller: controller,
        onRefresh: () => controller.load(),
        title: 'label_qna'.tr,
        child: Container(
          width: Get.width,
          padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingMedium),
              Text(
                controller.questionData.value.question ?? '',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w700),
              ),
              SizedBox(height: paddingMedium),
              Text(
                controller.questionData.value.answer ?? '',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
