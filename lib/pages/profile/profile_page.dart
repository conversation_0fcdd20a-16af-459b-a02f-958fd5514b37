// ignore_for_file: deprecated_member_use

import 'dart:developer';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdl_superapp/components/custom_switch.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_showcase.dart';
import 'package:pdl_superapp/controllers/profile/profile_controller.dart';
import 'package:pdl_superapp/pages/sliver_wrapper.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/common_widgets.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:showcaseview/showcaseview.dart';

class ProfilePage extends StatelessWidget {
  ProfilePage({super.key});

  final ProfileController controller = Get.put(
    ProfileController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return isWideScreen
        ? RefreshIndicator(
          onRefresh: () async {
            controller.load();
            // Wait for loading to complete
            while (controller.isLoading.value) {
              await Future.delayed(Duration(milliseconds: 100));
            }
            return;
          },
          child: SingleChildScrollView(child: _body(context)),
        )
        : RefreshIndicator(
          onRefresh: () async {
            controller.load();
            // Wait for loading to complete
            while (controller.isLoading.value) {
              await Future.delayed(Duration(milliseconds: 100));
            }
            return;
          },
          child: Stack(
            children: [
              SliverWrapper(
                collapseContent: _collapseContent(),
                expandContent: _expandContent(context),
                widget: [_body(context)],
              ),
              Positioned(
                top: 70,
                left: 0,
                right: 0,
                child: IgnorePointer(
                  ignoring: true,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: paddingMedium,
                      vertical: paddingLarge,
                    ),
                    child: PdlShowcase(
                      globalKey: controller.tutorProfile[0].key,
                      title: controller.tutorProfile[0].title,
                      description: controller.tutorProfile[0].description,
                      number: controller.tutorProfile[0].number,
                      total: controller.tutorProfile.length,
                      child: Container(
                        width: Get.width,
                        height: Get.height / 4,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(radiusMedium),
                        ),
                        margin: EdgeInsets.all(paddingMedium),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
  }

  Container _body(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isWideScreen) SizedBox(height: 100),
            if (isWideScreen) _wideScreenHeader(context),
            _info(context),

            if (controller.isShowAgentMenu.value)
              PdlShowcase(
                globalKey: controller.tutorProfile[2].key,
                title: controller.tutorProfile[2].title,
                description: controller.tutorProfile[2].description,
                number: controller.tutorProfile[2].number,
                total: controller.tutorProfile.length,
                child: Column(
                  children: [
                    _title(
                      context,
                      text:
                          '${'label_lisence'.tr} (${controller.totalLicense.value}/${controller.allLisence.value})',
                    ),
                    _license(context),
                  ],
                ),
              ),

            SizedBox(
              width: Get.width,
              child: Wrap(
                alignment: WrapAlignment.spaceBetween,
                runAlignment: WrapAlignment.spaceBetween,
                children: [
                  PdlShowcase(
                    globalKey: controller.tutorProfile[3].key,
                    title: controller.tutorProfile[3].title,
                    description: controller.tutorProfile[3].description,
                    number: controller.tutorProfile[3].number,
                    total: controller.tutorProfile.length,
                    child: _menuWrapper(
                      context,
                      childern: [
                        _title(context, text: 'title_account'.tr),
                        _account(context),
                      ],
                    ),
                  ),

                  if ((controller.isShowAgentMenu.value))
                    PdlShowcase(
                      globalKey: controller.tutorProfile[4].key,
                      title: controller.tutorProfile[4].title,
                      description: controller.tutorProfile[4].description,
                      number: controller.tutorProfile[4].number,
                      total: controller.tutorProfile.length,
                      child: _menuWrapper(
                        context,
                        childern: [
                          _title(context, text: 'title_keagenan'.tr),
                          _keagenan(context),
                        ],
                      ),
                    ),
                  PdlShowcase(
                    globalKey:
                        controller
                            .tutorProfile[(controller.isShowAgentMenu.value)
                                ? 5
                                : 4]
                            .key,
                    title:
                        controller
                            .tutorProfile[(controller.isShowAgentMenu.value)
                                ? 5
                                : 4]
                            .title,
                    description:
                        controller
                            .tutorProfile[(controller.isShowAgentMenu.value)
                                ? 5
                                : 4]
                            .description,
                    number:
                        controller
                            .tutorProfile[(controller.isShowAgentMenu.value)
                                ? 5
                                : 4]
                            .number,
                    total: controller.tutorProfile.length,
                    tooltipPosition: TooltipPosition.top,
                    child: _menuWrapper(
                      context,
                      childern: [
                        _title(context, text: 'label_application'.tr),
                        _application(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: Get.width,
              padding: EdgeInsets.symmetric(vertical: paddingLarge),
              child: Text(
                '${'label_version'.tr} ${controller.fullVersion.value}',
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _wideScreenHeader(BuildContext context) {
    String? photoProfile = controller.userData.value.photo;
    return Obx(
      () => Padding(
        padding: const EdgeInsets.symmetric(vertical: paddingSmall),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 90,
              height: 90,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(90),
                color: kColorGlobalBgGreen,
              ),
              clipBehavior: Clip.antiAlias,
              child:
                  photoProfile != null && photoProfile != ''
                      ? CachedNetworkImage(
                        imageUrl: photoProfile,
                        fit: BoxFit.cover,
                        alignment: Alignment.center,
                      )
                      : Center(
                        child: Text(
                          Utils.getInitials(
                            controller.userData.value.agentName ?? '-',
                          ),
                          style: Theme.of(
                            context,
                          ).textTheme.headlineLarge?.copyWith(
                            color: kColorTextTersierLight,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
            ),
            SizedBox(height: paddingSmall),
            Text(
              controller.userData.value.agentName ?? '',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 20,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            SizedBox(height: paddingExtraSmall),
            Text(
              controller.userData.value.roleName ?? '',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _menuWrapper(BuildContext context, {required List<Widget> childern}) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: isWideScreen ? Get.width / 5.3 : Get.width,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: childern,
      ),
    );
  }

  Column _application(BuildContext context) => Column(
    children: [
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-Gear.svg',
        title: 'label_app_setting'.tr,
        onPressed: () => Get.toNamed(Routes.SETTING),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-clipboard-linear.svg',
        title: 'label_privacy'.tr,
        onPressed:
            () => Get.toNamed(
              Routes.READ_PAGE,
              arguments: {'type': kInfoTypeTnc},
            ),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-dialog.svg',
        title: 'label_qna'.tr,
        onPressed: () => Get.toNamed(Routes.QNA),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-notebook.svg',
        title: 'label_tutorial'.tr,
        onPressed: () => Get.toNamed(Routes.TUTORIAL),
      ),
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-logout.svg',
        title: 'button_logout'.tr,
        suffix: Container(),
        onPressed: () => controller.requestLogout(context),
        color: kColorError,
      ),
    ],
  );

  Column _keagenan(BuildContext context) => Column(
    children: [
      CommonWidgets.profileContentCard(
        context,
        iconUrl: 'icon/ic-linear-documents.svg',
        title: 'label_download_pdf'.tr,
        suffix: SizedBox(
          width: 40,
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-download.svg',
            color: Theme.of(context).primaryColor,
            height: 24,
            fit: BoxFit.contain,
          ),
        ),
        onPressed: () => controller.downloadContractPdf(),
        isLast: true,
      ),
    ],
  );

  Widget _account(BuildContext context) => Obx(
    () => Column(
      children: [
        _viewOptionBan(
          child: CommonWidgets.profileContentCard(
            context,
            iconUrl: 'icon/ic-linear-user-rounded.svg',
            title: 'label_my_profile'.tr,
            onPressed:
                () => Get.toNamed(
                  Routes.PROFILE_EDIT,
                  arguments: controller.userData.value,
                ),
          ),
        ),
        if (controller.userData.value.userType == 'AGENT' ||
            controller.userData.value.userType == null)
          _viewOptionBan(
            child: CommonWidgets.profileContentCard(
              context,
              iconUrl: 'icon/ic-linear-lock.svg',
              title: 'label_change_password'.tr,
              onPressed: () => Get.toNamed(Routes.CHANGE_PASSWORD),
            ),
          ),
        CommonWidgets.profileContentCard(
          context,
          iconUrl: 'icon/ic-linear-scan-user.svg',
          title: 'label_biometric'.tr,
          suffix: CustomSwitch(
            value: controller.isBioActive.value,
            onChanged: (val) => controller.setBiometric(),
          ),
          isLast: true,
        ),
      ],
    ),
  );

  Widget _info(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Center(
      child: SizedBox(
        width: isWideScreen ? Get.width / 4 : Get.width,
        child: _cardWrapper(
          context,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Obx(
                      () => Text(
                        controller.userData.value.agentCode ?? '-',
                        style: Theme.of(context).textTheme.labelMedium
                            ?.copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                    SizedBox(height: paddingExtraSmall),
                    Obx(
                      () => Text(
                        controller.userData.value.branchName == null
                            ? controller.userData.value.branches?.isNotEmpty ==
                                    true
                                ? "${controller.userData.value.branches?.first.branchCode} - ${controller.userData.value.branches?.first.branchName}"
                                : '-'
                            : "${controller.userData.value.branchCode} - ${controller.userData.value.branchName}",
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color:
                              Get.isDarkMode
                                  ? kColorTextTersier
                                  : kColorTextTersierLight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (!controller.userLevel.contains("SECRETARY"))
                if (controller.userData.value.userType != 'STAFF')
                  if (controller.userData.value.channel != 'BAN')
                    GestureDetector(
                      onTap:
                          () => PdlBaseDialog(
                            context: context,
                            title: 'qr_code_str'.tr,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 150,
                                  height: 150,
                                  padding: EdgeInsets.all(paddingSmall),
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.secondary,
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: QrImageView(
                                    data: controller.dataQrCode,
                                    dataModuleStyle: QrDataModuleStyle(
                                      dataModuleShape: QrDataModuleShape.square,
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurface,
                                    ),
                                    eyeStyle: QrEyeStyle(
                                      eyeShape: QrEyeShape.square,
                                      color:
                                          Theme.of(
                                            context,
                                          ).colorScheme.onSurface,
                                    ),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    vertical: paddingLarge,
                                  ),
                                  child: Text(
                                    controller.userData.value.agentName ?? "-",
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w700),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(top: paddingSmall),
                                  width: Get.width,
                                  child: Text(
                                    'qr_scan_desc_str'.tr,
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Container(
                                  width: Get.width,
                                  padding: EdgeInsets.only(top: paddingMedium),
                                  child: FilledButton(
                                    // onPressed: () => Get.toNamed(Routes.FIRST_LOGIN_SUCCESS),
                                    onPressed: () async {
                                      double size = 300;
                                      double padding = 20;
                                      final qrValidationResult =
                                          QrValidator.validate(
                                            data: controller.dataQrCode,
                                            version: QrVersions.auto,
                                            errorCorrectionLevel:
                                                QrErrorCorrectLevel.Q,
                                          );

                                      if (qrValidationResult.status !=
                                          QrValidationStatus.valid) {
                                        throw Exception('Invalid QR data');
                                      }

                                      final qrCode = qrValidationResult.qrCode;

                                      final painter = QrPainter.withQr(
                                        qr: qrCode!,
                                        color: const Color(0xFF000000),
                                        emptyColor: const Color(0xFFFFFFFF),
                                        gapless: true,
                                      );

                                      // Original QR code image
                                      final qrImage = await painter.toImage(
                                        size,
                                      );
                                      final qrBytes = await qrImage.toByteData(
                                        format: ui.ImageByteFormat.png,
                                      );

                                      // Create a new canvas with padding
                                      final paddedSize = size + 2 * padding;
                                      final recorder = ui.PictureRecorder();
                                      final canvas = Canvas(recorder);
                                      final paint =
                                          Paint()
                                            ..color = const Color(
                                              0xFFFFFFFF,
                                            ); // white background
                                      canvas.drawRect(
                                        Rect.fromLTWH(
                                          0,
                                          0,
                                          paddedSize.toDouble(),
                                          paddedSize.toDouble(),
                                        ),
                                        paint,
                                      );

                                      // Draw the QR code image centered on the new canvas
                                      final codec = await ui
                                          .instantiateImageCodec(
                                            qrBytes!.buffer.asUint8List(),
                                          );
                                      final frame = await codec.getNextFrame();
                                      canvas.drawImage(
                                        frame.image,
                                        Offset(
                                          padding.toDouble(),
                                          padding.toDouble(),
                                        ),
                                        Paint(),
                                      );

                                      // Final image with padding
                                      final finalImage = await recorder
                                          .endRecording()
                                          .toImage(
                                            paddedSize.toInt(),
                                            paddedSize.toInt(),
                                          );
                                      final finalBytes = await finalImage
                                          .toByteData(
                                            format: ui.ImageByteFormat.png,
                                          );

                                      final tempDir =
                                          await getTemporaryDirectory();
                                      final file = File(
                                        '${tempDir.path}/qr_code_padded.png',
                                      );
                                      await file.writeAsBytes(
                                        finalBytes!.buffer.asUint8List(),
                                      );
                                      final params = ShareParams(
                                        text: 'QR data',
                                        files: [XFile(file.path)],
                                      );

                                      // ignore: unused_local_variable
                                      final result = await SharePlus.instance
                                          .share(params);
                                    },
                                    style: FilledButton.styleFrom(
                                      disabledBackgroundColor:
                                          Get.isDarkMode
                                              ? kColorTextTersier
                                              : kColorBorderLight,
                                      disabledForegroundColor: Color(
                                        0xFF6D6D6D,
                                      ),
                                    ),
                                    child: Wrap(
                                      alignment: WrapAlignment.center,
                                      crossAxisAlignment:
                                          WrapCrossAlignment.center,
                                      children: [
                                        Text('share_str'.tr),
                                        SizedBox(width: paddingSmall),
                                        Utils.cachedSvgWrapper(
                                          'icon/ic-linear-share.svg',
                                          color: kColorBgLight,
                                          height: 20,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      child: PdlShowcase(
                        globalKey: controller.tutorProfile[1].key,
                        title: controller.tutorProfile[1].title,
                        description: controller.tutorProfile[1].description,
                        number: controller.tutorProfile[1].number,
                        total: controller.tutorProfile.length,
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                Get.isDarkMode
                                    ? Theme.of(context).colorScheme.surface
                                    : Theme.of(context).colorScheme.secondary,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color:
                                  Get.isDarkMode
                                      ? kColorBorderDark
                                      : kColorBorderLight,
                            ),
                          ),
                          padding: EdgeInsets.all(paddingSmall),
                          child: Wrap(
                            spacing: paddingSmall,
                            crossAxisAlignment: WrapCrossAlignment.center,
                            children: [
                              Text(
                                'qr_code_str'.tr,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w700),
                              ),
                              Utils.cachedSvgWrapper(
                                'icon/ic-linear-qr.svg',
                                color: Theme.of(context).primaryColor,
                                width: 24,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            ],
          ),
        ),
      ),
    );
  }

  Padding _title(BuildContext context, {required text}) {
    return Padding(
      padding: EdgeInsets.only(bottom: paddingMedium, top: paddingLarge),
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
      ),
    );
  }

  SizedBox _license(BuildContext context) {
    DateTime today = DateTime.now();
    return SizedBox(
      width: Get.width,
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        runAlignment: WrapAlignment.spaceBetween,
        runSpacing: paddingMedium,
        children: [
          if (!controller.userLevel.value.inList([
            kUserLevelABDD,
            kUserLevelBdm,
            kUserLevelBDD,
            kUserLevelHOS,
            kUserLevelCAO,
            kUserLevelARA,
            kUserLevelAgeBranch,
            kUserLevelSecreatryBd,
            kUserLevelSecreatryBm,
            kUserLevelSecreatryBp,
          ]))
            _lisenceCard(
              context,
              isCompleted: DateTime.parse(
                controller.userData.value.licenseExpiredDateAAJI ??
                    today.toString(),
              ).isAfter(today),
              title: 'AAJI',
              number: controller.userData.value.licenseNumberAAJI,
              expiredDate:
                  controller.userData.value.licenseExpiredDateAAJI?.formatDate(
                    format: "dd/MM/yyyy",
                  ) ??
                  "-",
            ),
          if (controller.userData.value.channel != kUserChannelBan)
            if (!controller.userLevel.value.inList([
              kUserLevelABDD,
              kUserLevelBdm,
              kUserLevelBDD,
              kUserLevelHOS,
              kUserLevelCAO,
              kUserLevelARA,
              kUserLevelAgeBranch,
              kUserLevelSecreatryBd,
              kUserLevelSecreatryBm,
              kUserLevelSecreatryBp,
            ]))
              _lisenceCard(
                context,
                isCompleted: DateTime.parse(
                  controller.userData.value.licenseExpiredDateAASI ??
                      today.toString(),
                ).isAfter(today),
                title: 'AASI',
                number: controller.userData.value.licenseNumberAASI,
                expiredDate:
                    controller.userData.value.licenseExpiredDateAASI
                        ?.formatDate(format: "dd/MM/yyyy") ??
                    "-",
              ),
        ],
      ),
    );
  }

  SizedBox _lisenceCard(
    BuildContext context, {
    required bool isCompleted,
    required String title,
    String? expiredDate,
    String? number,
  }) {
    final bool isWideScreen = kIsWeb;
    return SizedBox(
      width: isWideScreen ? Get.width / 3.45 : Get.width / 2.25,
      child: _cardWrapper(
        context,
        isCompleted: isCompleted,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
            ),
            SizedBox(height: paddingSmall),
            if (isCompleted == true)
              Text(
                number ?? '-',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w700,
                  color:
                      Get.isDarkMode
                          ? kColorTextTersier
                          : kColorTextTersierLight,
                ),
              ),
            SizedBox(height: paddingSmall),
            if (isCompleted == true)
              Text(
                'Exp $expiredDate',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w400),
              ),

            if (isCompleted == false)
              Container(
                decoration: BoxDecoration(
                  color: kColorGlobalBgWarning,
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: EdgeInsets.symmetric(
                  vertical: paddingExtraSmall,
                  horizontal: paddingSmall,
                ),
                child: Text(
                  'mandatory_str'.tr,
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: kColorGlobalWarning),
                ),
              ),
            SizedBox(height: paddingSmall),
            isCompleted == true
                ? Container(
                  margin: EdgeInsets.only(top: paddingExtraSmall),
                  decoration: BoxDecoration(
                    color: Color(0xFFF3FAF1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: paddingSmall,
                    horizontal: paddingMedium,
                  ),
                  child: Text(
                    'active_status_str'.tr,
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Color(0xFF126F3C),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
                : SizedBox(
                  width: Get.width,
                  child: FilledButton(
                    onPressed: () {},
                    child: Text('register_str'.tr),
                  ),
                ),
          ],
        ),
      ),
    );
  }

  Widget _cardWrapper(
    BuildContext context, {
    required Widget child,
    bool? isCompleted,
  }) {
    return Container(
      width: Get.width,

      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceTint,
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          if (isCompleted != null)
            Positioned(
              right: 0,
              child: Container(
                width: 38,
                height: 38,
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color:
                      isCompleted == true
                          ? kColorGlobalBgGreen
                          : kColorGlobalBgWarning,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(16),
                    topLeft: Radius.circular(30),
                    bottomLeft: Radius.circular(30),
                  ),
                ),
                child: Center(
                  child: Utils.cachedSvgWrapper(
                    isCompleted == true
                        ? 'icon/ic-bold-verified-check.svg'
                        : 'icon/ic-bold-Info.svg',
                    color:
                        isCompleted == true
                            ? kColorGlobalGreen
                            : kColorGlobalWarning,
                  ),
                ),
              ),
            ),
          Padding(padding: EdgeInsets.all(paddingMedium), child: child),
        ],
      ),
    );
  }

  Text _collapseContent() {
    return Text(
      "Profile",
      key: ValueKey("Collapsed"),
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
    );
  }

  Container _expandContent(BuildContext context) {
    RxString agentInitials = ''.obs;
    RxString agentName = '-'.obs;
    RxString agentLevel = '-'.obs;
    String? photoProfile = controller.userData.value.photo;

    switch (controller.userLevel.value) {
      case kUserLevelBp:
      case kUserLevelBm:
      case kUserLevelBd:
        agentName.value = controller.userData.value.agentName ?? '-';
        agentLevel.value = getDescription();
        break;
      case kUserLevelCAO:
        agentName.value = controller.userData.value.name ?? '-';
        agentLevel.value = controller.userData.value.roles?.name ?? '-';
      default:
        // BDM up selain CAO
        log('here');
        agentName.value =
            controller.userData.value.name ??
            controller.userData.value.agentName ??
            '-';
        agentLevel.value = controller.userData.value.roles?.name ?? '-';
    }
    if (photoProfile != null && photoProfile != '') {
      agentInitials.value = '';
    } else {
      agentInitials.value = Utils.getInitials(agentName.value);
    }
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(bottom: 40),
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 70,
            height: 70,
            child: CircleAvatar(
              radius: 70,
              backgroundColor: Theme.of(context).colorScheme.primary,
              backgroundImage:
                  photoProfile != null && photoProfile != ''
                      ? CachedNetworkImageProvider(photoProfile)
                      : null,
              child: Text(
                agentInitials.value,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ), // Replace with your image
            ),
          ),
          SizedBox(height: paddingSmall),
          Text(
            agentName.value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              fontSize: 20,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
          SizedBox(height: paddingExtraSmall),
          Text(
            agentLevel.value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  String getDescription() {
    switch (controller.userData.value.level) {
      case kLevelBM:
        return 'Business Manager';
      case kLevelBP:
        return "Business Partner";
      case kLevelBD:
        return "Business Developer";
      default:
        return '${controller.userData.value.level ?? '-'} - ${controller.userData.value.agentCode ?? '-'}';
    }
  }

  Widget _viewOptionBan({required Widget child}) {
    if (controller.userData.value.channel == 'BAN') {
      if (controller.userData.value.roles?.code == kUserLevelBanBO ||
          controller.userData.value.roles?.code == kUserLevelBanASM) {
        return child;
      }
      return SizedBox.shrink();
    } else {
      return child;
    }
  }
}
