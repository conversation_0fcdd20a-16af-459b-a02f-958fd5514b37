import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_base_dialog.dart';
import 'package:pdl_superapp/components/pdl_dialog_content.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/controllers/profile/device_controller.dart';
import 'package:pdl_superapp/models/device_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ConnectedDevicePage extends StatelessWidget {
  ConnectedDevicePage({super.key});

  final DeviceController controller = Get.put(DeviceController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      backEnabled: true,
      onRefresh: () => controller.getDeviceList(),
      controller: controller,
      title: 'label_connected_device'.tr,
      child: Container(
        width: Get.width,
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingLarge),
              TitleWidget(title: 'label_connected_device'.tr),
              SizedBox(height: paddingSmall),
              Text(
                'sub_title_connected_device'.tr,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              _subTitle(context, title: 'title_current_device'.tr),
              _cart(
                context,
                data: controller.activeDevices.value,
                iconUrl: 'icon/ic-linear-smartphone-2.svg',
                isPrimary: true,
              ),
              _subTitle(context, title: 'title_other_device'.tr),
              SizedBox(height: paddingExtraSmall),
              Text(
                'sub_title_other_device'.tr,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: paddingSmall),

              for (int i = 0; i < controller.listData.length; i++)
                _cart(
                  context,
                  data: controller.listData[i],
                  iconUrl: 'icon/ic-linear-global.svg',
                  isPrimary: false,
                ),
              SizedBox(height: paddingExtraLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _cart(
    BuildContext context, {
    required String iconUrl,
    required DeviceModels data,
    bool? isPrimary,
  }) {
    String activeString = controller.convertApiDateToHuman(
      data.lastLogin ?? DateTime.now().toString(),
    );
    activeString = 'title_last_used'.trParams({'month': activeString});
    if (isPrimary == true) {
      activeString = 'title_current_device'.tr;
    }
    return GestureDetector(
      onTap:
          isPrimary == true
              ? null
              : () {
                PdlBaseDialog(
                  context: context,
                  child: PdlDialogContent(
                    message: 'label_confirm_revoke'.trParams({
                      'device': data.deviceModel ?? '-',
                    }),
                    onTap:
                        () => controller.performRevokeDevice(
                          id: data.deviceId ?? '-',
                        ),
                  ),
                );
              },
      child: Container(
        width: Get.width,
        margin: EdgeInsets.only(top: paddingSmall, bottom: paddingExtraSmall),
        padding: EdgeInsets.all(paddingMedium),
        decoration: BoxDecoration(
          color:
              isPrimary == true
                  ? Theme.of(context).colorScheme.secondary
                  : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isPrimary == true
                    ? Colors.transparent
                    : (Get.isDarkMode
                        ? kColorGlobalBgDarkBlue
                        : kColorBorderLight),
          ),
          boxShadow: [
            BoxShadow(
              color: Color(0xFFe1e1e1).withValues(alpha: 0.04),
              blurRadius: 20, // Softness of the shadow
              spreadRadius: 0, // How much the shadow spreads
              offset: Offset(0, -10), // Shadow position
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 45,
              padding: EdgeInsets.all(10),
              height: 45,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(40),
              ),
              child: Utils.cachedSvgWrapper(
                iconUrl,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.deviceModel ?? '-',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: paddingExtraSmall),
                  Text(
                    activeString,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
            if (isPrimary == false)
              Container(
                color: Theme.of(context).colorScheme.surface,
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-login-2.svg',
                  color: kColorError,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _subTitle(BuildContext context, {required String title}) {
    return Padding(
      padding: const EdgeInsets.only(top: paddingMedium),
      child: Text(
        title,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w700,
          color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
        ),
      ),
    );
  }
}
