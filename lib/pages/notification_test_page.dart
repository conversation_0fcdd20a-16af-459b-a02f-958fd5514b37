import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/notification_service.dart';

/// Halaman untuk testing notifikasi FCM
/// Menampilkan FCM token dan status permission
class NotificationTestPage extends StatelessWidget {
  const NotificationTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Notification Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Notification Status',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Obx(() {
                        final notificationService =
                            NotificationService.instance;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  notificationService.isPermissionGranted
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color:
                                      notificationService.isPermissionGranted
                                          ? Colors.green
                                          : Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Permission: ${notificationService.isPermissionGranted ? "Granted" : "Denied"}',
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  notificationService.fcmToken.isNotEmpty
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color:
                                      notificationService.fcmToken.isNotEmpty
                                          ? Colors.green
                                          : Colors.red,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'FCM Token: ${notificationService.fcmToken.isNotEmpty ? "Available" : "Not Available"}',
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  notificationService.isFCMEnabled
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color:
                                      notificationService.isFCMEnabled
                                          ? Colors.green
                                          : Colors.orange,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'FCM Service: ${notificationService.isFCMEnabled ? "Enabled" : "Disabled"}',
                                ),
                              ],
                            ),
                          ],
                        );
                      }),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // FCM Token Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'FCM Token',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Obx(() {
                            final token = NotificationService.instance.fcmToken;
                            return IconButton(
                              onPressed:
                                  token.isNotEmpty
                                      ? () {
                                        Clipboard.setData(
                                          ClipboardData(text: token),
                                        );
                                        Get.snackbar(
                                          'Copied',
                                          'FCM Token copied to clipboard',
                                          snackPosition: SnackPosition.BOTTOM,
                                        );
                                      }
                                      : null,
                              icon: const Icon(Icons.copy),
                            );
                          }),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Obx(() {
                        final token = NotificationService.instance.fcmToken;
                        return Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child: Text(
                            token.isNotEmpty ? token : 'Token not available',
                            style: TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                              color:
                                  token.isNotEmpty ? Colors.black : Colors.grey,
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Topic Subscription Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Topic Subscription',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                NotificationService.instance.subscribeToTopic(
                                  'general',
                                );
                                Get.snackbar(
                                  'Subscribed',
                                  'Subscribed to "general" topic',
                                  snackPosition: SnackPosition.BOTTOM,
                                );
                              },
                              child: const Text('Subscribe to General'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                NotificationService.instance
                                    .unsubscribeFromTopic('general');
                                Get.snackbar(
                                  'Unsubscribed',
                                  'Unsubscribed from "general" topic',
                                  snackPosition: SnackPosition.BOTTOM,
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Unsubscribe'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Instructions Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Testing Instructions',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '1. Copy the FCM token above\n'
                        '2. Use Firebase Console or Postman to send test notification\n'
                        '3. Test different app states: foreground, background, terminated\n'
                        '4. Include payload data for navigation testing',
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                NotificationService.instance.retryGetToken();
                                Get.snackbar(
                                  'Retrying',
                                  'Attempting to get FCM token...',
                                  snackPosition: SnackPosition.BOTTOM,
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Retry Get Token'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                NotificationService.instance
                                    .clearAllNotifications();
                                Get.snackbar(
                                  'Cleared',
                                  'All notifications cleared',
                                  snackPosition: SnackPosition.BOTTOM,
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Clear Notifications'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // FCM Control Buttons
                      Obx(() {
                        final notificationService =
                            NotificationService.instance;
                        return Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                onPressed:
                                    notificationService.isFCMEnabled
                                        ? () {
                                          notificationService.disableFCM();
                                          Get.snackbar(
                                            'FCM Disabled',
                                            'Push notifications disabled',
                                            snackPosition: SnackPosition.BOTTOM,
                                          );
                                        }
                                        : () {
                                          notificationService.enableFCM();
                                          Get.snackbar(
                                            'FCM Enabled',
                                            'Attempting to enable push notifications...',
                                            snackPosition: SnackPosition.BOTTOM,
                                          );
                                        },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      notificationService.isFCMEnabled
                                          ? Colors.red
                                          : Colors.green,
                                  foregroundColor: Colors.white,
                                ),
                                child: Text(
                                  notificationService.isFCMEnabled
                                      ? 'Disable FCM'
                                      : 'Enable FCM',
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () async {
                                  bool isAvailable =
                                      await notificationService
                                          .checkFCMAvailability();
                                  Get.snackbar(
                                    'FCM Check',
                                    isAvailable
                                        ? 'FCM is available'
                                        : 'FCM is not available',
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor:
                                        isAvailable ? Colors.green : Colors.red,
                                    colorText: Colors.white,
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.purple,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('Check FCM'),
                              ),
                            ),
                          ],
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
