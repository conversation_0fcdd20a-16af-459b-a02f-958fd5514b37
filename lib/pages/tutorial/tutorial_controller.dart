import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TutorialController extends BaseControllers {
  RxString userLevel = ''.obs;
  late SharedPreferences prefs;

  @override
  void onInit() async {
    super.onInit();

    prefs = await SharedPreferences.getInstance();
    userLevel.value = prefs.getString(kStorageUserLevel) ?? '';
  }
}
