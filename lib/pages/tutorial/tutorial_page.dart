// ignore_for_file: unused_element_parameter

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/pages/tutorial/tutorial_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';

class TutorialPage extends StatelessWidget {
  TutorialPage({super.key});
  final controller = Get.put(TutorialController());

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'label_tutorial'.tr,
      backEnabled: true,
      controller: controller,
      onRefresh: () {},
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        child: SizedBox(
          width: double.infinity,
          child: Column(
            spacing: paddingMedium,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: paddingLarge),
              _ItemTutorial(
                text: 'menu_title_home'.tr,
                onTap:
                    () => _goToMainTutor(indexTab: 0, controller: controller),
              ),
              _ItemTutorial(
                text: 'menu_title_leaderboard'.tr,
                onTap:
                    () => _goToMainTutor(indexTab: 1, controller: controller),
              ),
              _ItemTutorial(
                text: 'menu_title_info'.tr,
                onTap:
                    () => _goToMainTutor(indexTab: 2, controller: controller),
              ),
              _ItemTutorial(
                text: 'menu_title_profile'.tr,
                onTap:
                    () => _goToMainTutor(indexTab: 3, controller: controller),
              ),
              _ItemTutorial(
                text: 'notification_str'.tr,
                onTap: () {
                  Get.toNamed(
                    Routes.NOTIFICATION_PAGE,
                    arguments: {'start_tutorial': true},
                  );
                },
              ),
              _ItemTutorial(text: 'my_task_str'.tr, onTap: () {}),
            ],
          ),
        ),
      ),
    );
  }

  void _goToMainTutor({
    required int indexTab,
    required TutorialController controller,
  }) {
    Get.offAllNamed(
      Routes.MAIN,
      arguments: {
        'index': indexTab,
        'start_tutorial': true,
        'level': controller.userLevel.value,
      },
    );
  }
}

class _ItemTutorial extends StatelessWidget {
  const _ItemTutorial({super.key, required this.text, required this.onTap});
  final String text;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(paddingSmall),
        child: Row(
          children: [
            Expanded(
              child: Text(text, style: Theme.of(context).textTheme.bodyMedium),
            ),
            Icon(Icons.keyboard_arrow_right_rounded),
          ],
        ),
      ),
    );
  }
}
