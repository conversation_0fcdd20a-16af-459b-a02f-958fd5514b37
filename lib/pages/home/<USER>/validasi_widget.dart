import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/custom_chip.dart';
import 'package:pdl_superapp/components/pdl_drop_down.dart';
import 'package:pdl_superapp/components/status_row.dart';
import 'package:pdl_superapp/components/table_card.dart';
import 'package:pdl_superapp/controllers/widget/validasi_widget_controller.dart';
import 'package:pdl_superapp/models/promosi_agent_model.dart';
import 'package:pdl_superapp/models/validasi_hirarki_model.dart';
import 'package:pdl_superapp/utils/extensions/num_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/widgets/gradient_progress_indicator.dart';
import '../../../utils/constants.dart';

class ValidasiWidget extends StatelessWidget {
  // Use GetX controller
  final ValidasiWidgetController controller = Get.put(
    ValidasiWidgetController(),
  );

  ValidasiWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        children: [
          // Show tabs for non-BD roles
          if (controller.level != kLevelBD) _buildTabSelector(context),

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector for non-BD roles
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        return Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToValidasi(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 0
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 0
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Validasi',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 0
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () => controller.switchToPromosiBM(),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50),
                    border:
                        controller.selectedSection.value == 1
                            ? Border.all(color: kColorBgLight)
                            : null,
                    color:
                        controller.selectedSection.value == 1
                            ? Theme.of(context).colorScheme.surface
                            : null,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    'Promosi ke ${controller.level == kLevelBP ? "BM" : "BD"}',
                    style: TextStyle(
                      fontWeight:
                          controller.selectedSection.value == 1
                              ? FontWeight.bold
                              : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BD role, show the dropdown content
    if (controller.level == kLevelBD) {
      return _buildValidasiContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      if (controller.selectedSection.value == 0) {
        return _buildValidasiContent();
      } else {
        return _buildPromosiBMContent();
      }
    });
  }

  // Validasi Content
  Widget _buildValidasiContent() {
    return Obx(() {
      // For BD role, use BD controller, otherwise use validasi controller
      final isBDRole = controller.level == kLevelBD;
      final isLoading =
          isBDRole
              ? controller.validasiBDController.isLoading.value
              : controller.validasiController.isLoading.value;
      final hasError =
          isBDRole
              ? controller.validasiBDController.hasError.value
              : controller.validasiController.hasError.value;
      final errorMessage =
          isBDRole
              ? controller.validasiBDController.errorMessage.value
              : controller.validasiController.errorMessage.value;
      final onRetry =
          isBDRole
              ? () => controller.validasiBDController.fetchValidasiData()
              : () => controller.validasiController.fetchValidasiData();

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show dropdown only for BD role
            if (isBDRole)
              PdlDropDown(
                disableSearch: true,
                title: '',
                item:
                    controller.validasiBDController.dropdownOptions.values
                        .toList(),
                selectedItem:
                    controller.validasiBDController.dropdownOptions[controller
                        .validasiBDController
                        .selectedOption
                        .value] ??
                    '',
                enabled: !isLoading,
                onChanged: (val) {
                  if (val != null) {
                    // Find the key for the selected value
                    final entry = controller
                        .validasiBDController
                        .dropdownOptions
                        .entries
                        .firstWhere(
                          (entry) => entry.value == val,
                          orElse: () => MapEntry('pribadi-1', val),
                        );
                    controller.validasiBDController.changeOption(entry.key);
                  }
                },
              ),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                  ],
                ),
              )
            else if (isBDRole &&
                controller.validasiBDController.validasiData.value != null)
              _buildBDContentBasedOnOption(
                controller.validasiBDController.validasiData.value!,
              )
            else if (isBDRole &&
                controller.validasiBDController.validasiData.value == null)
              const Center(child: Text('No data available'))
            else if (!isBDRole &&
                controller.validasiController.validasiData.value != null)
              _buildRegularContent(
                controller.validasiController.validasiData.value!,
              )
            else
              const Center(child: Text('No data available')),

            // Refresh Button
            if (!isLoading)
              Padding(
                padding: EdgeInsets.only(top: 16.0),
                child: Center(
                  child: TextButton(
                    onPressed: onRetry,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.refresh),
                        SizedBox(width: paddingSmall),
                        Text('refresh_str'.tr),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  // Promosi BM Content
  Widget _buildPromosiBMContent() {
    return Obx(() {
      // Handle loading and error states
      final isLoading = controller.promosiBMController.isLoading.value;
      final hasError = controller.promosiBMController.hasError.value;
      final errorMessage = controller.promosiBMController.errorMessage.value;
      void onRetry() => controller.promosiBMController.fetchPromosiAgentData();

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller
                .promosiBMController
                .promosiData
                .value
                .agentCode
                .isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildPromosiBMContentData(
                controller.promosiBMController.promosiData.value,
              ),

            // Refresh Button
            if (!isLoading)
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  // Promosi BM Content Data
  Widget _buildPromosiBMContentData(PromosiAgentModel data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(),
        // NET APE Pribadi
        _buildMetricCard(
          title: 'NET APE Pribadi',
          target: data.netApe.target.formatCurrencyId(),
          actual: data.netApe.aktual.formatCurrencyId(),
          kekurangan: data.netApe.kurang.formatCurrencyId(),
          progress: _calculateProgress(data.netApe.aktual, data.netApe.target),
          gradient: kGradientBlue,
        ),

        Divider(),
        // NET APE Group
        _buildMetricCard(
          title: 'NET APE Group',
          target: data.netApeGroup.target.formatCurrencyId(),
          actual: data.netApeGroup.aktual.formatCurrencyId(),
          kekurangan: data.netApeGroup.kurang.formatCurrencyId(),
          progress: _calculateProgress(
            data.netApeGroup.aktual,
            data.netApeGroup.target,
          ),
          gradient: kGradientGreen,
        ),

        Divider(),

        // BP Validasi
        TableCard(
          title: 'BP Validasi',
          headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
          rows: [
            [
              data.agentCount.target.toStringAsFixed(0),
              data.agentCount.aktual.toStringAsFixed(0),
              data.agentCount.kurang.toStringAsFixed(0),
            ],
          ],
          progress: _calculateProgress(
            data.agentCount.aktual,
            data.agentCount.target,
          ),
        ),

        const SizedBox(height: paddingMedium),

        // BP Baru Aktif
        TableCard(
          title: 'BP Baru Aktif',
          headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
          rows: [
            [
              data.newAgentCount.target.toStringAsFixed(0),
              data.newAgentCount.aktual.toStringAsFixed(0),
              data.newAgentCount.kurang.toStringAsFixed(0),
            ],
          ],
          progress: _calculateProgress(
            data.newAgentCount.aktual,
            data.newAgentCount.target,
          ),
        ),

        const SizedBox(height: paddingMedium),

        // Direct BM (leaderCount) - Only for BM role
        if (controller.level == kLevelBM && data.leaderCount != null)
          Column(
            children: [
              TableCard(
                title: 'Direct BM',
                headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
                rows: [
                  [
                    data.leaderCount!.target.toStringAsFixed(0),
                    data.leaderCount!.aktual.toStringAsFixed(0),
                    data.leaderCount!.kurang.toStringAsFixed(0),
                  ],
                ],
                progress: _calculateProgress(
                  data.leaderCount!.aktual,
                  data.leaderCount!.target,
                ),
              ),
              const SizedBox(height: paddingMedium),
            ],
          ),

        // Persistensi 13
        TableCard(
          title: 'Persistensi 13',
          headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
          rows: [
            [
              controller.promosiBMController.formatPercentage(
                data.persistensi.target,
              ),
              controller.promosiBMController.formatPercentage(
                data.persistensi.aktual,
              ),
              controller.promosiBMController.formatPercentage(
                data.persistensi.kurang,
              ),
            ],
          ],
          progress: _calculateProgress(
            data.persistensi.aktual,
            data.persistensi.target,
          ),
        ),

        const SizedBox(height: paddingMedium),

        // Status
        StatusRow(
          label: "Status",
          customWidget: CustomChip(
            type:
                data.status.toLowerCase() == 'tercapai'
                    ? ChipType.success
                    : ChipType.warning,
            text: data.status,
          ),
        ),

        const SizedBox(height: paddingMedium),

        // Pelatihan
        StatusRow(
          label: 'training_str'.tr,
          value: "${data.pelatihan.completed} dari ${data.pelatihan.total}",
        ),

        const SizedBox(height: paddingMedium),

        // ${'lisence_status_str'.tr}
        StatusRow(
          label: "${'lisence_status_str'.tr} AAJI",
          value: data.statusLisensiAAJI,
        ),

        const SizedBox(height: paddingSmall),

        StatusRow(
          label: "${'lisence_status_str'.tr} AASI",
          value: data.statusLisensiAASI,
        ),

        const SizedBox(height: paddingMedium),

        // Kekurangan
        if (data.kekurangan.isNotEmpty)
          StatusRow(
            label: "Kekurangan",
            value: data.kekurangan.map((item) => "- $item").join("\n"),
            isHorizontal: false,
          ),
      ],
    );
  }

  // Regular content for non-BD roles
  Widget _buildRegularContent(ValidasiHirarkiModel data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // NET POLIS
        if ([kLevelBP].contains(controller.level)) _validasiNetPolis(data),

        // NET APE
        if ([kLevelBD, kLevelBM].contains(controller.level))
          _buildMetricCard(
            title: 'NET APE Group',
            target: data.validasiPerHirarki.netApe.target.formatCurrencyId(),
            actual: data.validasiPerHirarki.netApe.aktual.formatCurrencyId(),
            kekurangan:
                (data.validasiPerHirarki.netApe.target -
                        data.validasiPerHirarki.netApe.aktual)
                    .formatCurrencyId(),
            progress: _calculateProgress(
              data.validasiPerHirarki.netApe.aktual,
              data.validasiPerHirarki.netApe.target,
            ),
            gradient: kGradientGreen,
          ),

        const SizedBox(height: paddingMedium),

        // BP Validasi
        if ([kLevelBD, kLevelBM].contains(controller.level))
          _validasiAgentCount(data),

        if ([kLevelBD, kLevelBM].contains(controller.level))
          const SizedBox(height: paddingMedium),

        // Persistensi
        if ([kLevelBP, kLevelBM].contains(controller.level))
          _validasiPersistensi(data),

        const SizedBox(height: paddingMedium),

        // Status
        _validasiStatus(data),

        const SizedBox(height: paddingMedium),

        // Pelatihan
        _valdasiPelatihan(data),

        const SizedBox(height: paddingMedium),

        // ${'lisence_status_str'.tr}
        _lisensiAAJI(data),

        const SizedBox(height: paddingSmall),

        _lisensiAASI(data),

        const SizedBox(height: paddingMedium),

        // Kekurangan
        _kekurangan(data),
      ],
    );
  }

  // Build content based on selected option for BD role
  Widget _buildBDContentBasedOnOption(ValidasiHirarkiModel data) {
    final option = controller.validasiBDController.selectedOption.value;
    List<Widget> col = [SizedBox.shrink()];

    switch (option) {
      case 'pribadi-1':
        col.addAll([
          // NET APE Group
          _buildMetricCard(
            title: 'NET APE Group',
            target: data.validasiPerHirarki.netApe.target.formatCurrencyId(),
            actual: data.validasiPerHirarki.netApe.aktual.formatCurrencyId(),
            kekurangan:
                (data.validasiPerHirarki.netApe.target -
                        data.validasiPerHirarki.netApe.aktual)
                    .formatCurrencyId(),

            progress: _calculateProgress(
              data.validasiPerHirarki.netApe.aktual,
              data.validasiPerHirarki.netApe.target,
            ),
            gradient: kGradientGreen,
          ),

          // Leader Count (BM)
          TableCard(
            title: '${'has_str'.tr} BM',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                data.validasiPerHirarki.agentCount.target.toStringAsFixed(0),
                data.validasiPerHirarki.agentCount.aktual.toStringAsFixed(0),
                data.validasiPerHirarki.agentCount.kurang.toStringAsFixed(0),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerHirarki.agentCount.aktual,
              data.validasiPerHirarki.agentCount.target,
            ),
          ),
        ]);
        break;
      case 'pribadi-2':
        col.addAll([
          // NET APE Group
          _buildMetricCard(
            title: 'NET APE Group',
            target: data.validasiPerHirarki.netApe.target.formatCurrencyId(),
            actual: data.validasiPerHirarki.netApe.aktual.formatCurrencyId(),
            kekurangan:
                (data.validasiPerHirarki.netApe.target -
                        data.validasiPerHirarki.netApe.aktual)
                    .formatCurrencyId(),

            progress: _calculateProgress(
              data.validasiPerHirarki.netApe.aktual,
              data.validasiPerHirarki.netApe.target,
            ),
            gradient: kGradientGreen,
          ),

          // Leader Count (BM)
          TableCard(
            title: '${'has_str'.tr} BP',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                data.validasiPerHirarki.leaderCount.target.toStringAsFixed(0),
                data.validasiPerHirarki.leaderCount.aktual.toStringAsFixed(0),
                data.validasiPerHirarki.leaderCount.kurang.toStringAsFixed(0),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerHirarki.leaderCount.aktual,
              data.validasiPerHirarki.leaderCount.target,
            ),
          ),
        ]);
        break;

      case 'G1':
        col.addAll([
          // NET APE Group
          _buildMetricCard(
            title: 'NET APE Group',
            target: (data.validasiPerG1?.netApe.target ?? 0).formatCurrencyId(),
            actual: (data.validasiPerG1?.netApe.aktual ?? 0).formatCurrencyId(),
            kekurangan:
                (data.validasiPerG1?.netApe.kurang ?? 0).formatCurrencyId(),
            // kekurangan: controller.validasiBDController.formatCurrency(
            //   (data.validasiPerG1?.netApe.target ?? 0) -
            //       (data.validasiPerG1?.netApe.aktual ?? 0),
            // ),
            progress: _calculateProgress(
              data.validasiPerG1?.netApe.aktual ?? 0,
              data.validasiPerG1?.netApe.target ?? 0,
            ),
            gradient: kGradientGreen,
          ),

          // NET APE G1
          _buildMetricCard(
            title: 'NET APE G1',
            target:
                (data.validasiPerG1?.netApeG1?.target ?? 0).formatCurrencyId(),
            actual:
                (data.validasiPerG1?.netApeG1?.aktual ?? 0).formatCurrencyId(),
            kekurangan:
                (data.validasiPerG1?.netApeG1?.kurang ?? 0).formatCurrencyId(),
            // kekurangan:
            //     ((data.validasiPerG1?.netApeG1?.target ?? 0) -
            //             (data.validasiPerG1?.netApeG1?.aktual ?? 0))
            //         .formatCurrencyId(),
            progress: _calculateProgress(
              data.validasiPerG1?.netApeG1?.aktual ?? 0,
              data.validasiPerG1?.netApeG1?.target ?? 0,
            ),
            gradient: kGradientBlue,
          ),

          // G1 Count
          TableCard(
            title: '${'has_str'.tr} G1',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                (data.validasiPerG1?.g1Count?.target ?? 0).toStringAsFixed(0),
                (data.validasiPerG1?.g1Count?.aktual ?? 0).toStringAsFixed(0),
                (data.validasiPerG1?.g1Count?.kurang ?? 0).toStringAsFixed(0),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerG1?.g1Count?.aktual ?? 0,
              data.validasiPerG1?.g1Count?.target ?? 0,
            ),
          ),

          // Persistensi
          TableCard(
            title: 'Persistensi 13',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.target ?? 0,
                ),
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.aktual ?? 0,
                ),
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.kurang ?? 0,
                ),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerG1?.persistensi.aktual ?? 0,
              data.validasiPerG1?.persistensi.target ?? 0,
            ),
          ),
        ]);
        break;

      case 'GAll':
        col.addAll([
          // NET APE All Generations
          _buildMetricCard(
            title: 'NET APE Semua Generasi',
            target:
                (data.validasiPerG1?.gnetApe?.target ?? 0).formatCurrencyId(),
            actual:
                (data.validasiPerG1?.gnetApe?.aktual ?? 0).formatCurrencyId(),
            kekurangan:
                (data.validasiPerG1?.gnetApe?.kurang ?? 0).formatCurrencyId(),
            progress: _calculateProgress(
              data.validasiPerG1?.gnetApe?.aktual ?? 0,
              data.validasiPerG1?.gnetApe?.target ?? 0,
            ),
            gradient: kGradientOrange,
          ),

          // G1 Count
          TableCard(
            title: '${'has_str'.tr} G1',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                (data.validasiPerG1?.g1Count?.target ?? 0).toStringAsFixed(0),
                (data.validasiPerG1?.g1Count?.aktual ?? 0).toStringAsFixed(0),
                (data.validasiPerG1?.g1Count?.kurang ?? 0).toStringAsFixed(0),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerG1?.g1Count?.aktual ?? 0,
              data.validasiPerG1?.g1Count?.target ?? 0,
            ),
          ),

          // Persistensi
          TableCard(
            title: 'Persistensi 13',
            headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
            rows: [
              [
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.target ?? 0,
                ),
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.aktual ?? 0,
                ),
                controller.validasiBDController.formatPercentage(
                  data.validasiPerG1?.persistensi.kurang ?? 0,
                ),
              ],
            ],
            progress: _calculateProgress(
              data.validasiPerG1?.persistensi.aktual ?? 0,
              data.validasiPerG1?.persistensi.target ?? 0,
            ),
          ),
        ]);

      default:
        return const Center(child: Text('Invalid option selected'));
    }

    col.addAll([
      _validasiStatus(data),
      _valdasiPelatihan(data),
      _lisensiAAJI(data),
      _lisensiAASI(data),
      _kekurangan(data),
    ]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: paddingMedium,
      children: col,
    );
  }

  // Helper method to build metric cards for Promosi BM section
  Widget _buildMetricCard({
    required String title,
    required String target,
    required String actual,
    required String kekurangan,
    required double progress,
    required Gradient gradient,
  }) {
    // Ensure progress is a valid value
    progress = _validateProgress(progress);
    return Container(
      padding: const EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.transparent : Colors.white,
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(
          color: Get.isDarkMode ? Colors.transparent : Colors.grey.shade300,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: paddingSmall,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          Row(
            children: [
              const Text('Target', textAlign: TextAlign.end),
              Spacer(),
              Text(target, style: TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
          GradientLinearProgressIndicator(value: progress, gradient: gradient),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: paddingSmall,
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              Text('actual_str'.tr, textAlign: TextAlign.end),
              Spacer(),
              Text(actual, style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: paddingSmall,
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color:
                      Get.isDarkMode
                          ? kColorGlobalBgDarkBlue
                          : Colors.grey[200],
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              Text('shortage_str'.tr, textAlign: TextAlign.end),
              Spacer(),
              Text(
                kekurangan,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _kekurangan(ValidasiHirarkiModel data) {
    if (data.kekurangan.isEmpty) return const SizedBox.shrink();
    return StatusRow(
      label: 'shortage_str'.tr,
      value: data.kekurangan.map((item) => "- $item").join("\n"),
      isHorizontal: false,
    );
  }

  Widget _lisensiAASI(ValidasiHirarkiModel data) => StatusRow(
    label: "${'lisence_status_str'.tr} AASI",
    value: data.statusLisensiAASI,
  );

  Widget _lisensiAAJI(ValidasiHirarkiModel data) => StatusRow(
    label: "${'lisence_status_str'.tr} AAJI",
    value: data.statusLisensiAAJI,
  );

  Widget _valdasiPelatihan(ValidasiHirarkiModel data) {
    if (controller.validasiBDController.selectedOption.value == 'G1' ||
        controller.validasiBDController.selectedOption.value == 'GAll') {
      return StatusRow(
        label: 'training_str'.tr,
        value:
            "${data.validasiPerG1?.pelatihan.completed} dari ${data.validasiPerG1?.pelatihan.total}",
      );
    }

    return StatusRow(
      label: 'training_str'.tr,
      value:
          "${data.validasiPerHirarki.pelatihan.completed} dari ${data.validasiPerHirarki.pelatihan.total}",
    );
  }

  Widget _validasiStatus(ValidasiHirarkiModel data) {
    if (data.validasiPerG1 != null) {
      return StatusRow(
        label: "Status",
        customWidget: CustomChip(
          type:
              data.validasiPerG1!.status.toLowerCase() == 'tercapai'
                  ? ChipType.success
                  : ChipType.warning,
          text: data.validasiPerG1!.status,
        ),
      );
    }

    return StatusRow(
      label: "Status",
      customWidget: CustomChip(
        type:
            data.validasiPerHirarki.status.toLowerCase() == 'tercapai'
                ? ChipType.success
                : ChipType.targetless,
        text: data.validasiPerHirarki.status,
      ),
    );
  }

  Widget _validasiPersistensi(ValidasiHirarkiModel data) {
    return TableCard(
      title: 'Persistensi 13',
      headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
      rows: [
        [
          controller.validasiController.formatPercentage(
            data.validasiPerHirarki.persistensi.target,
          ),
          controller.validasiController.formatPercentage(
            data.validasiPerHirarki.persistensi.aktual,
          ),
          controller.validasiController.formatPercentage(
            data.validasiPerHirarki.persistensi.kurang,
          ),
        ],
      ],
      progress: _calculateProgress(
        data.validasiPerHirarki.persistensi.aktual,
        data.validasiPerHirarki.persistensi.target,
      ),
    );
  }

  Widget _validasiAgentCount(ValidasiHirarkiModel data) {
    return TableCard(
      title: 'BP Validasi',
      headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
      rows: [
        [
          data.validasiPerHirarki.agentCount.target.toStringAsFixed(0),
          data.validasiPerHirarki.agentCount.aktual.toStringAsFixed(0),
          data.validasiPerHirarki.agentCount.kurang.toStringAsFixed(0),
        ],
      ],
      progress: _calculateProgress(
        data.validasiPerHirarki.agentCount.aktual,
        data.validasiPerHirarki.agentCount.target,
      ),
    );
  }

  Widget _validasiNetPolis(ValidasiHirarkiModel data) {
    return TableCard(
      title: 'NET Polis',
      headers: ['Target', 'actual_str'.tr, 'insufficient_str'.tr],
      rows: [
        [
          data.validasiPerHirarki.netPolis.target.toStringAsFixed(0),
          data.validasiPerHirarki.netPolis.aktual.toStringAsFixed(0),
          data.validasiPerHirarki.netPolis.kurang.toStringAsFixed(0),
        ],
      ],
      progress: _calculateProgress(
        data.validasiPerHirarki.netPolis.aktual,
        data.validasiPerHirarki.netPolis.target,
      ),
    );
  }

  // Helper method to validate progress value
  double _validateProgress(double value) {
    // Handle NaN
    if (value.isNaN) {
      return 0.0;
    }

    // Clamp between 0.0 and 1.0
    if (value > 1.0) {
      return 1.0;
    } else if (value < 0.0) {
      return 0.0;
    }

    return value;
  }

  // Helper method to calculate progress
  double _calculateProgress(double actual, double target) {
    if (target <= 0) return 0.0;
    return _validateProgress(actual / target);
  }
}
