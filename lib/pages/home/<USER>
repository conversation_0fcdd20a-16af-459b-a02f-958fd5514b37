import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/components/filter_button.dart';
import 'package:pdl_superapp/components/persistensi_filter.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/widget/persistensi_widget_controller.dart';
import 'package:pdl_superapp/models/persistensi_model.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

import '../../routes/app_routes.dart';

class PersistensiWidget extends StatelessWidget {
  // Use GetX controller
  final PersistensiWidgetController controller = Get.put(
    PersistensiWidgetController(),
  );

  PersistensiWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading.isTrue) {
        return const Center(child: CircularProgressIndicator());
      }
      return Column(
        children: [
          // Always show tabs for BAN channel, role-based for AGE
          if (controller.channel == kUserChannelBan) ...[
            // BAN channel - always show tabs
            _buildTabSelector(context),
            SizedBox(height: paddingSmall),
            // Search and filter
            _buildSearchAndFilter(),
          ] else ...[
            // AGE channel - existing logic
            if (controller.level != kLevelBP)
              if (controller.userType != 'STAFF') _buildTabSelector(context),
          ],

          // Content based on role and selected tab
          _buildContent(),
        ],
      );
    });
  }

  // Tab selector based on channel and role
  Widget _buildTabSelector(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: paddingSmall,
        vertical: paddingSmall,
      ),
      height: 50,
      child: Obx(() {
        // Get available tabs based on channel and role
        List<String> tabs = [];
        if (controller.channel == kUserChannelBan) {
          if (controller.level == kUserLevelBanBO) {
            tabs = ['my_self_str'.tr, 'team_str'.tr];
          } else {
            tabs = ['my_self_str'.tr, 'team_str'.tr, 'cabang_str'.tr];
          }
        } else {
          // AGE channel - existing logic
          if (controller.level == kLevelBP) {
            tabs = ['my_self_str'.tr];
          } else {
            tabs = [
              'my_self_str'.tr,
              controller.level == kLevelBD ? 'group_str'.tr : 'team_str'.tr,
            ];
          }
        }

        return Row(
          children:
              tabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tabName = entry.value;
                final isSelected = controller.selectedSection.value == index;

                return Expanded(
                  child: InkWell(
                    onTap: () {
                      switch (index) {
                        case 0:
                          controller.switchToIndividu();
                          break;
                        case 1:
                          controller.switchToTeam();
                          break;
                        case 2:
                          controller.switchToArea();
                          break;
                      }
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(50),
                        border:
                            isSelected
                                ? Border.all(color: kColorBgLight)
                                : null,
                        color:
                            isSelected
                                ? Theme.of(context).colorScheme.surface
                                : null,
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        tabName,
                        style: TextStyle(
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
        );
      }),
    );
  }

  // Search and filter for BAN channel
  Widget _buildSearchAndFilter() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: PdlTextField(
              hint: 'search_str'.tr,
              prefixIcon: Icon(Icons.search),
              enabled: true,
              onChanged: (value) {
                // TODO: Implement search functionality
              },
            ),
          ),
          SizedBox(width: paddingSmall),
          FilterButton(
            content: PersistensiFilter(
              selectedView: controller.selectedView.value,
              availableViews: controller.getAvailableViews(),
              branches: controller.availableBranches,
              selectedBranches: controller.selectedBranches,
              onViewChanged: (view) => controller.selectedView.value = view,
              onBranchesChanged:
                  (branches) => controller.selectedBranches.assignAll(branches),
              onReset: () => controller.resetFilter(),
              onApply:
                  () => controller.applyFilter(
                    controller.selectedView.value,
                    controller.selectedBranches,
                  ),
            ),
            title: 'filter_str'.tr,
          ),
        ],
      ),
    );
  }

  // Content based on role and selected tab
  Widget _buildContent() {
    // For BP role (AGE channel), show only individu content
    if (controller.channel != kUserChannelBan && controller.level == kLevelBP) {
      return _buildIndividuContent();
    }

    // For other roles, show content based on selected tab
    return Obx(() {
      switch (controller.selectedSection.value) {
        case 0:
          return _buildIndividuContent();
        case 1:
          return _buildTeamContent();
        case 2:
          return _buildAreaContent();
        default:
          return _buildIndividuContent();
      }
    });
  }

  // Individu Content
  Widget _buildIndividuContent() {
    return Obx(() {
      final isLoading = controller.individuController.isLoading.value;
      final hasError = controller.individuController.hasError.value;
      final errorMessage = controller.individuController.errorMessage.value;

      void onRetry() {
        controller.individuController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                  ],
                ),
              )
            else if (controller.individuController.persistensiData.value ==
                null)
              const Center(child: Text('No data available'))
            else ...[
              _buildPersistencyData(
                controller.individuController.persistensiData.value!,
              ),
              if (controller.level != kLevelBP) SizedBox(height: paddingSmall),
              if (controller.level != kLevelBP)
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=0');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
            ],

            // Refresh Button
            if (!isLoading) ...[
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Team Content
  Widget _buildTeamContent() {
    return Obx(() {
      final isLoading = controller.teamController.isLoading.value;
      final hasError = controller.teamController.hasError.value;
      final errorMessage = controller.teamController.errorMessage.value;

      void onRetry() {
        controller.teamController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.teamController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAgentPersistencyTables(),

            // Refresh Button
            if (!isLoading) ...[
              if (!controller.showFullData.value &&
                  controller.teamController.persistensiDataList.isNotEmpty &&
                  controller.teamController.persistensiDataList.length > 3) ...[
                SizedBox(height: paddingSmall),
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=1');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
                SizedBox(height: paddingSmall),
              ],
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Build persistency data display
  Widget _buildPersistencyData(PersistensiModel data) {
    return Column(
      spacing: paddingMedium,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildPersistenceRow(
          'Persistensi-13',
          controller.individuController.formatPercentage(data.persistency13),
        ),
        _buildPersistenceRow(
          'Persistensi-25',
          controller.individuController.formatPercentage(data.persistency25),
        ),
        _buildPersistenceRow(
          'Persistensi-37',
          controller.individuController.formatPercentage(data.persistency37),
        ),
        _buildPersistenceRow(
          'Persistensi-49',
          controller.individuController.formatPercentage(data.persistency49),
        ),
        _buildPersistenceRow(
          'Persistensi-61',
          controller.individuController.formatPercentage(data.persistency61),
        ),
      ],
    );
  }

  // Build single table for team/group persistency data
  Widget _buildAgentPersistencyTables() {
    final agents =
        (!controller.showFullData.value &&
                    controller.teamController.persistensiDataList.length > 3
                ? controller.teamController.persistensiDataList.take(3)
                : controller.teamController.persistensiDataList)
            .toList();

    if (agents.isEmpty) {
      return Container();
    }

    return _buildClickableTable(
      headers: ["name_str".tr, "P-13", "P-25", "P-37", "P-49", "P-61"],
      agents: agents,
      isTeamData: true,
    );
  }

  // Area Content (for Cabang view)
  Widget _buildAreaContent() {
    return Obx(() {
      final isLoading = controller.areaController.isLoading.value;
      final hasError = controller.areaController.hasError.value;
      final errorMessage = controller.areaController.errorMessage.value;

      void onRetry() {
        controller.areaController.fetchPersistensiData();
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Show loading or error state
            if (isLoading)
              const Center(child: CircularProgressIndicator())
            else if (hasError)
              Center(
                child: Column(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.red,
                    ),
                    const SizedBox(height: paddingSmall),
                    Text('Error: $errorMessage'),
                    const SizedBox(height: paddingMedium),
                    ElevatedButton(
                      onPressed: onRetry,
                      child: Text('retry_str'.tr),
                    ),
                  ],
                ),
              )
            else if (controller.areaController.persistensiDataList.isEmpty)
              const Center(child: Text('No data available'))
            else
              _buildAreaPersistencyTables(),

            // Refresh Button
            if (!isLoading) ...[
              if (!controller.showFullData.value &&
                  controller.areaController.persistensiDataList.isNotEmpty &&
                  controller.areaController.persistensiDataList.length > 3) ...[
                SizedBox(height: paddingSmall),
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    onPressed: () {
                      Get.toNamed('${Routes.PERSISTENSI}?mode=2');
                    },
                    title: "show_details_str".tr,
                  ),
                ),
                SizedBox(height: paddingSmall),
              ],
              Center(
                child: TextButton(
                  onPressed: onRetry,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: paddingSmall),
                      Text('refresh_str'.tr),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Build single table for area persistency data
  Widget _buildAreaPersistencyTables() {
    final areas =
        (!controller.showFullData.value &&
                    controller.areaController.persistensiDataList.length > 3
                ? controller.areaController.persistensiDataList.take(3)
                : controller.areaController.persistensiDataList)
            .toList();

    if (areas.isEmpty) {
      return Container();
    }

    return _buildClickableTable(
      headers: ["name_str".tr, "P-13", "P-25", "P-37"],
      agents: areas,
      isTeamData: false,
    );
  }

  // Build clickable table with name as first column
  Widget _buildClickableTable({
    required List<String> headers,
    required List<PersistensiModel> agents,
    required bool isTeamData,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: paddingMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radiusSmall),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Header row
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0075BD),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(radiusSmall),
                topRight: Radius.circular(radiusSmall),
              ),
            ),
            child: Row(
              children:
                  headers.map((header) {
                    return Expanded(
                      flex:
                          header == headers.first ? 2 : 1, // Name column wider
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          header,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),

          // Data rows
          ...agents.map((agent) {
            if (agent.name == '') return Container();

            return Container(
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  // Name column (clickable)
                  Expanded(
                    flex: 2,
                    child: InkWell(
                      onTap: () => _navigateToAgentDetail(agent, isTeamData),
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          agent.name,
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                    ),
                  ),

                  // Persistency columns
                  if (isTeamData) ...[
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency49,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.teamController.formatPercentage(
                            agent.persistency61,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ] else ...[
                    // Area data (only 3 columns)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency13,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency25,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(paddingSmall),
                        child: Text(
                          controller.areaController.formatPercentage(
                            agent.persistency37,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  // Navigate to agent detail page
  void _navigateToAgentDetail(PersistensiModel agent, bool isTeamData) {
    // Determine mode based on data type
    int mode = isTeamData ? 1 : 2; // 1 for team, 2 for area/cabang
    Get.toNamed(
      '${Routes.PERSISTENSI}?mode=$mode&agentCode=${agent.agentCode}',
    );
  }

  Widget _buildPersistenceRow(String title, String percentage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ],
    );
  }
}
