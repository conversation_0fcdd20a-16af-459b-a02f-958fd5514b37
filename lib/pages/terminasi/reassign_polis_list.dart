import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/terminasi/reassign_polis_list_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ReassignPolisList extends StatelessWidget {
  ReassignPolisList({super.key});

  final ReassignPolisListController controller = Get.put(
    ReassignPolisListController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'title_polis_assignment_transfer_list'.tr,
      controller: controller,
      onRefresh: () async => controller.refreshData(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingLarge),
            PdlTextField(
              hint: 'hint_search_agent_name_code'.tr,
              prefixIcon: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-search -2.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),
            SizedBox(height: paddingSmall),
            Obx(
              () => ListView.separated(
                shrinkWrap: true,
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.only(top: paddingMedium, bottom: 18),
                separatorBuilder:
                    (context, index) => Divider(color: kColorBorderLight),
                itemCount: controller.teamTerminasiList.length,
                itemBuilder:
                    (context, index) => InkWell(
                      onTap:
                          () => Get.toNamed(
                            Routes.REASSIGN_POLIS_PAGE,
                            arguments: {
                              kArgsTerminationData:
                                  controller.teamTerminasiList[index],
                            },
                          ),
                      child: _AgentItem(
                        agentCode:
                            controller
                                .teamTerminasiList[index]
                                .target
                                ?.agentCode ??
                            '',
                        agentLevel:
                            controller
                                .teamTerminasiList[index]
                                .target
                                ?.agentLevel ??
                            '',
                        channel:
                            controller
                                .teamTerminasiList[index]
                                .target
                                ?.channel ??
                            '',
                        agentName:
                            controller.teamTerminasiList[index].target?.name ??
                            '',
                        statusText:
                            controller.teamTerminasiList[index].status ?? '',
                      ),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AgentItem extends StatelessWidget {
  final String agentCode;
  final String agentName;
  final String channel;
  final String agentLevel;
  final String statusText;
  const _AgentItem({
    required this.agentCode,
    required this.agentName,
    required this.channel,
    required this.agentLevel,
    required this.statusText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(width: 44, height: 44, child: CircleAvatar()),
        SizedBox(width: paddingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Terminasi',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorTextTersierLight,
                ),
              ),
              SizedBox(height: paddingSmall),
              Text(
                agentName,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: paddingSmall),
              Text(
                '$agentLevel $channel - $agentCode',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorTextTersierLight,
                ),
              ),
              SizedBox(height: paddingSmall),
              Container(
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(50),
                  color: Color(0xFFF0F8FF),
                ),
                child: Text(
                  statusText,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorGlobalBlue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
