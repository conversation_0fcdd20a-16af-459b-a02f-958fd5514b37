import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/terminasi_bp_item.dart';
import 'package:pdl_superapp/controllers/terminasi/terminasi_team_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiSelectTeam extends StatefulWidget {
  // TerminasiTeamController
  const TerminasiSelectTeam({super.key});

  @override
  State<TerminasiSelectTeam> createState() => _TerminasiSelectTeamPageState();
}

class _TerminasiSelectTeamPageState extends State<TerminasiSelectTeam> {
  final TerminasiTeamController controller = Get.put(
    TerminasiTeamController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    controller.getTerminasiCandidateList();

    return Stack(
      children: [
        BaseDetailPage(
          title: 'title_bp_termination'.tr,
          controller: controller,
          onRefresh: () async => controller.refreshData(),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
            width: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: paddingLarge),
                  Text(
                    'title_select_bp_termination'.tr,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: paddingMedium),
                  PdlTextField(
                    hint: 'hint_search_agent_name_code'.tr,
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(paddingMedium),
                      child: Utils.cachedSvgWrapper(
                        'icon/ic-linear-search -2.svg',
                        color:
                            Get.isDarkMode ? kColorTextDark : kColorTextLight,
                      ),
                    ),
                  ),
                  Obx(
                    () => _listTerminasiBp(
                      context,
                      length: controller.terminasiCandidateList.length,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _listTerminasiBp(BuildContext context, {required int length}) {
    return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: length,
      separatorBuilder: (context, index) => Divider(color: kColorBorderLight),
      itemBuilder: (context, index) {
        var agentName =
            controller.terminasiCandidateList[index].agentName ?? '';
        var agentCode =
            controller.terminasiCandidateList[index].agentCode ?? '';
        var level = controller.terminasiCandidateList[index].level ?? '';
        return TerminasiBpItem(
          title: agentName,
          description: '$level - $agentCode',
          onTap: () {
            Get.toNamed(
              Routes.TERMINASI,
              arguments: {
                kArgsSelfTermination: false,
                kArgsTerminationData: controller.terminasiCandidateList[index],
              },
            );
          },
          iconUrl: 'icon/ic-menu-sub-rekrut.svg',
        );
      },
    );
  }
}
