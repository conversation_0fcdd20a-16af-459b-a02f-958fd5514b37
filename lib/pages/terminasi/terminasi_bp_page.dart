import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/terminasi/terminasi_bp_controller.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class TerminasiBpPage extends StatelessWidget {
  TerminasiBpPage({super.key});

  final TerminasiBpController controller = Get.put(
    TerminasiBpController(),
    tag: Utils.getRandomString(),
  );

  @override
  Widget build(BuildContext context) {
    return BaseDetailPage(
      title: 'title_bp_termination'.tr,
      controller: controller,
      onRefresh: () async => controller.refreshData(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: paddingMedium),
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: paddingLarge),
            Text(
              'select_bp_termination'.tr,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: paddingLarge),
            PdlTextField(
              hint: 'hint_search_agent_name_code'.tr,
              prefixIcon: Padding(
                padding: EdgeInsets.all(paddingMedium),
                child: Utils.cachedSvgWrapper(
                  'icon/ic-linear-search -2.svg',
                  color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                ),
              ),
            ),
            SizedBox(height: paddingSmall),
            ListView.separated(
              shrinkWrap: true,
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.only(top: paddingMedium, bottom: 18),
              separatorBuilder:
                  (context, index) => Divider(color: kColorBorderLight),
              itemCount: 20,
              itemBuilder:
                  (context, index) => InkWell(
                    onTap: () => Get.toNamed(Routes.TERMINASI),
                    child: _AgentItem(),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AgentItem extends StatelessWidget {
  const _AgentItem();

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(width: 44, height: 44, child: CircleAvatar()),
        SizedBox(width: paddingMedium),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'BM Majid Sulistio',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                '0000 1234',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorTextTersierLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
