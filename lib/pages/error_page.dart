import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/components/pdl_button.dart';

class ErrorPage extends StatelessWidget {
  final String message;

  const ErrorPage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kColorBgLight,
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(paddingLarge),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Error Icon
                Icon(Icons.error_outline, size: 120, color: kColorError),
                SizedBox(height: paddingLarge),

                // Error Title
                Text(
                  '404',
                  style: Theme.of(context).textTheme.displayLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: kColorError,
                  ),
                ),
                SizedBox(height: paddingMedium),

                // Error Message
                Text(
                  message,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: kColorTextLight,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: paddingSmall),

                // Description
                Text(
                  'Halaman yang Anda cari tidak ditemukan atau telah dipindahkan.',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: kColorTextTersier),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: paddingExtraLarge),

                // Back to Home Button
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    title: 'Kembali ke Beranda',
                    onPressed: () {
                      Get.offAllNamed(Routes.MAIN);
                    },
                    backgroundColor: kColorGlobalBlue,
                    analyticsName: 'Back to Home Button Clicked',
                    analyticsSection: 'Error Page',
                    analyticsData: {'error_type': 'page_not_found'},
                  ),
                ),
                SizedBox(height: paddingMedium),

                // Login Button (if not authenticated)
                SizedBox(
                  width: double.infinity,
                  child: PdlButton(
                    title: 'Login',
                    onPressed: () {
                      Get.offAllNamed(Routes.LOGIN);
                    },
                    backgroundColor: Colors.transparent,
                    foregorundColor: kColorGlobalBlue,
                    analyticsName: 'Go to Login Button Clicked',
                    analyticsSection: 'Error Page',
                    analyticsData: {
                      'error_type': 'page_not_found',
                      'redirect_to': 'login',
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
