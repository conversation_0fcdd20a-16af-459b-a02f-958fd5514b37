import 'package:flutter/foundation.dart';

class AnalyticsRouteTest {
  /// Test the route cleaning function with various inputs
  static void runTests() {
    if (!kDebugMode) return;

    // print('🧪 Testing Analytics Route Cleaning Function');
    // print('=' * 50);

    final testCases = [
      // Basic routes
      {'input': '/', 'expected': 'home'},
      {'input': '/login', 'expected': 'login'},
      {'input': '/main', 'expected': 'main'},

      // Nested routes
      {'input': '/keagenan', 'expected': 'keagenan'},
      {'input': '/keagenan/recruitment', 'expected': 'keagenan_recruitment'},
      {'input': '/keagenan/approval', 'expected': 'keagenan_approval'},

      // Routes with IDs
      {
        'input': '/keagenan/recruitment/123',
        'expected': 'keagenan_recruitment_id',
      },
      {'input': '/notification/456', 'expected': 'notification_id'},

      // Routes with UUIDs
      {
        'input': '/keagenan/approval/2fc5e4f9-fbcf-4c58-a0c7-778a94848171',
        'expected': 'keagenan_approval_uuid',
      },
      {
        'input':
            '/recruitment/form-keagenan/abc123def456ghi789jkl012mno345pqr678',
        'expected': 'recruitment_form-keagenan_uuid',
      },

      // Routes with query parameters
      {'input': '/keagenan?tab=recruitment', 'expected': 'keagenan'},
      {'input': '/main?debug_mode=1', 'expected': 'main'},

      // Routes with fragments
      {'input': '/profile#settings', 'expected': 'profile'},
      {
        'input': '/keagenan/recruitment#form',
        'expected': 'keagenan_recruitment',
      },

      // Complex routes
      {
        'input': '/keagenan/recruitment/form-keagenan/new',
        'expected': 'keagenan_recruitment_form-keagenan_new',
      },
      {'input': '/profile-edit', 'expected': 'profile-edit'},

      // Edge cases
      {'input': '', 'expected': 'home'},
      {'input': '///', 'expected': 'home'},
      {'input': '/special-chars!@#', 'expected': 'special-chars___'},
    ];

    // ignore: unused_local_variable
    int passed = 0;
    int failed = 0;

    for (final testCase in testCases) {
      final input = testCase['input'] as String;
      final expected = testCase['expected'] as String;
      final actual = _cleanRouteName(input);

      if (actual == expected) {
        // print('✅ "$input" → "$actual"');
        passed++;
      } else {
        // print('❌ "$input" → "$actual" (expected: "$expected")');
        failed++;
      }
    }

    // print('=' * 50);
    // print('📊 Test Results: $passed passed, $failed failed');

    if (failed == 0) {
      // print('🎉 All tests passed!');
    } else {
      // print('⚠️  Some tests failed. Check the route cleaning logic.');
    }
  }

  /// Clean route name for analytics (copy of the actual function for testing)
  static String _cleanRouteName(String route) {
    // Remove parameters and query strings
    String cleaned = route.split('?').first.split('#').first;

    // Replace parameter placeholders with generic names
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/[a-f0-9-]{30,}'), // UUID patterns
      (match) => '_uuid',
    );
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/\d+'), // Numeric IDs
      (match) => '_id',
    );
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/:[\w]+'), // Route parameters like /:id, /:uuid
      (match) => '_param',
    );

    // Remove leading slash and replace remaining slashes with underscores
    if (cleaned.startsWith('/')) cleaned = cleaned.substring(1);
    cleaned = cleaned.replaceAll('/', '_');

    // Handle special cases
    if (cleaned.isEmpty || cleaned == '/' || cleaned == '') return 'home';
    if (cleaned == 'login') return 'login';
    if (cleaned == 'main') return 'main';

    // Ensure valid analytics name (alphanumeric and underscores only)
    cleaned = cleaned.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_');
    cleaned = cleaned.replaceAll(
      RegExp(r'_+'),
      '_',
    ); // Remove multiple underscores
    cleaned = cleaned.replaceAll(
      RegExp(r'^_|_$'),
      '',
    ); // Remove leading/trailing underscores

    return cleaned.isEmpty ? 'unknown_page' : cleaned;
  }
}

/// Extension to easily run tests from anywhere
extension AnalyticsTestExtension on Object {
  void testAnalyticsRoutes() {
    AnalyticsRouteTest.runTests();
  }
}
