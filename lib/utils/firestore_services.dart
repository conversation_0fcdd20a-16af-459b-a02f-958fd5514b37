import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FirestoreServices {
  FirebaseFirestore db = FirebaseFirestore.instance;

  Future<void> initFireStore() async {
    try {
      // Enable offline persistence with a larger cache size
      db.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes:
            Settings.CACHE_SIZE_UNLIMITED, // Use unlimited cache size
        sslEnabled: true,
      );

      // Enable network for Firestore but allow offline operations
      try {
        await db.enableNetwork();
      } catch (e) {
        try {
          Get.find<LoggerService>().log(
            'Network enable failed, continuing with offline mode: $e',
          );
        } catch (_) {
          log('Network enable failed, continuing with offline mode: $e');
        }
      }

      try {
        Get.find<LoggerService>().log(
          'Firestore initialized with offline persistence',
        );
      } catch (_) {
        log(
          'Firestore initialized with offline persistence',
        ); // Fallback to direct log
      }

      // Tunggu sebentar untuk memastikan pengaturan diterapkan
      await Future.delayed(const Duration(milliseconds: 500));

      // Pre-cache some collections to ensure they're available offline
      try {
        final SharedPreferences prefs = await SharedPreferences.getInstance();
        String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

        if (fireStoreId.isNotEmpty) {
          // Check if we're online before attempting pre-caching
          final bool isOnline = await _checkInternetConnection();

          if (isOnline) {
            try {
              // Pre-cache the user's document only if online
              await db.collection(kFireCollectionAgent).doc(fireStoreId).get();

              // Pre-cache the API calls collection only if online
              await db
                  .collection(kFireCollectionAgent)
                  .doc(fireStoreId)
                  .collection('apiCall')
                  .limit(
                    100,
                  ) // Meningkatkan limit untuk memastikan lebih banyak data tersedia offline
                  .get();

              // Pre-cache the favorite widgets document only if online
              await db.collection(kFireCollectionAgent).doc(fireStoreId).get();

              try {
                Get.find<LoggerService>().log(
                  'Pre-cached collections for offline use',
                );
              } catch (_) {
                log(
                  'Pre-cached collections for offline use',
                ); // Fallback to direct log
              }
            } catch (e) {
              try {
                Get.find<LoggerService>().log(
                  'Pre-caching failed (will use cache-only mode): $e',
                );
              } catch (_) {
                log(
                  'Pre-caching failed (will use cache-only mode): $e',
                ); // Fallback to direct log
              }
            }
          } else {
            try {
              Get.find<LoggerService>().log(
                'Device is offline, skipping pre-caching (will use cache-only mode)',
              );
            } catch (_) {
              log(
                'Device is offline, skipping pre-caching (will use cache-only mode)',
              ); // Fallback to direct log
            }
          }
        } else {
          try {
            Get.find<LoggerService>().log(
              'No Firestore ID available for pre-caching, waiting for login',
            );
          } catch (_) {
            log('No Firestore ID available for pre-caching, waiting for login');
          }
        }
      } catch (cacheError) {
        try {
          Get.find<LoggerService>().log(
            'Error pre-caching collections: $cacheError',
          );
        } catch (_) {
          log(
            'Error pre-caching collections: $cacheError',
          ); // Fallback to direct log
        }
        // Continue anyway
      }

      await getInitialValue();

      // Verifikasi bahwa Firestore siap untuk offline
      try {
        final isOnline = await _checkInternetConnection();
        Get.find<LoggerService>().log(
          'Firestore initialization complete. Network status: ${isOnline ? "online" : "offline"}',
        );

        // Jika offline, verifikasi bahwa cache tersedia
        if (!isOnline) {
          Get.find<LoggerService>().log(
            'Device is offline, verifying Firestore offline capabilities',
          );
        }
      } catch (e) {
        log('Error checking network status: $e');
      }

      return; // Explicit return untuk kejelasan
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error initializing Firestore: $e');
      } catch (_) {
        log('Error initializing Firestore: $e'); // Fallback to direct log
      }
      // Still try to continue even if there's an error
      return; // Explicit return untuk kejelasan
    }
  }

  getInitialValue() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

    if (fireStoreId != '') {
      try {
        Get.find<LoggerService>().log(
          'Getting initial values from Firestore for: $fireStoreId',
        );
      } catch (_) {
        log('Getting initial values from Firestore for: $fireStoreId');
      }

      try {
        // Try cache first to avoid network calls when offline
        DocumentSnapshot doc = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .get(const GetOptions(source: Source.cache));

        if (doc.exists) {
          var data = doc.data() as Map<String, dynamic>?;
          if (data != null) {
            // Set theme
            if (data.containsKey(kFireDocTheme)) {
              Utils.setTheme(data[kFireDocTheme]);
            }

            // Set locale
            if (data.containsKey(kFireDocLanguage)) {
              String language = data[kFireDocLanguage];
              Utils.setLocale(language);
              await prefs.setString(kFireDocLanguage, language);

              try {
                Get.find<LoggerService>().log(
                  'Successfully loaded language from cache: $language',
                );
              } catch (_) {
                log('Successfully loaded language from cache: $language');
              }
            }
            return; // Successfully loaded from cache
          }
        }
      } catch (cacheError) {
        try {
          Get.find<LoggerService>().log(
            'Cache read failed for initial values: $cacheError',
          );
        } catch (_) {
          log('Cache read failed for initial values: $cacheError');
        }
      }

      // If cache failed and we're online, try server
      final bool isOnline = await _checkInternetConnection();
      if (isOnline) {
        try {
          DocumentSnapshot doc = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .get(const GetOptions(source: Source.server));

          if (doc.exists) {
            var data = doc.data() as Map<String, dynamic>?;
            if (data != null) {
              // Set theme
              if (data.containsKey(kFireDocTheme)) {
                Utils.setTheme(data[kFireDocTheme]);
              }

              // Set locale
              if (data.containsKey(kFireDocLanguage)) {
                String language = data[kFireDocLanguage];
                Utils.setLocale(language);
                await prefs.setString(kFireDocLanguage, language);

                try {
                  Get.find<LoggerService>().log(
                    'Successfully loaded language from server: $language',
                  );
                } catch (_) {
                  log('Successfully loaded language from server: $language');
                }
              }
              return; // Successfully loaded from server
            }
          } else {
            // doc not found, create one if already logged in
            if (prefs.getString(kStorageToken) != '') {
              if (prefs.getString(kStorageUserFirestoreId) == '') {
                Future.delayed(Duration(seconds: 2));
              }
              // Try to get language from SharedPreferences if available
              String? savedLanguage = prefs.getString(kFireDocLanguage);
              createCollection(initialLanguage: savedLanguage);
            }
          }
        } catch (serverError) {
          try {
            Get.find<LoggerService>().log(
              'Server read failed for initial values: $serverError',
            );
          } catch (_) {
            log('Server read failed for initial values: $serverError');
          }
        }
      } else {
        try {
          Get.find<LoggerService>().log(
            'Device is offline, using default values for theme and language',
          );
        } catch (_) {
          log('Device is offline, using default values for theme and language');
        }

        // When offline, try to use cached language from SharedPreferences
        String? cachedLanguage = prefs.getString(kFireDocLanguage);
        if (cachedLanguage != null && cachedLanguage.isNotEmpty) {
          Utils.setLocale(cachedLanguage);
          try {
            Get.find<LoggerService>().log(
              'Using cached language from SharedPreferences: $cachedLanguage',
            );
          } catch (_) {
            log(
              'Using cached language from SharedPreferences: $cachedLanguage',
            );
          }
        } else {
          // Final fallback to default
          Utils.setLocale('id_ID');
        }
      }
    } else {
      try {
        Get.find<LoggerService>().log(
          'No Firestore ID available, using default language',
        );
      } catch (_) {
        log('No Firestore ID available, using default language');
      }
      // Use default language when no Firestore ID
      Utils.setLocale('id_ID');
    }
  }

  createCollection({String? initialLanguage}) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';
    if (fireStoreId == '') {
      return;
    } else {
      // Only create collection if online
      final bool isOnline = await _checkInternetConnection();
      if (isOnline) {
        // Use initialLanguage from server if available, otherwise default to 'id_ID'
        String defaultLanguage = initialLanguage ?? 'id_ID';

        //   check if login or not
        // create new firestore agent id ("agents-${agent_code}-${user_id}")
        db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .set({'theme_is_dark': Get.isDarkMode, 'language': defaultLanguage})
            .then((_) {
              try {
                Get.find<LoggerService>().log(
                  'Successfully created agent document with language: $defaultLanguage',
                );
              } catch (_) {
                log(
                  'Successfully created agent document with language: $defaultLanguage',
                );
              }
            })
            .catchError((e) {
              try {
                Get.find<LoggerService>().log(
                  'Failed to create agent document: $e',
                );
              } catch (_) {
                log('Failed to create agent document: $e');
              }
            });
      } else {
        try {
          Get.find<LoggerService>().log(
            'Device is offline, skipping document creation',
          );
        } catch (_) {
          log('Device is offline, skipping document creation');
        }
      }
    }
  }

  setTheme(bool isDarkMode) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

    db
        .collection(kFireCollectionAgent)
        .doc(fireStoreId)
        .update({'theme_is_dark': isDarkMode})
        .then((_) {
          // print('here success set theme');
        })
        .catchError((e) {
          // print('here failed bro $e');
        });
  }

  setLanguage(String languageCode, {String? fId}) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String fireStoreId = fId ?? prefs.getString(kStorageUserFirestoreId) ?? '';

    db
        .collection(kFireCollectionAgent)
        .doc(fireStoreId)
        .update({'language': languageCode})
        .then((_) {
          // print('here success set Language');
        })
        .catchError((e) {
          // print('here failed bro $e');
        });
  }

  /// Sanitize URL to create a valid document ID
  String _sanitizeUrl(String url) {
    return url.replaceAll(RegExp(r'[\/:*?"<>|]'), '_');
  }

  /// Store API call data in Firestore
  Future<void> storeApiCallData({
    required String url,
    required dynamic responseData,
  }) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        Get.find<LoggerService>().log(
          'No Firestore ID available for storing API data',
        );
        return; // No firestore ID available
      }

      // Create a sanitized URL to use as document ID
      String docId = _sanitizeUrl(url);

      Get.find<LoggerService>().log(
        'Storing API data for URL: $url (docId: $docId)',
      );

      // Store data in a local variable to ensure it's available for offline use
      final dataToStore = {
        'url': url,
        'responseData': responseData,
        'timestamp': FieldValue.serverTimestamp(),
        'lastUpdated':
            DateTime.now().millisecondsSinceEpoch, // Local timestamp as backup
        'cachedAt': DateTime.now().toString(), // For debugging
      };

      // Store the API response data in Firestore with SetOptions to merge
      // This ensures we don't overwrite the entire document if it already exists
      try {
        await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('apiCall')
            .doc(docId)
            .set(dataToStore, SetOptions(merge: true));

        Get.find<LoggerService>().log(
          'Successfully stored API data for URL: $url',
        );

        // Verify the data was stored by reading it back immediately
        try {
          final docSnapshot = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('apiCall')
              .doc(docId)
              .get(const GetOptions(source: Source.cache));

          if (docSnapshot.exists) {
            Get.find<LoggerService>().log(
              'Verified data is in cache for URL: $url',
            );
          } else {
            Get.find<LoggerService>().log(
              'Warning: Data not found in cache immediately after storing for URL: $url',
            );
          }
        } catch (verifyError) {
          Get.find<LoggerService>().log(
            'Error verifying cached data: $verifyError',
          );
        }
      } catch (e) {
        Get.find<LoggerService>().log('Error storing API call data: $e');
      }
    } catch (e) {
      Get.find<LoggerService>().log('Error in storeApiCallData: $e');
    }
  }

  /// Get API call data from Firestore
  Future<dynamic> getApiCallData({required String url}) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String fireStoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      if (fireStoreId.isEmpty) {
        Get.find<LoggerService>().log(
          'No Firestore ID available for offline data retrieval',
        );
        return null; // No firestore ID available
      }

      // Create a sanitized URL to use as document ID
      String docId = _sanitizeUrl(url);

      Get.find<LoggerService>().log(
        'Attempting to retrieve data for URL: $url (docId: $docId)',
      );

      // Selalu coba cache terlebih dahulu, terlepas dari status koneksi
      try {
        Get.find<LoggerService>().log(
          'Explicitly trying cache first for URL: $url',
        );
        DocumentSnapshot doc = await db
            .collection(kFireCollectionAgent)
            .doc(fireStoreId)
            .collection('apiCall')
            .doc(docId)
            .get(const GetOptions(source: Source.cache));

        if (doc.exists) {
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          final responseData = data['responseData'];
          Get.find<LoggerService>().log(
            'Successfully retrieved cached data for URL: $url',
          );
          Get.find<LoggerService>().log(
            'Data type: ${responseData.runtimeType}',
          );
          return responseData;
        } else {
          Get.find<LoggerService>().log(
            'Document does not exist in cache for URL: $url',
          );
        }
      } catch (cacheError) {
        Get.find<LoggerService>().log(
          'Error retrieving from cache: $cacheError',
        );
      }

      // Jika cache gagal dan kita online, coba dari server
      final hasConnection = await _checkInternetConnection();
      if (hasConnection) {
        try {
          Get.find<LoggerService>().log('Trying server for URL: $url');
          DocumentSnapshot doc = await db
              .collection(kFireCollectionAgent)
              .doc(fireStoreId)
              .collection('apiCall')
              .doc(docId)
              .get(const GetOptions(source: Source.server));

          if (doc.exists) {
            Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
            final responseData = data['responseData'];
            Get.find<LoggerService>().log(
              'Retrieved data from server for URL: $url',
            );

            // Simpan kembali ke cache untuk memastikan data tersedia offline
            try {
              await storeApiCallData(url: url, responseData: responseData);
              Get.find<LoggerService>().log(
                'Re-cached server data for future offline use',
              );
            } catch (e) {
              Get.find<LoggerService>().log(
                'Failed to re-cache server data: $e',
              );
            }

            return responseData;
          } else {
            Get.find<LoggerService>().log(
              'Document does not exist on server for URL: $url',
            );
          }
        } catch (serverError) {
          Get.find<LoggerService>().log(
            'Error retrieving from server: $serverError',
          );
        }
      }

      // Sebagai upaya terakhir, coba dengan opsi default (akan menggunakan cache jika offline)
      if (hasConnection) {
        try {
          DocumentSnapshot doc =
              await db
                  .collection(kFireCollectionAgent)
                  .doc(fireStoreId)
                  .collection('apiCall')
                  .doc(docId)
                  .get();

          if (doc.exists) {
            Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
            final responseData = data['responseData'];
            Get.find<LoggerService>().log(
              'Successfully retrieved data with default options for URL: $url (source: ${doc.metadata.isFromCache ? "cache" : "server"})',
            );
            return responseData;
          }
        } catch (e) {
          Get.find<LoggerService>().log(
            'Error retrieving data with default options: $e',
          );
        }
      }

      Get.find<LoggerService>().log(
        'No data found for URL: $url in cache or server',
      );
      return null; // Document doesn't exist in cache or server
    } catch (e) {
      Get.find<LoggerService>().log('Error in getApiCallData: $e');
      return null;
    }
  }

  /// Check internet connection
  Future<bool> _checkInternetConnection() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error checking connectivity: $e');
      } catch (_) {
        log('Error checking connectivity: $e'); // Fallback to direct log
      }
      return false;
    }
  }

  /// Disable Firestore network when going offline
  Future<void> disableNetwork() async {
    try {
      await db.disableNetwork();
      try {
        Get.find<LoggerService>().log(
          'Firestore network disabled for offline mode',
        );
      } catch (_) {
        log('Firestore network disabled for offline mode');
      }
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Failed to disable Firestore network: $e',
        );
      } catch (_) {
        log('Failed to disable Firestore network: $e');
      }
    }
  }

  /// Enable Firestore network when going online
  Future<void> enableNetwork() async {
    try {
      await db.enableNetwork();
      try {
        Get.find<LoggerService>().log(
          'Firestore network enabled for online mode',
        );
      } catch (_) {
        log('Firestore network enabled for online mode');
      }
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Failed to enable Firestore network: $e');
      } catch (_) {
        log('Failed to enable Firestore network: $e');
      }
    }
  }

  /// Save favorite widgets to Firestore
  Future<void> saveFavoriteWidgets(List<String> favoriteIds) async {
    try {
      // Check if online
      final bool isOnline = await _checkInternetConnection();

      // Get agent ID from SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String firestoreId = prefs.getString(kStorageUserFirestoreId) ?? '';
      if (firestoreId.isEmpty) {
        log(
          'No Firestore ID available, skipping Firestore save for favorite widgets',
        );
        return;
      }

      try {
        Get.find<LoggerService>().log(
          'Saving favorite widgets to Firestore: $favoriteIds',
        );
      } catch (_) {
        log('Saving favorite widgets to Firestore: $favoriteIds');
      }

      // Create the data to save
      Map<String, dynamic> dataToSave = {
        'fav_widget': favoriteIds,
        'fav_widget_updated_at':
            isOnline ? FieldValue.serverTimestamp() : Timestamp.now(),
      };

      // If online, save to Firestore server
      if (isOnline) {
        try {
          await db
              .collection(kFireCollectionAgent)
              .doc(firestoreId)
              .set(dataToSave, SetOptions(merge: true));

          try {
            Get.find<LoggerService>().log(
              'Successfully saved favorite widgets to Firestore server',
            );
          } catch (_) {
            log('Successfully saved favorite widgets to Firestore server');
          }
        } catch (e) {
          try {
            Get.find<LoggerService>().log(
              'Error saving favorite widgets to Firestore server: $e',
            );
          } catch (_) {
            log('Error saving favorite widgets to Firestore server: $e');
          }
        }
      } else {
        log('Device is offline, saving to local cache only');
      }

      // Always update the local cache regardless of online status
      try {
        // Get the current document from cache
        DocumentSnapshot<Map<String, dynamic>> docSnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(firestoreId)
            .get(const GetOptions(source: Source.cache));

        // Merge the new data with existing data
        Map<String, dynamic> newData = {};
        if (docSnapshot.exists) {
          newData = Map<String, dynamic>.from(docSnapshot.data() ?? {});
        }
        newData.addAll(dataToSave);

        // Update the local cache
        await db
            .collection(kFireCollectionAgent)
            .doc(firestoreId)
            .set(newData, SetOptions(merge: true));

        try {
          Get.find<LoggerService>().log(
            'Successfully updated favorite widgets in local cache',
          );
        } catch (_) {
          log('Successfully updated favorite widgets in local cache');
        }
      } catch (e) {
        try {
          Get.find<LoggerService>().log(
            'Error updating favorite widgets in local cache: $e',
          );
        } catch (_) {
          log('Error updating favorite widgets in local cache: $e');
        }
      }

      // Verify the cache was updated by reading it back
      try {
        final verifySnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(firestoreId)
            .get(const GetOptions(source: Source.cache));

        if (verifySnapshot.exists) {
          final data = verifySnapshot.data();
          if (data != null && data.containsKey('fav_widget')) {
            final List<dynamic> widgetIds = data['fav_widget'];
            try {
              Get.find<LoggerService>().log(
                'Verified favorite widgets in cache: $widgetIds',
              );
            } catch (_) {
              log('Verified favorite widgets in cache: $widgetIds');
            }
          }
        }
      } catch (e) {
        try {
          Get.find<LoggerService>().log(
            'Error verifying favorite widgets in cache: $e',
          );
        } catch (_) {
          log('Error verifying favorite widgets in cache: $e');
        }
      }
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error saving favorite widgets to Firestore: $e',
        );
      } catch (_) {
        log('Error saving favorite widgets to Firestore: $e');
      }
    }
  }

  /// Load favorite widgets from Firestore
  Future<List<String>?> getFavoriteWidgets() async {
    try {
      // Get agent ID from SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      String firestoreId = prefs.getString(kStorageUserFirestoreId) ?? '';

      // If firestoreId is not available, wait for it with a timeout
      if (firestoreId.isEmpty) {
        try {
          Get.find<LoggerService>().log(
            'Waiting for Firestore ID to become available...',
          );
        } catch (_) {
          log('Waiting for Firestore ID to become available...');
        }

        // Try to get firestoreId for up to 5 seconds (10 attempts, 500ms apart)
        int attempts = 0;
        const maxAttempts = 10;
        const delayMs = 500;

        while (firestoreId.isEmpty && attempts < maxAttempts) {
          await Future.delayed(Duration(milliseconds: delayMs));
          firestoreId = prefs.getString(kStorageUserFirestoreId) ?? '';
          attempts++;

          try {
            Get.find<LoggerService>().log(
              'Attempt $attempts to get Firestore ID: ${firestoreId.isEmpty ? "still empty" : "found"}',
            );
          } catch (_) {
            log(
              'Attempt $attempts to get Firestore ID: ${firestoreId.isEmpty ? "still empty" : "found"}',
            );
          }
        }
      }

      // Check if we got a firestoreId after waiting
      if (firestoreId.isEmpty) {
        try {
          Get.find<LoggerService>().log(
            'No Firestore ID available after waiting, skipping Firestore load for favorite widgets',
          );
        } catch (_) {
          log(
            'No Firestore ID available after waiting, skipping Firestore load for favorite widgets',
          );
        }
        return null;
      }

      try {
        Get.find<LoggerService>().log(
          'Loading favorite widgets from Firestore',
        );
      } catch (_) {
        log('Loading favorite widgets from Firestore');
      }

      // Check if online
      final bool isOnline = await _checkInternetConnection();

      // Try cache first to ensure we have data even offline
      try {
        final docSnapshot = await db
            .collection(kFireCollectionAgent)
            .doc(firestoreId)
            .get(const GetOptions(source: Source.cache));

        if (docSnapshot.exists) {
          final data = docSnapshot.data();
          if (data != null && data.containsKey('fav_widget')) {
            final List<dynamic> widgetIds = data['fav_widget'];
            final List<String> result =
                widgetIds.map((id) => id.toString()).toList();

            try {
              Get.find<LoggerService>().log(
                'Successfully loaded favorite widgets from cache: $result',
              );
            } catch (_) {
              log('Successfully loaded favorite widgets from cache: $result');
            }

            // If we're offline, return the cache data immediately
            if (!isOnline) {
              log('Device is offline, using cached favorite widgets');
              return result;
            }

            // If we're online, store the cache result but continue to try server
            List<String> cacheResult = result;

            // Try to get from server to get the latest data
            try {
              final serverSnapshot = await db
                  .collection(kFireCollectionAgent)
                  .doc(firestoreId)
                  .get(const GetOptions(source: Source.server));

              if (serverSnapshot.exists) {
                final serverData = serverSnapshot.data();
                if (serverData != null &&
                    serverData.containsKey('fav_widget')) {
                  final List<dynamic> serverWidgetIds =
                      serverData['fav_widget'];
                  final List<String> serverResult =
                      serverWidgetIds.map((id) => id.toString()).toList();

                  try {
                    Get.find<LoggerService>().log(
                      'Successfully loaded favorite widgets from server: $serverResult',
                    );
                  } catch (_) {
                    log(
                      'Successfully loaded favorite widgets from server: $serverResult',
                    );
                  }

                  // Return server data as it's more up-to-date
                  return serverResult;
                }
              }

              // If server data doesn't have fav_widget, return cache data
              return cacheResult;
            } catch (e) {
              try {
                Get.find<LoggerService>().log(
                  'Error getting favorite widgets from server, using cache: $e',
                );
              } catch (_) {
                log(
                  'Error getting favorite widgets from server, using cache: $e',
                );
              }

              // Return cache data if server fails
              return cacheResult;
            }
          }
        }
      } catch (e) {
        try {
          Get.find<LoggerService>().log(
            'Error getting favorite widgets from cache: $e',
          );
        } catch (_) {
          log('Error getting favorite widgets from cache: $e');
        }
      }

      // If we're online, try server as a last resort
      if (isOnline) {
        try {
          final docSnapshot = await db
              .collection(kFireCollectionAgent)
              .doc(firestoreId)
              .get(const GetOptions(source: Source.server));

          if (docSnapshot.exists) {
            final data = docSnapshot.data();
            if (data != null && data.containsKey('fav_widget')) {
              final List<dynamic> widgetIds = data['fav_widget'];
              final List<String> result =
                  widgetIds.map((id) => id.toString()).toList();

              try {
                Get.find<LoggerService>().log(
                  'Successfully loaded favorite widgets from server (last resort): $result',
                );
              } catch (_) {
                log(
                  'Successfully loaded favorite widgets from server (last resort): $result',
                );
              }

              return result;
            }
          }
        } catch (e) {
          try {
            Get.find<LoggerService>().log(
              'Error getting favorite widgets from server (last resort): $e',
            );
          } catch (_) {
            log('Error getting favorite widgets from server (last resort): $e');
          }
        }
      }

      // If no data found in Firestore
      return null;
    } catch (e) {
      try {
        Get.find<LoggerService>().log(
          'Error loading favorite widgets from Firestore: $e',
        );
      } catch (_) {
        log('Error loading favorite widgets from Firestore: $e');
      }
      return null;
    }
  }
}
