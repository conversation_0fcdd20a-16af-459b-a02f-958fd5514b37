import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// Test widget to verify SVG functionality across platforms
class SvgTestWidget extends StatelessWidget {
  const SvgTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('SVG Test - ${kIsWeb ? 'Web' : 'Mobile'}'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Platform: ${kIsWeb ? 'Web (using SvgPicture.network)' : 'Mobile (using CachedNetworkSVGImage)'}',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Utils.cachedSvgWrapper(
                'icons/home.svg', // Example SVG path
                width: 80,
                height: 80,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'If you see an SVG icon above, the platform-specific implementation is working correctly!',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
