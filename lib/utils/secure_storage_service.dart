import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    webOptions: WebOptions(
      dbName: 'pdl_superapp_secure',
      publicKey: 'pdl_superapp_public_key',
    ),
  );

  // Fallback keys for web when secure storage fails
  static const String _webKeyUsername = 'web_saved_username';
  static const String _webKeyPassword = 'web_saved_password';

  // Keys for storing credentials
  static const String _keyUsername = 'saved_username';
  static const String _keyPassword = 'saved_password';
  static const String _keyRemember = 'remember_me';

  /// Save login credentials (only if remember me is enabled)
  static Future<void> saveCredentials({
    required String username,
    required String password,
  }) async {
    try {
      // final rememberMe = await getRememberMe();

      await _storage.write(key: _keyUsername, value: username);
      await _storage.write(key: _keyPassword, value: password);
      debugPrint('Credentials saved securely');
    } catch (e) {
      debugPrint('Error saving credentials: $e');
      // Fallback to SharedPreferences if secure storage fails
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_webKeyUsername, username);
        await prefs.setString(_webKeyPassword, password);
        debugPrint('Credentials saved using fallback method');
      } catch (fallbackError) {
        debugPrint('Fallback error saving credentials: $fallbackError');
      }
    }
  }

  /// Get saved credentials
  static Future<Map<String, String?>> getSavedCredentials() async {
    try {
      String? username, password;

      username = await _storage.read(key: _keyUsername);
      password = await _storage.read(key: _keyPassword);

      return {'username': username, 'password': password};
    } catch (e) {
      debugPrint('Error reading saved credentials: $e');

      return {'username': null, 'password': null};
    }
  }

  /// Clear all saved credentials
  static Future<void> clearCredentials() async {
    try {
      if (kIsWeb) {
        // Use SharedPreferences as fallback for web
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_webKeyUsername);
        await prefs.remove(_webKeyPassword);
      } else {
        final remember = (await _storage.read(key: _keyRemember)) ?? "";
        if (remember.isEmpty) {
          await _storage.delete(key: _keyUsername);
          await _storage.delete(key: _keyPassword);
        } else {
          await _storage.delete(key: _keyPassword);
        }
      }
      debugPrint('Credentials cleared');
    } catch (e) {
      debugPrint('Error clearing credentials: $e');
    }
  }

  /// Clear credentials but keep remember me preference
  static Future<void> clearCredentialsOnly() async {
    try {
      await _storage.delete(key: _keyUsername);
      await _storage.delete(key: _keyPassword);
      debugPrint('Credentials cleared (remember me preference kept)');
    } catch (e) {
      debugPrint('Error clearing credentials: $e');
    }
  }

  /// Replace old credentials with new ones
  static Future<void> replaceCredentials({
    required String username,
    required String password,
  }) async {
    try {
      // Clear old credentials first
      await clearCredentialsOnly();
      // Save new credentials
      await saveCredentials(username: username, password: password);
      await _storage.write(key: _keyRemember, value: "X");
      debugPrint('Credentials replaced successfully');
    } catch (e) {
      debugPrint('Error replacing credentials: $e');
    }
  }

  static Future<void> loginWithoutRemember() async {
    try {
      await clearCredentialsOnly();
      await _storage.write(key: _keyRemember, value: "");
      debugPrint('Credentials deleted successfully');
    } catch (e) {
      debugPrint('Error deleting credentials: $e');
    }
  }

  static Future<String> preloadUsername() async {
    try {
      final remember = (await _storage.read(key: _keyRemember)) ?? "";
      if (remember.isNotEmpty) {
        return (await _storage.read(key: _keyUsername)) ?? "";
      }
    } catch (e) {
      //
    }
    return "";
  }
}
