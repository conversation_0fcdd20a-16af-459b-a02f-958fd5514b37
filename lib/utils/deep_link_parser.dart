import 'dart:async';
import 'dart:developer' as dev;

import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

class DeepLinkParser {
  DeepLinkParser._();
  static final _instance = DeepLinkParser._();
  factory DeepLinkParser() => _instance;

  final _appLinks = AppLinks();

  Future<void> initAppLink() async {
    Future.delayed(Duration(seconds: 2)).then((val) async {
      if (await _appLinks.getInitialLink() != null) {
        Uri uriLink = await _appLinks.getInitialLink() ?? Uri();
        parseData(uriLink);
      }
      _appLinks.uriLinkStream.listen((uri) {
        parseData(uri);
      });
    });
    // if (fragment.contains('/product/')) {
    //   var lastIndexOfSlash = fragment.lastIndexOf('/');
    //   if (lastIndexOfSlash == fragment.length - 1) {
    //     return const ProductList();
    //   }
    //   String id = fragment.substring(lastIndexOfSlash + 1);
    //   return ProductScreen.withId(id: id);
    // }

    // return const Home();
  }

  parseData(Uri uri) {
    try {
      _logMessage('🔗 Deep link received: ${uri.toString()}');

      String path = uri.path;
      String scheme = uri.scheme;
      String host = uri.host;

      // !!IMPORTANT: this is only if we can't accept encoded URL in the first place, bcs when using cmd to perform this on emulator we couldnt use un-encoded url
      // decode -> re encode
      // decode uri from encoded to normal
      String decode = Uri.decodeFull(uri.toString());
      // parse again so we can get params
      Uri uriParse = Uri.parse(decode);

      _logMessage(
        '🔍 Parsing deep link - Scheme: $scheme, Host: $host, Path: $path',
      );
      _logMessage('🔍 Path segments: ${uriParse.pathSegments}');
      _logMessage('🔍 Query parameters: ${uriParse.queryParameters}');

      // Handle different schemes
      if (_isCustomScheme(scheme)) {
        _logMessage('📱 Handling custom scheme deeplink');
        _handleCustomScheme(uriParse);
      } else if (_isWebScheme(scheme)) {
        _logMessage('🌐 Handling web scheme deeplink (Universal Link)');
        _handleWebScheme(uriParse);
      } else {
        _logMessage('⚠️ Unknown scheme: $scheme');
      }
    } catch (e) {
      _logMessage('❌ Error parsing deep link: $e');
    }
  }

  /// Check if the scheme is a custom scheme (quantumx360 or quantumx360dev)
  bool _isCustomScheme(String scheme) {
    return scheme == 'quantumx360' || scheme == 'quantumx360dev';
  }

  /// Check if the scheme is a web scheme (https)
  bool _isWebScheme(String scheme) {
    return scheme == 'https';
  }

  /// Handle custom scheme deep links
  void _handleCustomScheme(Uri uri) {
    String path = uri.path;
    Map<String, String> queryParams = uri.queryParameters;

    _logMessage('🎯 Handling custom scheme - Path: $path');

    // Remove leading slash if present
    if (path.startsWith('/')) {
      path = path.substring(1);
    }

    // Route based on path
    switch (path) {
      case 'home':
        Get.offAllNamed(Routes.HOME);
        break;
      case 'profile':
        Get.toNamed(Routes.PROFILE);
        break;
      case 'keagenan':
        Get.toNamed(Routes.MENU_KEAGENAN);
        break;
      case 'recruitment':
        Get.toNamed(Routes.KEAGENAN_LIST);
        break;
      case 'inbox':
        Get.toNamed(Routes.MENU_INBOX);
        break;
      case 'notification':
        Get.toNamed(Routes.NOTIFICATION_PAGE);
        break;
      case 'reset-password':
      case 'change-password':
        String? token;

        // Check if token is in query parameters
        if (queryParams.containsKey('token')) {
          token = queryParams['token'];
        }
        // Check if token is in path segments (for paths like change-password/TOKEN)
        else {
          List<String> pathSegments = uri.pathSegments;
          if (pathSegments.length > 1) {
            token =
                pathSegments[1]; // Get the segment after reset-password or change-password
          }
        }

        if (token != null && token.isNotEmpty) {
          _logMessage(
            '🔑 Found token for password reset: ${token.substring(0, 10)}...',
          );
          Get.toNamed(Routes.RESET_PASSWORD, parameters: {'token': token});
        } else {
          _logMessage('⚠️ Password reset link missing token parameter');
        }
        break;
      case 'public/recruitment':
        if (queryParams.containsKey('recruiter') &&
            queryParams.containsKey('candidate')) {
          Get.toNamed(
            Routes.PUBLIC_RECRUITMENT_FORM,
            parameters: {
              'recruiter': queryParams['recruiter']!,
              'candidate': queryParams['candidate']!,
            },
          );
        } else {
          Get.toNamed(Routes.PUBLIC_RECRUITMENT_FORM);
        }
        break;
      default:
        _logMessage('⚠️ Unknown custom scheme path: $path');
        Get.offAllNamed(Routes.HOME);
        break;
    }
  }

  /// Handle web scheme deep links (https://sandbox-quantumx.panindai-ichilife.co.id)
  void _handleWebScheme(Uri uri) {
    if (kIsWeb) {
      return;
    }
    String path = uri.path;
    Map<String, String> queryParams = uri.queryParameters;

    _logMessage('🌐 Handling web scheme - Path: $path');

    // Handle legacy paths and new paths
    if (path.contains('/forget-password') ||
        path.contains('/reset-password') ||
        path.contains('/change-password')) {
      String? token;

      // Check if token is in query parameters
      if (queryParams.containsKey('token')) {
        token = queryParams['token'].toString();
      }
      // Check if token is in path (e.g., /change-password/TOKEN) - legacy support
      else if (path.contains('/change-password/') ||
          path.contains('/reset-password/')) {
        List<String> pathSegments = path.split('/');
        // Find the segment after 'change-password' or 'reset-password'
        for (int i = 0; i < pathSegments.length - 1; i++) {
          if (pathSegments[i] == 'change-password' ||
              pathSegments[i] == 'reset-password') {
            if (i + 1 < pathSegments.length && pathSegments[i + 1].isNotEmpty) {
              token = pathSegments[i + 1];
              break;
            }
          }
        }
      }

      if (token != null && token.isNotEmpty) {
        _logMessage(
          '🔑 Found token for password reset: ${token.substring(0, 10)}...',
        );
        Get.toNamed(Routes.RESET_PASSWORD, parameters: {'token': token});
      } else {
        _logMessage('⚠️ Password reset link missing token parameter');
      }
    } else if (path.contains('/public/recruitment') ||
        path.contains('/public/form-keagenan')) {
      if (queryParams.containsKey('recruiter') &&
          queryParams.containsKey('candidate')) {
        Get.toNamed(
          Routes.PUBLIC_RECRUITMENT_FORM,
          parameters: {
            'recruiter': queryParams['recruiter']!,
            'candidate': queryParams['candidate']!,
          },
        );
      } else {
        Get.toNamed(Routes.PUBLIC_RECRUITMENT_FORM);
      }
    } else if (path.contains('/public/signature-only')) {
      Get.toNamed(Routes.PUBLIC_TTD_ONLY);
    } else if (path.contains('/home')) {
      Get.offAllNamed(Routes.HOME);
    } else if (path.contains('/profile')) {
      Get.toNamed(Routes.PROFILE);
    } else if (path.contains('/keagenan')) {
      Get.toNamed(Routes.MENU_KEAGENAN);
    } else if (path.contains('/inbox')) {
      Get.toNamed(Routes.MENU_INBOX);
    } else if (path.contains('/notification')) {
      Get.toNamed(Routes.NOTIFICATION_PAGE);
    } else {
      _logMessage('⚠️ Unknown web scheme path: $path');
      // Default to home for unknown paths
      Get.offAllNamed(Routes.HOME);
    }
  }

  /// Log message using LoggerService if available
  void _logMessage(String message) {
    try {
      Get.find<LoggerService>().log('[DeepLinkParser] $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[DeepLinkParser] $message');
    }
  }
}
