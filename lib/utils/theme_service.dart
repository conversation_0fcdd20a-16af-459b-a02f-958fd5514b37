import 'dart:developer' as dev;

import 'package:get/get.dart';
import 'package:pdl_superapp/controllers/theme_controller.dart';
import 'package:pdl_superapp/models/theme_models.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

class ThemeService extends GetxService {
  // Reference to the ThemeController
  late ThemeController _themeController;

  /// Initialize the theme service
  Future<ThemeService> init() async {
    try {
      // Initialize the ThemeController
      _themeController = Get.put(ThemeController(), permanent: true);
      _log('ThemeService initialized with ThemeController');
    } catch (e) {
      _logError('Error initializing ThemeService: $e');
    }
    return this;
  }

  /// Get the current theme model
  ThemeModel get currentTheme => _themeController.currentTheme.value;

  /// Get the observable theme model
  Rx<ThemeModel> get currentThemeObs => _themeController.currentTheme;

  /// Check if theme is loaded
  bool get isThemeLoaded => _themeController.isThemeLoaded.value;

  /// Get the observable theme loaded state
  RxBool get isThemeLoadedObs => _themeController.isThemeLoaded;

  /// Fetch current theme from API
  Future<void> fetchCurrentTheme() async {
    try {
      _log('Fetching current theme from API');
      await _themeController.fetchCurrentTheme();
    } catch (e) {
      _logError('Error fetching current theme: $e');
    }
  }

  /// Get asset URL by filename
  String getAssetUrl(String fileName) {
    return _themeController.getAssetUrl(fileName);
  }

  /// Log message using LoggerService if available
  void _log(String message) {
    try {
      Get.find<LoggerService>().log('[ThemeService] $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[ThemeService] $message');
    }
  }

  /// Log error using LoggerService if available
  void _logError(String message) {
    try {
      Get.find<LoggerService>().log('[ThemeService] ERROR: $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[ThemeService] ERROR: $message');
    }
  }
}
