import 'package:flutter/foundation.dart';
import 'package:pdl_superapp/services/ktp_ocr_service.dart';
import 'package:pdl_superapp/services/ocr_config_service.dart';

/// Helper class for testing OCR functionality
class OcrTestHelper {
  /// Test OCR configuration and functionality
  static Future<void> testOcrSetup() async {
    if (kDebugMode) {
      print('=== OCR Configuration Test ===');
      print('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      print('Status: ${OcrConfigService.configurationStatus}');

      if (kIsWeb) {
        print(
          'Cloud Vision API Key configured: ${OcrConfigService.isWebOcrConfigured}',
        );
        if (!OcrConfigService.isWebOcrConfigured) {
          print(
            '⚠️  Warning: Add GOOGLE_CLOUD_VISION_API_KEY to .env file for web OCR support',
          );
        }
      }
      print('=== End OCR Test ===');
    }
  }

  /// Test OCR with a sample image file (File for mobile, XFile for web)
  static Future<void> testOcrWithImage(dynamic imageFile) async {
    if (kDebugMode) {
      final imagePath = imageFile?.path ?? imageFile?.name ?? 'Unknown';
      print('=== Testing OCR with image: $imagePath ===');

      try {
        final startTime = DateTime.now();
        final result = await KtpOcrService.extractKtp(imageFile);
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);

        print('OCR completed in ${duration.inMilliseconds}ms');
        print('Results:');
        print('  NIK: ${result.nik ?? 'Not found'}');
        print('  Name: ${result.name ?? 'Not found'}');
        print('  Place of Birth: ${result.placeBirth ?? 'Not found'}');
        print('  Birth Date: ${result.birthDay ?? 'Not found'}');
        print('  Gender: ${result.gender ?? 'Not found'}');
        print('  Address: ${result.address ?? 'Not found'}');
        print('  Province: ${result.province ?? 'Not found'}');
        print('  City: ${result.city ?? 'Not found'}');
      } catch (e) {
        print('❌ OCR test failed: $e');
      }

      print('=== End OCR Image Test ===');
    }
  }

  /// Get OCR status for UI display
  static String getOcrStatusMessage() {
    if (kIsWeb) {
      if (OcrConfigService.isWebOcrConfigured) {
        return '✅ Web OCR Ready (Google Cloud Vision)';
      } else {
        return '⚠️ Web OCR Not Configured';
      }
    } else {
      return '✅ Mobile OCR Ready (Google ML Kit)';
    }
  }

  /// Check if OCR is ready to use
  static bool get isOcrReady {
    if (kIsWeb) {
      return OcrConfigService.isWebOcrConfigured;
    }
    return true; // Mobile ML Kit is always available
  }
}
