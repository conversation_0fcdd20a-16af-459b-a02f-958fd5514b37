import 'dart:developer' as dev;

import 'package:get/get.dart';
import 'package:pdl_superapp/models/theme_models.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/theme_service.dart';

/// Utility class for accessing theme assets and data throughout the app
class ThemeUtils {
  /// Get the current theme model
  static ThemeModel get currentTheme {
    try {
      return Get.find<ThemeService>().currentTheme;
    } catch (e) {
      _logError('Error getting current theme: $e');
      return ThemeModel.empty();
    }
  }

  /// Get the observable theme model
  static Rx<ThemeModel> get currentThemeObs {
    try {
      return Get.find<ThemeService>().currentThemeObs;
    } catch (e) {
      _logError('Error getting observable theme: $e');
      return ThemeModel.empty().obs;
    }
  }

  /// Check if theme data is loaded
  static bool get isThemeLoaded {
    try {
      return Get.find<ThemeService>().isThemeLoaded;
    } catch (e) {
      _logError('Error checking if theme is loaded: $e');
      return false;
    }
  }

  /// Get the observable theme loaded state
  static RxBool get isThemeLoadedObs {
    try {
      return Get.find<ThemeService>().isThemeLoadedObs;
    } catch (e) {
      _logError('Error getting observable theme loaded state: $e');
      return false.obs;
    }
  }

  /// Get asset URL by filename
  static String getAssetUrl(String fileName) {
    try {
      return Get.find<ThemeService>().getAssetUrl(fileName);
    } catch (e) {
      _logError('Error getting asset URL for $fileName: $e');
      return '';
    }
  }

  /// Refresh theme data from API
  static Future<void> refreshTheme() async {
    try {
      await Get.find<ThemeService>().fetchCurrentTheme();
    } catch (e) {
      _logError('Error refreshing theme: $e');
    }
  }

  /// Log error using LoggerService if available
  static void _logError(String message) {
    try {
      Get.find<LoggerService>().log('[ThemeUtils] ERROR: $message');
    } catch (_) {
      // Fallback to dev.log if LoggerService is not available
      dev.log('[ThemeUtils] ERROR: $message');
    }
  }
}
