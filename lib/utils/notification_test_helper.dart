import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/services/notification_service.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

/// Helper class untuk testing notification functionality
class NotificationTestHelper {
  static NotificationTestHelper get instance => NotificationTestHelper._();
  NotificationTestHelper._();

  /// Test semua aspek notification service
  Future<void> runFullNotificationTest() async {
    try {
      Get.find<LoggerService>().log('🧪 Starting full notification test...');

      final notificationService = NotificationService.instance;

      // 1. Debug status
      notificationService.debugNotificationStatus();

      // 2. Test FCM availability
      await _testFCMAvailability();

      // 3. Test local notification
      await _testLocalNotification();

      // 4. Test foreground message simulation
      await _testForegroundMessageSimulation();

      Get.find<LoggerService>().log('✅ Full notification test completed');
    } catch (e) {
      Get.find<LoggerService>().log('❌ Notification test failed: $e');
    }
  }

  /// Test FCM availability
  Future<void> _testFCMAvailability() async {
    try {
      Get.find<LoggerService>().log('🔧 Testing FCM availability...');

      final notificationService = NotificationService.instance;
      bool isAvailable = await notificationService.checkFCMAvailability();

      Get.find<LoggerService>().log('📱 FCM Available: $isAvailable');
      Get.find<LoggerService>().log(
        '🔑 FCM Token: ${notificationService.fcmToken.isNotEmpty ? "Available" : "Not available"}',
      );
      Get.find<LoggerService>().log(
        '✅ Permission granted: ${notificationService.isPermissionGranted}',
      );
    } catch (e) {
      Get.find<LoggerService>().log('❌ FCM availability test failed: $e');
    }
  }

  /// Test local notification
  Future<void> _testLocalNotification() async {
    try {
      Get.find<LoggerService>().log('🔔 Testing local notification...');

      final notificationService = NotificationService.instance;
      await notificationService.sendTestNotification();

      Get.find<LoggerService>().log('✅ Local notification test completed');
    } catch (e) {
      Get.find<LoggerService>().log('❌ Local notification test failed: $e');
    }
  }

  /// Simulate foreground message untuk testing
  Future<void> _testForegroundMessageSimulation() async {
    try {
      Get.find<LoggerService>().log('🔔 Simulating foreground message...');

      // Create a mock RemoteMessage
      final mockMessage = RemoteMessage(
        messageId: 'test_${DateTime.now().millisecondsSinceEpoch}',
        data: {
          'test': 'true',
          'screen': 'home',
          'timestamp': DateTime.now().toIso8601String(),
        },
        notification: const RemoteNotification(
          title: 'Test Foreground Message',
          body: 'This is a simulated foreground message for testing',
        ),
        from: 'test',
        sentTime: DateTime.now(),
      );

      // Simulate the message being received
      Get.find<LoggerService>().log('📨 Mock message created:');
      Get.find<LoggerService>().log(
        '  - Title: ${mockMessage.notification?.title}',
      );
      Get.find<LoggerService>().log(
        '  - Body: ${mockMessage.notification?.body}',
      );
      Get.find<LoggerService>().log('  - Data: ${mockMessage.data}');

      // Test analytics tracking
      final notificationService = NotificationService.instance;
      notificationService.testAnalyticsTracking(
        'test_foreground_message',
        mockMessage,
      );

      Get.find<LoggerService>().log(
        '✅ Foreground message simulation completed',
      );
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Foreground message simulation failed: $e',
      );
    }
  }

  /// Test notification permissions
  Future<void> testNotificationPermissions() async {
    try {
      Get.find<LoggerService>().log('🔐 Testing notification permissions...');

      final messaging = FirebaseMessaging.instance;

      // Request permission
      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      Get.find<LoggerService>().log(
        '📱 Permission status: ${settings.authorizationStatus}',
      );
      Get.find<LoggerService>().log('🔔 Alert setting: ${settings.alert}');
      Get.find<LoggerService>().log('🔊 Sound setting: ${settings.sound}');
      Get.find<LoggerService>().log('🏷️ Badge setting: ${settings.badge}');

      Get.find<LoggerService>().log('✅ Permission test completed');
    } catch (e) {
      Get.find<LoggerService>().log('❌ Permission test failed: $e');
    }
  }

  /// Quick test untuk debugging
  Future<void> quickDebugTest() async {
    Get.find<LoggerService>().log('🚀 Quick debug test started');

    final notificationService = NotificationService.instance;
    notificationService.debugNotificationStatus();

    // Test local notification immediately
    await notificationService.sendTestNotification();

    Get.find<LoggerService>().log('🚀 Quick debug test completed');
  }

  /// Test khusus untuk foreground notification issue
  Future<void> testForegroundNotificationIssue() async {
    try {
      Get.find<LoggerService>().log(
        '🔍 Testing foreground notification issue...',
      );

      final notificationService = NotificationService.instance;

      // 1. Check current status
      Get.find<LoggerService>().log('📊 Current Status:');
      notificationService.debugNotificationStatus();

      // 2. Test permissions
      await testNotificationPermissions();

      // 3. Test local notification capability
      Get.find<LoggerService>().log(
        '🧪 Testing local notification capability...',
      );
      await notificationService.sendTestNotification();

      // 4. Simulate foreground message
      Get.find<LoggerService>().log('🔔 Simulating foreground message...');
      await _simulateForegroundMessage();

      // 5. Check if WorkSource warning appears
      Get.find<LoggerService>().log(
        '⚠️ Note: WorkSource warnings are normal and can be ignored',
      );

      Get.find<LoggerService>().log('✅ Foreground notification test completed');
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Foreground notification test failed: $e',
      );
    }
  }

  /// Simulate foreground message dengan detail logging
  Future<void> _simulateForegroundMessage() async {
    try {
      // Create mock message yang mirip dengan real FCM message
      final mockMessage = RemoteMessage(
        messageId: 'test_foreground_${DateTime.now().millisecondsSinceEpoch}',
        data: {
          'test': 'true',
          'type': 'foreground_test',
          'screen': 'home',
          'timestamp': DateTime.now().toIso8601String(),
        },
        notification: const RemoteNotification(
          title: '🧪 Test Foreground Notification',
          body: 'This simulates a real FCM message received while app is open',
          android: AndroidNotification(channelId: 'pdl_superapp_channel'),
        ),
        from: 'test_sender',
        sentTime: DateTime.now(),
        ttl: 3600,
      );

      Get.find<LoggerService>().log('📨 Mock foreground message created:');
      Get.find<LoggerService>().log('  - Message ID: ${mockMessage.messageId}');
      Get.find<LoggerService>().log(
        '  - Title: ${mockMessage.notification?.title}',
      );
      Get.find<LoggerService>().log(
        '  - Body: ${mockMessage.notification?.body}',
      );
      Get.find<LoggerService>().log('  - Data: ${mockMessage.data}');
      Get.find<LoggerService>().log(
        '  - Channel ID: ${mockMessage.notification?.android?.channelId}',
      );

      // Manually trigger the foreground handler
      final notificationService = NotificationService.instance;

      // This simulates what happens when FirebaseMessaging.onMessage.listen triggers
      Get.find<LoggerService>().log(
        '🔔 Triggering foreground message handler...',
      );

      // We can't directly call private method, but we can test the analytics
      notificationService.testAnalyticsTracking(
        'test_foreground_simulation',
        mockMessage,
      );

      Get.find<LoggerService>().log(
        '✅ Foreground message simulation completed',
      );
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Foreground message simulation failed: $e',
      );
    }
  }
}
