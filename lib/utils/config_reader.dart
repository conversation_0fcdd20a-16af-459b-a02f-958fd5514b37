import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:pdl_superapp/flavors.dart';

class ConfigReader {
  static Map<String, dynamic> _config = {};

  static Future<void> initialize() async {
    Flavor flavor = F.appFlavor ?? Flavor.dev;
    String configPath = 'config/development/app_config.json';
    switch (flavor) {
      case Flavor.production:
        configPath = 'config/production/app_config.json';
        break;

      default:
    }
    final configString = await rootBundle.loadString(configPath);
    _config = json.decode(configString) as Map<String, dynamic>;
  }

  static String getBaseUrl() {
    return _config['base_url'] as String;
  }

  static String getPublicUrl() {
    return _config['public_url'] as String;
  }
}
