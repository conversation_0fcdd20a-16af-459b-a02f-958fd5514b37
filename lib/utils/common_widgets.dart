import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class CommonWidgets {
  static Widget profileContentCard(
    BuildContext context, {
    String? iconUrl,
    required String title,
    Widget? suffix, //override suffic widget
    bool? isLast, //override padding bottom to zero
    Color? color, //override icon and text color
    Function()? onPressed,
    EdgeInsets? padding,
  }) {
    return GestureDetector(
      onTap: onPressed ?? () {},
      child: Padding(
        padding:
            padding ??
            EdgeInsets.only(bottom: isLast == true ? 0 : paddingLarge),
        child: Row(
          children: [
            if (iconUrl != null)
              SizedBox(
                width: 24,
                height: 24,
                child: Utils.cachedSvgWrapper(
                  iconUrl,
                  height: 24,
                  color:
                      color ??
                      (Get.isDarkMode ? kColorTextDark : kColorTextLight),
                ),
              ),
            if (iconUrl != null) SizedBox(width: paddingSmall),
            Expanded(
              child: Padding(
                padding: EdgeInsets.zero,
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color:
                        color ??
                        (Get.isDarkMode ? kColorTextDark : kColorTextLight),
                  ),
                ),
              ),
            ),

            suffix ??
                SizedBox(
                  width: 40,
                  child: Utils.cachedSvgWrapper(
                    'icon/ic-linear-chevron right.svg',
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                    height: 24,
                    fit: BoxFit.contain,
                  ),
                ),
          ],
        ),
      ),
    );
  }

  static Widget bottomSheetRadioOptionCard(
    context, {
    required String title,
    Widget? suffix,
    required bool isSelected,
    required Function() onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        width: Get.width,
        padding: EdgeInsets.symmetric(vertical: paddingSmall),
        child: Row(
          children: [
            isSelected
                ? Icon(
                  Icons.radio_button_checked,
                  color: Theme.of(context).colorScheme.primary,
                )
                : Icon(Icons.radio_button_off),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Wrap(
                alignment: WrapAlignment.start,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(title),
                  SizedBox(width: paddingMedium),
                  suffix ?? Container(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget bottomSheetOptionCard(
    context, {
    required String title,
    Widget? suffix,
    Widget? prefix,
    required Function() onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Theme.of(context).colorScheme.surface,
        width: Get.width,
        padding: EdgeInsets.symmetric(vertical: paddingSmall),
        child: Row(
          children: [
            prefix ?? Container(),
            SizedBox(width: paddingMedium),
            Expanded(
              child: Wrap(
                alignment: WrapAlignment.start,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: paddingMedium),
                  suffix ?? Container(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget errorCard(context, {required String content}) {
    return Container(
      width: Get.width,
      margin: EdgeInsets.only(bottom: paddingSmall),
      padding: EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        color: kColorGlobalBgRed,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        content,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: kColorErrorText),
      ),
    );
  }
}
