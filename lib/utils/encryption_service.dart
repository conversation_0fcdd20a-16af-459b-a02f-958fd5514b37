import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart' as crypto;
import 'package:encrypt/encrypt.dart' as encrypt;

class Encryption {
  late Uint8List secretKey;

  Encryption(String secretKeyStr) {
    if (secretKeyStr.isEmpty) {
      throw Exception('Secret key is required');
    }
    secretKey = Uint8List.fromList(utf8.encode(secretKeyStr));
  }

  String doencrypt(String val, String saltData) {
    try {
      int substr = 8;
      // Use exact 8 bytes of salt as in original
      final Uint8List usedSaltData = Uint8List.fromList(
        utf8.encode(saltData),
      ).sublist(0, substr);

      // Generate key and IV using original method
      final Map<String, Uint8List> keyIvMap = generateKeyAndIV(
        32,
        16,
        usedSaltData,
        secretKey,
      );
      final Uint8List key = keyIvMap["key"]!;
      final Uint8List iv = keyIvMap["iv"]!;

      // Create cipher with same parameters
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key(key), mode: encrypt.AESMode.cbc),
      );

      // Encrypt the value
      final Uint8List valArray = Uint8List.fromList(utf8.encode(val));
      final encrypt.Encrypted encrypted = encrypter.encryptBytes(
        valArray,
        iv: encrypt.IV(iv),
      );

      // Construct final buffer exactly as original
      final ByteData fullEncryption = ByteData(16 + encrypted.bytes.length);

      // Fill salt at the right position (8-16)
      for (int i = 0; i < substr; i++) {
        fullEncryption.setUint8(i + substr, usedSaltData[i]);
      }

      // Place encrypted data after salt
      for (int i = 0; i < encrypted.bytes.length; i++) {
        fullEncryption.setUint8(16 + i, encrypted.bytes[i]);
      }

      // Convert to Uint8List for base64 encoding
      final Uint8List resultBytes = Uint8List.view(fullEncryption.buffer);
      return base64.encode(resultBytes);
    } catch (error) {
      // print('Encryption error: $error');
      return "";
    }
  }

  String decrypt(String encryptedVal) {
    try {
      // Decode base64
      final Uint8List fullEncryption = base64.decode(encryptedVal);

      // Extract salt from position 8-16
      final Uint8List usedSaltData = fullEncryption.sublist(8, 16);

      // Extract encrypted data
      final Uint8List encrypted = fullEncryption.sublist(16);

      // Generate key and IV using same method as encryption
      final Map<String, Uint8List> keyIvMap = generateKeyAndIV(
        32,
        16,
        usedSaltData,
        secretKey,
      );
      final Uint8List key = keyIvMap["key"]!;
      final Uint8List iv = keyIvMap["iv"]!;

      // Create decipher
      final encrypter = encrypt.Encrypter(
        encrypt.AES(encrypt.Key(key), mode: encrypt.AESMode.cbc),
      );

      // Decrypt
      final List<int> decrypted = encrypter.decryptBytes(
        encrypt.Encrypted(encrypted),
        iv: encrypt.IV(iv),
      );

      return utf8.decode(decrypted);
    } catch (error) {
      // print('Decryption error: $error');
      return "";
    }
  }

  Map<String, Uint8List> generateKeyAndIV(
    int keyLength,
    int ivLength,
    Uint8List saltData,
    Uint8List password,
  ) {
    final int digestLength = 16;
    final int requiredLength =
        ((keyLength + ivLength + digestLength - 1) ~/ digestLength) *
        digestLength;
    final Uint8List generatedData = Uint8List(requiredLength);
    int generatedLength = 0;

    try {
      while (generatedLength < keyLength + ivLength) {
        final crypto.Hash md5 = crypto.md5;
        final List<int> dataToHash = [];

        if (generatedLength > 0) {
          final Uint8List tempGenerated = generatedData.sublist(
            generatedLength - digestLength,
            generatedLength,
          );
          dataToHash.addAll(tempGenerated);
        }

        dataToHash.addAll(password);
        dataToHash.addAll(saltData);

        final Uint8List temp = Uint8List.fromList(
          md5.convert(dataToHash).bytes,
        );

        for (
          int i = 0;
          i < digestLength && generatedLength + i < keyLength + ivLength;
          i++
        ) {
          generatedData[generatedLength + i] = temp[i];
        }
        generatedLength += digestLength;
      }

      final Map<String, Uint8List> keyIv = {
        "key": generatedData.sublist(0, keyLength),
        "iv": generatedData.sublist(keyLength, keyLength + ivLength),
      };

      return keyIv;
    } catch (error) {
      // print("Key/IV generation error: $error");
      rethrow;
    }
  }
}
