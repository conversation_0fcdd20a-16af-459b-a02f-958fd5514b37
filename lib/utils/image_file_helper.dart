import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';

/// Helper class for handling image files across platforms
class ImageFileHelper {
  /// Convert XFile to compatible format for the platform
  /// On web, always returns XFile; on mobile, returns as-is (File or XFile)
  static dynamic convertToCompatibleFile(dynamic imageFile) {
    if (kIsWeb) {
      // On web, ensure we have XFile
      if (imageFile is XFile) {
        return imageFile;
      } else {
        throw Exception(
          'Web platform requires XFile, got ${imageFile.runtimeType}',
        );
      }
    } else {
      // On mobile, return as-is (let the caller handle File conversion)
      return imageFile;
    }
  }

  /// Get image bytes from either File or XFile
  static Future<Uint8List> getImageBytes(dynamic imageFile) async {
    if (imageFile is XFile) {
      return await imageFile.readAsBytes();
    } else if (kIsWeb) {
      // On web, if we somehow get a non-XFile object, it's an error
      throw Exception(
        'Web platform requires XFile, got ${imageFile.runtimeType}',
      );
    } else {
      // For mobile File objects, use readAsBytes method
      try {
        return await imageFile.readAsBytes();
      } catch (e) {
        throw Exception('Failed to read image bytes: $e');
      }
    }
  }

  /// Get image path/name for logging purposes
  static String getImagePath(dynamic imageFile) {
    if (imageFile is XFile) {
      return imageFile.name;
    } else {
      // Try to get path property
      try {
        return imageFile.path ?? 'Unknown path';
      } catch (e) {
        return 'Unknown file type: ${imageFile.runtimeType}';
      }
    }
  }

  /// Check if the image file is valid
  static bool isValidImageFile(dynamic imageFile) {
    if (imageFile is XFile) {
      return true;
    }

    // Check if it has readAsBytes method (File-like object)
    try {
      return imageFile.readAsBytes != null;
    } catch (e) {
      return false;
    }
  }

  /// Safely delete image file across platforms
  /// On web (XFile), this is a no-op since XFile doesn't support delete
  /// On mobile (File), this calls the delete method
  static Future<void> safeDeleteImageFile(dynamic imageFile) async {
    if (imageFile == null) return;

    try {
      if (kIsWeb) {
        // On web, XFile doesn't have delete method, so we just clear the reference
        // The browser will handle garbage collection
        return;
      } else {
        // On mobile, try to delete if it's a File object
        if (imageFile.delete != null) {
          await imageFile.delete();
        }
      }
    } catch (e) {
      // Silently ignore delete errors as they're not critical
      // The file system will handle cleanup eventually
    }
  }
}
