import 'dart:developer' as dev;
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A centralized logging service that allows enabling/disabling logs globally
class LoggerService extends GetxService {
  // Observable boolean to track if logging is enabled
  final RxBool _isLoggingEnabled = true.obs;

  // SharedPreferences key for storing logging preference
  static const String _loggingEnabledKey = 'logging_enabled';

  // Getter for logging state
  bool get isLoggingEnabled => _isLoggingEnabled.value;

  /// Initialize the logger service and load saved preferences
  Future<LoggerService> init() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Load saved preference, default to true if not found
      _isLoggingEnabled.value = prefs.getBool(_loggingEnabledKey) ?? true;

      // Log the initial state (this will only show if logging is enabled)
      log(
        'LoggerService initialized. Logging is ${_isLoggingEnabled.value ? 'enabled' : 'disabled'}',
      );
    } catch (e) {
      // If there's an error, default to enabled
      _isLoggingEnabled.value = true;
      // Use dev.log directly here since our log method might not work during initialization
      dev.log('Error initializing LoggerService: $e');
    }
    return this;
  }

  /// Log a message if logging is enabled
  void log(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
  }) {
    if (_isLoggingEnabled.value) {
      dev.log(
        message,
        name: name ?? 'PDL_SUPERAPP',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Enable logging globally and save preference
  Future<void> enableLogging() async {
    _isLoggingEnabled.value = true;
    await _saveLoggingPreference(true);
    log('Logging enabled');
  }

  /// Disable logging globally and save preference
  Future<void> disableLogging() async {
    // Log before disabling
    log('Logging disabled');
    _isLoggingEnabled.value = false;
    await _saveLoggingPreference(false);
  }

  /// Toggle logging state and save preference
  Future<bool> toggleLogging() async {
    final newState = !_isLoggingEnabled.value;

    if (newState) {
      // Enable logging first so we can log the state change
      _isLoggingEnabled.value = true;
      log('Logging toggled to enabled');
    } else {
      // Log before disabling
      log('Logging toggled to disabled');
    }

    _isLoggingEnabled.value = newState;
    await _saveLoggingPreference(newState);
    return newState;
  }

  /// Save logging preference to SharedPreferences
  Future<void> _saveLoggingPreference(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_loggingEnabledKey, enabled);
    } catch (e) {
      // Use dev.log directly here since our log method might not work
      dev.log('Error saving logging preference: $e');
    }
  }
}
