import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Comprehensive Analytics Utility for Firebase Analytics
/// Provides reusable methods for tracking pages, events, and user interactions
class AnalyticsUtils {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static final FirebaseAnalyticsObserver _observer = FirebaseAnalyticsObserver(
    analytics: _analytics,
  );

  /// Get the analytics observer for navigation tracking
  static FirebaseAnalyticsObserver get observer => _observer;

  /// Initialize analytics (basic initialization without user data)
  static Future<void> initialize() async {
    try {
      // Enable debug mode for Firebase Analytics in debug builds
      if (kDebugMode) {
        await _analytics.setAnalyticsCollectionEnabled(true);

        // For web, also enable debug mode via gtag if available
        if (kIsWeb) {
          _enableWebDebugMode();
        }
      }
    } catch (e) {
      // Silently handle initialization errors
    }
  }

  /// Enable debug mode for web using gtag
  static void _enableWebDebugMode() {
    try {
      // Enable debug mode for web analytics
      if (kIsWeb) {
        // Web debug mode enabled silently
      }
    } catch (e) {
      // Silently handle web debug mode setup errors
    }
  }

  /// Check if running on web platform
  static bool get isWeb => kIsWeb;

  /// Get platform-specific information
  static Map<String, String> getPlatformInfo() {
    return {
      'platform': kIsWeb ? 'web' : 'mobile',
      'is_debug': kDebugMode.toString(),
      if (kIsWeb) 'user_agent': _getUserAgent(),
      if (kIsWeb) 'url': Uri.base.toString(),
    };
  }

  /// Get user agent for web
  static String _getUserAgent() {
    if (kIsWeb) {
      try {
        // This will work in web environment
        return 'web_browser';
      } catch (e) {
        return 'unknown_browser';
      }
    }
    return 'mobile_app';
  }

  /// Initialize analytics with user data after login
  static Future<void> initializeWithUser({
    String? userId,
    Map<String, String>? userProperties,
  }) async {
    try {
      if (userId != null && userId.isNotEmpty) {
        await _analytics.setUserId(id: userId);
      }

      if (userProperties != null && userProperties.isNotEmpty) {
        for (final entry in userProperties.entries) {
          await _analytics.setUserProperty(name: entry.key, value: entry.value);
        }
      }

      // Enable debug view in debug mode
      if (kDebugMode) {
        await enableDebugView();
      }
    } catch (e) {
      // Silently handle user initialization errors
    }
  }

  /// Enable Firebase Analytics debug view
  static Future<void> enableDebugView() async {
    try {
      // Set debug mode property
      await _analytics.setUserProperty(name: 'debug_mode', value: 'true');

      // Log a debug event to verify debug view is working
      await _analytics.logEvent(
        name: 'debug_view_enabled',
        parameters: {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'platform': kIsWeb ? 'web' : 'mobile',
          'app_version': '1.0.0',
        },
      );

      // Debug view enabled silently
    } catch (e) {
      // Silently handle debug view errors
    }
  }

  /// Track page views with automatic route detection
  static Future<void> trackPageView({
    String? pageName,
    String? pageClass,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final currentRoute = Get.currentRoute;
      // Always use route-based page name for consistency
      final cleanPageName = _cleanRouteName(currentRoute);

      final eventParameters = <String, Object>{
        'page_name': cleanPageName,
        'page_class': pageClass ?? cleanPageName,
        'route': currentRoute,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        // Add platform-specific information
        ...getPlatformInfo(),
        if (parameters != null)
          ...parameters.map((k, v) => MapEntry(k, _sanitizeParameterValue(v))),
      };

      await _analytics.logScreenView(
        screenName: cleanPageName, // Use route-based name, not app name
        screenClass: cleanPageName,
        parameters: eventParameters,
      );

      // Page view tracked silently
    } catch (e) {
      // Silently handle page view tracking errors
    }
  }

  /// Track user interactions and clicks
  static Future<void> trackEvent({
    required String eventName,
    String? category,
    String? action,
    String? label,
    int? value,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        if (category != null) 'category': category,
        if (action != null) 'action': action,
        if (label != null) 'label': label,
        if (value != null) 'value': value,
        'page': _cleanRouteName(Get.currentRoute),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (parameters != null)
          ...parameters.map((k, v) => MapEntry(k, _sanitizeParameterValue(v))),
      };

      await _analytics.logEvent(
        name: _sanitizeEventName(eventName),
        parameters: eventParameters,
      );

      // Event tracked silently
    } catch (e) {
      // Silently handle event tracking errors
    }
  }

  /// Track button clicks with automatic context detection
  static Future<void> trackButtonClick({
    required String buttonName,
    String? buttonType,
    String? section,
    Map<String, dynamic>? additionalData,
  }) async {
    final currentRoute = Get.currentRoute;
    final cleanPageName = _cleanRouteName(currentRoute);

    await trackEvent(
      eventName: 'button_click',
      category: 'user_interaction',
      action: 'click',
      label: buttonName,
      parameters: {
        'button_name': buttonName,
        'button_type': buttonType ?? 'unknown',
        'section': section ?? 'unknown',
        'page_name': cleanPageName,
        'full_route': currentRoute,
        ...?additionalData,
      },
    );
  }

  /// Enhanced button click tracking with automatic page context
  /// This is the main method to use for all button tracking
  static Future<void> trackButtonAction({
    required String buttonName,
    String? buttonType,
    String? section,
    String? action,
    Map<String, dynamic>? additionalData,
  }) async {
    final currentRoute = Get.currentRoute;
    final cleanPageName = _cleanRouteName(currentRoute);

    await trackEvent(
      eventName: 'button_action',
      category: 'user_interaction',
      action: action ?? 'click',
      label: '$cleanPageName.$buttonName',
      parameters: {
        'button_name': buttonName,
        'button_type': buttonType ?? 'unknown',
        'section': section ?? 'unknown',
        'page_name': cleanPageName,
        'full_route': currentRoute,
        'action_type': action ?? 'click',
        'button_context': '$cleanPageName.$buttonName',
        ...?additionalData,
      },
    );
  }

  /// Track any clickable element (buttons, cards, list items, etc.)
  static Future<void> trackElementClick({
    required String elementName,
    String? elementType,
    String? section,
    String? action,
    Map<String, dynamic>? additionalData,
  }) async {
    final currentRoute = Get.currentRoute;
    final cleanPageName = _cleanRouteName(currentRoute);

    await trackEvent(
      eventName: 'element_click',
      category: 'user_interaction',
      action: action ?? 'tap',
      label: '$cleanPageName.$elementName',
      parameters: {
        'element_name': elementName,
        'element_type': elementType ?? 'unknown',
        'section': section ?? 'unknown',
        'page_name': cleanPageName,
        'full_route': currentRoute,
        'action_type': action ?? 'tap',
        'element_context': '$cleanPageName.$elementName',
        ...?additionalData,
      },
    );
  }

  /// Track navigation events with enhanced context
  static Future<void> trackNavigation({
    required String from,
    required String to,
    String? method,
    String? triggerElement,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'navigation',
      category: 'navigation',
      action: method ?? 'navigate',
      parameters: {
        'from_page': _cleanRouteName(from),
        'to_page': _cleanRouteName(to),
        'navigation_method': method ?? 'unknown',
        'trigger_element': triggerElement,
        ...?additionalData,
      },
    );
  }

  /// Track tab/section changes within a page
  static Future<void> trackTabChange({
    required String fromTab,
    required String toTab,
    String? section,
    Map<String, dynamic>? additionalData,
  }) async {
    final currentRoute = Get.currentRoute;
    final cleanPageName = _cleanRouteName(currentRoute);

    await trackEvent(
      eventName: 'tab_change',
      category: 'user_interaction',
      action: 'tab_switch',
      label: '$cleanPageName.$toTab',
      parameters: {
        'from_tab': fromTab,
        'to_tab': toTab,
        'page_name': cleanPageName,
        'section': section ?? 'unknown',
        'full_route': currentRoute,
        ...?additionalData,
      },
    );
  }

  /// Track dropdown/selection changes
  static Future<void> trackSelectionChange({
    required String fieldName,
    required String selectedValue,
    String? previousValue,
    String? section,
    Map<String, dynamic>? additionalData,
  }) async {
    final currentRoute = Get.currentRoute;
    final cleanPageName = _cleanRouteName(currentRoute);

    await trackEvent(
      eventName: 'selection_change',
      category: 'user_interaction',
      action: 'select',
      label: '$cleanPageName.$fieldName',
      parameters: {
        'field_name': fieldName,
        'selected_value': selectedValue,
        'previous_value': previousValue,
        'page_name': cleanPageName,
        'section': section ?? 'unknown',
        'full_route': currentRoute,
        ...?additionalData,
      },
    );
  }

  /// Track form interactions
  static Future<void> trackFormEvent({
    required String formName,
    required String action, // 'start', 'submit', 'error', 'abandon'
    String? fieldName,
    String? errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'form_$action',
      category: 'form_interaction',
      action: action,
      label: formName,
      parameters: {
        'form_name': formName,
        'form_action': action,
        if (fieldName != null) 'field_name': fieldName,
        if (errorMessage != null) 'error_message': errorMessage,
        ...?additionalData,
      },
    );
  }

  /// Track search events
  static Future<void> trackSearch({
    required String searchTerm,
    String? searchCategory,
    int? resultCount,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'search',
      category: 'search',
      action: 'search_performed',
      label: searchTerm,
      parameters: {
        'search_term': searchTerm,
        'search_category': searchCategory ?? 'general',
        if (resultCount != null) 'result_count': resultCount,
        ...?additionalData,
      },
    );
  }

  /// Track API calls and performance
  static Future<void> trackApiCall({
    required String endpoint,
    required String method,
    required int statusCode,
    int? duration,
    String? errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'api_call',
      category: 'api',
      action: method.toLowerCase(),
      label: endpoint,
      value: duration,
      parameters: {
        'endpoint': endpoint,
        'method': method,
        'status_code': statusCode,
        'success': statusCode >= 200 && statusCode < 300,
        if (duration != null) 'duration_ms': duration,
        if (errorMessage != null) 'error_message': errorMessage,
        ...?additionalData,
      },
    );
  }

  /// Track user engagement events
  static Future<void> trackEngagement({
    required String engagementType, // 'scroll', 'time_on_page', 'feature_use'
    String? feature,
    int? value,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'user_engagement',
      category: 'engagement',
      action: engagementType,
      label: feature,
      value: value,
      parameters: {
        'engagement_type': engagementType,
        if (feature != null) 'feature': feature,
        ...?additionalData,
      },
    );
  }

  /// Track errors and exceptions
  static Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
    bool fatal = false,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackEvent(
      eventName: 'error_occurred',
      category: 'error',
      action: errorType,
      label: errorMessage,
      parameters: {
        'error_type': errorType,
        'error_message': errorMessage,
        'fatal': fatal,
        if (stackTrace != null) 'stack_trace': stackTrace,
        ...?additionalData,
      },
    );
  }

  /// Set user properties
  static Future<void> setUserProperty({
    required String name,
    required String value,
  }) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
    } catch (e) {
      // Silently handle user property errors
    }
  }

  /// Clean route name for analytics
  static String _cleanRouteName(String route) {
    // Remove parameters and query strings
    String cleaned = route.split('?').first.split('#').first;

    // Replace parameter placeholders with generic names
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/[a-f0-9-]{30,}'), // UUID patterns
      (match) => '_uuid',
    );
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/\d+'), // Numeric IDs
      (match) => '_id',
    );
    cleaned = cleaned.replaceAllMapped(
      RegExp(r'/:[\w]+'), // Route parameters like /:id, /:uuid
      (match) => '_param',
    );

    // Remove leading slash and replace remaining slashes with underscores
    if (cleaned.startsWith('/')) cleaned = cleaned.substring(1);
    cleaned = cleaned.replaceAll('/', '_');

    // Handle special cases
    if (cleaned.isEmpty || cleaned == '/' || cleaned == '') return 'home';
    if (cleaned == 'login') return 'login';
    if (cleaned == 'main') return 'main';

    // Ensure valid analytics name (alphanumeric and underscores only)
    cleaned = cleaned.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_');
    cleaned = cleaned.replaceAll(
      RegExp(r'_+'),
      '_',
    ); // Remove multiple underscores
    cleaned = cleaned.replaceAll(
      RegExp(r'^_|_$'),
      '',
    ); // Remove leading/trailing underscores

    return cleaned.isEmpty ? 'unknown_page' : cleaned;
  }

  /// Sanitize event names for Firebase Analytics
  static String _sanitizeEventName(String eventName) {
    // Firebase Analytics event names must be alphanumeric with underscores
    return eventName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9_]'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');
  }

  /// Sanitize parameter values for Firebase Analytics
  /// Firebase Analytics only accepts String or num values
  static Object _sanitizeParameterValue(dynamic value) {
    if (value == null) return 'null';
    if (value is String || value is num) return value;
    if (value is bool) return value.toString();
    if (value is DateTime) return value.millisecondsSinceEpoch;
    if (value is List) return value.length;
    if (value is Map) return value.length;
    return value.toString();
  }
}
