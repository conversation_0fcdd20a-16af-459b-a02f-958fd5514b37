import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:pdl_superapp/utils/themes/theme_primary.dart';

class ThemeServices {
  static const String primaryTheme = 'Primary';
  static const String christmassTheme = 'Christmass';

  final _getStorage = GetStorage();
  final _storageKey = 'ThemeMode';
  static ThemeServices instance = ThemeServices._();

  ThemeServices._();
  set themeMode(ThemeMode themeMode) {
    if (themeMode == ThemeMode.system) {
      _getStorage.remove(_storageKey);
    } else {
      _getStorage.write(_storageKey, themeMode == ThemeMode.light);
    }
    Get.changeThemeMode(themeMode);
  }

  ThemeMode get themeMode {
    switch (_getStorage.read(_storageKey)) {
      case true:
        return ThemeMode.dark;
      case false:
        return ThemeMode.light;
      default:
        return ThemeMode.light;
    }
  }

  // Get the theme by name and handle light and dark mode
  ThemeData getTheme(String name, {bool isDark = false}) {
    ThemeData currTheme = ThemeData();
    switch (name) {
      case primaryTheme:
        currTheme = isDark ? ThemePrimary.dark : ThemePrimary.light;
        break;

      default:
    }
    return currTheme;
  }
}
