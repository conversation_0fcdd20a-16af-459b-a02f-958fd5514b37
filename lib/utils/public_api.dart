import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// API Service khusus untuk endpoint public yang tidak memerlukan authentication
/// Tidak mengirimkan Authorization header
String baseUrl = ConfigReader.getBaseUrl();

class PublicApi extends GetConnect {
  @override
  void onInit() async {
    httpClient.baseUrl = baseUrl;
    httpClient.timeout = const Duration(seconds: 15);
    setupRequestModifier();
  }

  setupRequestModifier() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    httpClient.addRequestModifier((Request request) {
      String deviceId = prefs.getString('device_id') ?? '';

      // Tidak menambahkan Authorization header untuk public API
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      if (deviceId != '') {
        request.headers['device_id'] = deviceId;
      }

      _print(
        doPrint: true,
        data: 'Public API Request Headers: ${request.headers}',
      );
      return request;
    });
  }

  void _print({required bool doPrint, required dynamic data}) {
    if (doPrint) {
      log(data.toString());
    }
  }

  /// Public API GET request
  Future<void> publicApiFetch({
    required String url,
    required BaseControllers controller,
    int code = 0,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // final hasConnection = await _checkInternetConnection();

      // if (!hasConnection) {
      //   Get.find<LoggerService>().log("tidak ada koneksi");
      //   return controller.loadError(
      //     "No internet connection. Please check your connection and try again.",
      //   );
      // }

      _print(doPrint: debug, data: 'Public API URL: $url');

      final response = await get(url);

      _print(
        doPrint: debug,
        data: 'Public API Response: ${response.body ?? {}}',
      );

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Public API Error: $url');
        return controller.loadFailed(requestCode: code, response: response);
      }

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log('Public API Error: $e');
      return controller.loadError(e);
    }
  }

  /// Public API POST request
  Future<void> publicApiPost({
    required String url,
    required BaseControllers controller,
    required var data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      // final hasConnection = await _checkInternetConnection();

      // if (!hasConnection) {
      //   Get.find<LoggerService>().log("tidak ada koneksi");
      //   return controller.loadError(
      //     "No internet connection. Cannot perform operation offline.",
      //   );
      // }

      _print(doPrint: debug, data: 'Public API POST URL: $url');
      _print(doPrint: debug, data: 'Public API POST Data: $data');

      final response = await post(
        url,
        data,
        headers: {'Content-Type': 'application/json'},
      );

      _print(
        doPrint: debug,
        data: 'Public API POST Response: ${response.body ?? {}}',
      );

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Public API POST Error: $url');
        return controller.loadFailed(requestCode: code, response: response);
      }

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log('Public API POST Error: $e');
      return controller.loadError(e);
    }
  }

  // Public API endpoints
  final String _publicRecruitment = '$baseUrl/public/agency/recruitment';
  final String _publicAgency = '$baseUrl/public/agency';
  final String _bank = '$baseUrl/public/bank';

  Future<Map<String, dynamic>?> uploadRecruitmentImage({
    File? imageFile,
    Uint8List? imageBytes,
    String? fileName,
    required String type,
    Function(double)? onProgress,
  }) async {
    try {
      MultipartFile multipartFile;

      // Handle different input types for mobile vs web
      if (kIsWeb && imageBytes != null) {
        // For web: use bytes data
        multipartFile = MultipartFile(
          imageBytes,
          filename: fileName ?? '${Utils.getRandomString()}.jpg',
        );
      } else if (imageFile != null) {
        // For mobile: use File object
        multipartFile = MultipartFile(
          imageFile,
          filename: '${Utils.getRandomString()}.jpg',
        );
      } else {
        return {
          'success': false,
          'url': null,
          'message': 'No image data provided',
        };
      }

      // Create form data
      final form = FormData({'file': multipartFile});

      // Upload to API
      final response = await GetConnect().post(
        '$_publicAgency/upload/$type',
        form,
        uploadProgress: (percent) {
          if (onProgress != null) {
            onProgress(percent);
          }
        },
      );

      log('Upload response: ${response.body}');

      if (response.isOk && response.body != null) {
        // Parse response to get URL
        final responseData = response.body;
        String? uploadedUrl;

        // Handle different response formats
        if (responseData is Map<String, dynamic>) {
          uploadedUrl = response.body['initialPreview'][0];
        } else if (responseData is String) {
          uploadedUrl = response.body['initialPreview'][0];
        }

        if (uploadedUrl != null && uploadedUrl.isNotEmpty) {
          log("Upload berhasil untuk $type: $uploadedUrl");
          return {
            'success': true,
            'url': uploadedUrl,
            'message': 'Upload berhasil',
          };
        } else {
          log("Upload gagal: URL tidak ditemukan dalam response");
          return {
            'success': false,
            'url': null,
            'message': 'URL tidak ditemukan dalam response',
          };
        }
      } else {
        log("Upload gagal: ${response.statusText}");
        return {
          'success': false,
          'url': null,
          'message': response.statusText ?? 'Upload gagal',
        };
      }
    } catch (e) {
      log("Error saat upload gambar $type: $e");
      return {'success': false, 'url': null, 'message': 'Error: $e'};
    }
  }

  Future<void> performSubmitRecruitmentForm({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await publicApiPost(
      url: '$_publicRecruitment/submit',
      controller: controllers,
      data: data,
      debug: true,
      code: code ?? 0,
    );
  }

  // Get Bank data
  Future<void> getBank({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await publicApiFetch(
      url: '$_bank?$params',
      controller: controllers,
      code: code ?? 0,
      debug: true,
    );
  }

  // Get Branch data
  Future<void> getBranch({
    required BaseControllers controllers,
    String? params,
    int? code,
  }) async {
    await publicApiFetch(
      url: '$baseUrl/public/branch?$params',
      controller: controllers,
      code: code ?? 0,
      debug: true,
    );
  }
}
