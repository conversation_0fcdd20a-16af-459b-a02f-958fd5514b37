import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:pdl_superapp/utils/image_file_helper.dart';

/// Helper class for checking image quality before OCR processing
class ImageQualityChecker {
  /// Minimum blur threshold - images below this value are considered too blurry
  /// Higher values = more strict blur detection
  static const double _minBlurThreshold = 100.0;

  /// Minimum brightness threshold (0-255)
  static const double _minBrightness = 50.0;

  /// Maximum brightness threshold (0-255)
  static const double _maxBrightness = 200.0;

  /// Check if image quality is sufficient for OCR processing
  /// Returns a [ImageQualityResult] with quality assessment
  static Future<ImageQualityResult> checkImageQuality(dynamic imageFile) async {
    try {
      final bytes = await ImageFileHelper.getImageBytes(imageFile);
      final image = img.decodeImage(bytes);

      if (image == null) {
        return ImageQualityResult(
          isGoodQuality: false,
          blurScore: 0.0,
          brightness: 0.0,
          issues: ['Gagal memproses gambar'],
        );
      }

      // Check blur using Laplacian variance
      final blurScore = _calculateBlurScore(image);

      // Check brightness
      final brightness = _calculateBrightness(image);

      // Determine quality issues
      final issues = <String>[];

      if (blurScore < _minBlurThreshold) {
        issues.add('Foto terlalu buram');
      }

      if (brightness < _minBrightness) {
        issues.add('Foto terlalu gelap');
      } else if (brightness > _maxBrightness) {
        issues.add('Foto terlalu terang');
      }

      final isGoodQuality = issues.isEmpty;

      if (kDebugMode) {
        print('=== Image Quality Check ===');
        print(
          'Blur Score: ${blurScore.toStringAsFixed(2)} (min: $_minBlurThreshold)',
        );
        print(
          'Brightness: ${brightness.toStringAsFixed(2)} (range: $_minBrightness-$_maxBrightness)',
        );
        print('Quality: ${isGoodQuality ? 'GOOD' : 'POOR'}');
        if (issues.isNotEmpty) {
          print('Issues: ${issues.join(', ')}');
        }
        print('========================');
      }

      return ImageQualityResult(
        isGoodQuality: isGoodQuality,
        blurScore: blurScore,
        brightness: brightness,
        issues: issues,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error checking image quality: $e');
      }
      return ImageQualityResult(
        isGoodQuality: false,
        blurScore: 0.0,
        brightness: 0.0,
        issues: ['Error saat memeriksa kualitas gambar'],
      );
    }
  }

  /// Calculate blur score using Laplacian variance
  /// Higher values indicate sharper images
  static double _calculateBlurScore(img.Image image) {
    // Convert to grayscale for blur detection
    final grayImage = img.grayscale(image);

    // Apply Laplacian kernel for edge detection
    final laplacianKernel = [
      [0, -1, 0],
      [-1, 4, -1],
      [0, -1, 0],
    ];

    double variance = 0.0;
    double mean = 0.0;
    int pixelCount = 0;

    // Apply Laplacian filter
    for (int y = 1; y < grayImage.height - 1; y++) {
      for (int x = 1; x < grayImage.width - 1; x++) {
        double laplacianValue = 0.0;

        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final pixel = grayImage.getPixel(x + kx - 1, y + ky - 1);
            final gray = img.getLuminance(pixel);
            laplacianValue += gray * laplacianKernel[ky][kx];
          }
        }

        mean += laplacianValue;
        pixelCount++;
      }
    }

    if (pixelCount > 0) {
      mean /= pixelCount;

      // Calculate variance
      for (int y = 1; y < grayImage.height - 1; y++) {
        for (int x = 1; x < grayImage.width - 1; x++) {
          double laplacianValue = 0.0;

          for (int ky = 0; ky < 3; ky++) {
            for (int kx = 0; kx < 3; kx++) {
              final pixel = grayImage.getPixel(x + kx - 1, y + ky - 1);
              final gray = img.getLuminance(pixel);
              laplacianValue += gray * laplacianKernel[ky][kx];
            }
          }

          variance += (laplacianValue - mean) * (laplacianValue - mean);
        }
      }

      variance /= pixelCount;
    }

    return variance;
  }

  /// Calculate average brightness of the image
  static double _calculateBrightness(img.Image image) {
    double totalBrightness = 0.0;
    int pixelCount = 0;

    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final brightness = img.getLuminance(pixel);
        totalBrightness += brightness;
        pixelCount++;
      }
    }

    return pixelCount > 0 ? totalBrightness / pixelCount : 0.0;
  }
}

/// Result of image quality assessment
class ImageQualityResult {
  final bool isGoodQuality;
  final double blurScore;
  final double brightness;
  final List<String> issues;

  const ImageQualityResult({
    required this.isGoodQuality,
    required this.blurScore,
    required this.brightness,
    required this.issues,
  });

  /// Get user-friendly quality message
  String get qualityMessage {
    if (isGoodQuality) {
      return 'Kualitas foto baik';
    }

    if (issues.length == 1) {
      return issues.first;
    }

    return 'Kualitas foto kurang baik: ${issues.join(', ')}';
  }

  /// Get suggestion for improving photo quality
  String get improvementSuggestion {
    if (isGoodQuality) {
      return '';
    }

    final suggestions = <String>[];

    if (issues.contains('Foto terlalu buram')) {
      suggestions.add('Pastikan kamera fokus dan tangan tidak bergetar');
    }

    if (issues.contains('Foto terlalu gelap')) {
      suggestions.add('Gunakan pencahayaan yang lebih terang');
    }

    if (issues.contains('Foto terlalu terang')) {
      suggestions.add('Kurangi pencahayaan atau hindari cahaya langsung');
    }

    return suggestions.join('. ');
  }
}
