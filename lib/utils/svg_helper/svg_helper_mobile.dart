import 'package:flutter/material.dart';
import 'package:cached_network_svg_image/cached_network_svg_image.dart';

/// Mobile-specific SVG helper that uses CachedNetworkSVGImage
/// for better caching performance on mobile platforms
Widget buildCachedNetworkSvg(
  String url, {
  double? width,
  double? height,
  Color? color,
  BoxFit? fit,
  Widget? errorWidget,
}) {
  return CachedNetworkSVGImage(
    url,
    width: width,
    height: height,
    // ignore: deprecated_member_use
    color: color,
    fit: fit ?? BoxFit.contain,
    errorWidget: errorWidget,
  );
}
