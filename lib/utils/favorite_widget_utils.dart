import 'dart:developer';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pdl_superapp/controllers/home_controller.dart';

class FavoriteWidgetUtils {
  // Store the last known list of favorite widget IDs for comparison
  static List<String> _lastKnownFavoriteIds = [];

  /// Check if favorite widgets have changed in SharedPreferences
  /// and notify HomeController if it exists
  static Future<void> checkForFavoriteWidgetChanges() async {
    try {
      // Try to get HomeController if it exists
      HomeController? homeController;
      try {
        // First try to find with 'home' tag (from HomePage)
        homeController = Get.find<HomeController>(tag: 'home');
      } catch (e) {
        try {
          // Fallback: try to find without tag (from other places like DummyPage)
          homeController = Get.find<HomeController>();
        } catch (e2) {
          log(
            'HomeController not found with or without tag, skipping favorite widget change check',
          );
          return;
        }
      }

      // Get current favorite widget IDs from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      List<String> currentIds = prefs.getStringList('favorite_widgets') ?? [];

      log(
        'Checking favorite widget changes: last known: $_lastKnownFavoriteIds, current: $currentIds',
      );

      // Check if the list has changed
      if (!_areListsEqual(_lastKnownFavoriteIds, currentIds)) {
        log('Favorite widgets changed, updating HomeController');
        // Update the last known list
        _lastKnownFavoriteIds = List.from(currentIds);

        // Notify HomeController to reload widgets
        homeController.loadFavoriteWidgets();
      } else {
        log('No changes in favorite widgets');
      }
    } catch (e) {
      log('Error checking for favorite widget changes: $e');
    }
  }

  /// Helper method to compare two lists
  static bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  /// Update the last known favorite IDs (useful for initialization)
  static void updateLastKnownFavoriteIds(List<String> ids) {
    _lastKnownFavoriteIds = List.from(ids);
  }

  /// Get the last known favorite IDs
  static List<String> getLastKnownFavoriteIds() {
    return List.from(_lastKnownFavoriteIds);
  }

  /// Force reload favorite widgets in HomeController if it exists
  static void forceReloadFavoriteWidgets() {
    try {
      HomeController? homeController;
      try {
        // First try to find with 'home' tag (from HomePage)
        homeController = Get.find<HomeController>(tag: 'home');
      } catch (e) {
        try {
          // Fallback: try to find without tag (from other places like DummyPage)
          homeController = Get.find<HomeController>();
        } catch (e2) {
          log(
            'HomeController not found with or without tag, skipping force reload',
          );
          return;
        }
      }

      // Clear the last known list to force a reload
      _lastKnownFavoriteIds.clear();

      // Load favorite widgets
      homeController.loadFavoriteWidgets();
    } catch (e) {
      log('Error force reloading favorite widgets: $e');
    }
  }
}
