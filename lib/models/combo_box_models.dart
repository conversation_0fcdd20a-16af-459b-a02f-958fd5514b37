class ComboBoxKeyModels {
  String? key;
  String? value;

  ComboBoxKeyModels({this.key, this.value});

  ComboBoxKeyModels.fromJSon(Map json) {
    key = json['key'];
    value = json['value'];
  }
}

class ComboBoxModels {
  String? key;
  String? value;
  String? value2;
  String? value3;

  ComboBoxModels({this.key, this.value, this.value2, this.value3});

  ComboBoxModels.fromJSon(Map json) {
    key = json['key'];
    value = json['value'];
    value2 = json['value2'];
    value3 = json['value3'];
  }
}

class ComboBoxValueModels extends ComboBoxModels {
  ComboBoxValueModels({
    required super.key,
    required super.value,
    required super.value2,
    required super.value3,
  });

  factory ComboBoxValueModels.fromJSon(Map json) {
    return ComboBoxValueModels(
      key: json['key'],
      value: json['value'],
      value2: json['value2'],
      value3: json['value3'],
    );
  }
}
