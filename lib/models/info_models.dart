class InfoModels {
  int? id;
  String? title;
  String? content;
  String? externalLink;
  bool? isActive;
  String? informationType;
  String? accessLevel;
  String? createdAt;
  String? updatedAt;
  List<AttachementInfoModels> attachments = [];

  InfoModels({
    this.id,
    this.title,
    this.content,
    this.externalLink,
    this.isActive,
    this.informationType,
    this.accessLevel,
    this.createdAt,
    this.updatedAt,
  });

  InfoModels.fromJson(Map json) {
    id = json['id'];
    title = json['title'];
    content = json['content'];
    externalLink = json['externalLink'];
    isActive = json['isActive'];
    informationType = json['informationType'];
    accessLevel = json['accessLevel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];

    if (json['attachments'] != null) {
      for (int i = 0; i < json['attachments'].length; i++) {
        AttachementInfoModels data = AttachementInfoModels.fromJson(
          json['attachments'][i],
        );
        attachments.add(data);
      }
    }
  }
}

class AttachementInfoModels {
  int? id;
  String? url;
  String? attachmentType;

  AttachementInfoModels({this.id, this.url, this.attachmentType});

  AttachementInfoModels.fromJson(Map json) {
    id = json['id'];
    url = json['url'];
    attachmentType = json['attachmentType'];
  }
}
