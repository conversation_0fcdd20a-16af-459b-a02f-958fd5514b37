class ApprovalTerminasiModel {
  ApprovalTerminasiModel({
    required this.id,
    required this.reason,
    required this.status,
    required this.approvalStatus,
    required this.requestedBy,
    required this.target,
    required this.createdAt,
    required this.updatedAt,
    required this.approvalDetails,
    required this.policyTransferInfo,
  });

  final int? id;
  final String? reason;
  final String? status;
  final String? approvalStatus;
  final RequestedBy? requestedBy;
  final RequestedBy? target;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<dynamic> approvalDetails;
  final String? policyTransferInfo;

  factory ApprovalTerminasiModel.fromJson(Map<String, dynamic> json) {
    return ApprovalTerminasiModel(
      id: json["id"],
      reason: json["reason"],
      status: json["status"],
      approvalStatus: json["approvalStatus"],
      requestedBy:
          json["requestedBy"] == null
              ? null
              : RequestedBy.fromJson(json["requestedBy"]),
      target:
          json["target"] == null ? null : RequestedBy.fromJson(json["target"]),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      approvalDetails:
          json["approvalDetails"] == null
              ? []
              : List<dynamic>.from(json["approvalDetails"]!.map((x) => x)),
      policyTransferInfo: "",
    );
  }
}

class RequestedBy {
  RequestedBy({
    required this.username,
    required this.name,
    required this.channel,
    required this.agentCode,
    required this.agentLevel,
    required this.picture,
    required this.roles,
  });

  final String? username;
  final String? name;
  final String? channel;
  final String? agentCode;
  final String? agentLevel;
  final String? picture;
  final List<Role> roles;

  factory RequestedBy.fromJson(Map<String, dynamic> json) {
    return RequestedBy(
      username: json["username"],
      name: json["name"],
      channel: json["channel"],
      agentCode: json["agentCode"],
      agentLevel: json["agentLevel"],
      picture: json["picture"],
      roles:
          json["roles"] == null
              ? []
              : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
    );
  }
}

class Role {
  Role({
    required this.code,
    required this.name,
    required this.channel,
    required this.platform,
  });

  final String? code;
  final String? name;
  final String? channel;
  final String? platform;

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      code: json["code"],
      name: json["name"],
      channel: json["channel"],
      platform: json["platform"],
    );
  }
}
