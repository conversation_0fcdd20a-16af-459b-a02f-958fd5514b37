class FlyerModels {
  int? id;
  String? title;
  String? content;
  String? frontImage;
  String? bgImage;
  String? startDate;
  String? endDate;
  bool? isActive;
  int? ordering;
  String? visibility;
  String? accessLevel;
  String? createdAt;
  String? updatedAt;

  FlyerModels({
    this.id,
    this.title,
    this.content,
    this.frontImage,
    this.bgImage,
    this.startDate,
    this.endDate,
    this.isActive,
    this.ordering,
    this.visibility,
    this.accessLevel,
    this.createdAt,
    this.updatedAt,
  });
  FlyerModels.fromJson(Map json) {
    id = json['id'];
    title = json['title'];
    content = json['content'];
    frontImage = json['frontImage'];
    bgImage = json['bgImage'];
    startDate = json['startDate'];
    endDate = json['endDate'];
    isActive = json['isActive'];
    ordering = json['ordering'];
    visibility = json['visibility'];
    accessLevel = json['accessLevel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}
