import 'dart:convert';

import 'package:pdl_superapp/models/approval_detail.dart';
import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/models/by_mode.dart';

DetailRejoinResponse detailRejoinResponseFromJson(String str) =>
    DetailRejoinResponse.fromJson(json.decode(str));

String detailRejoinResponseToJson(DetailRejoinResponse data) =>
    json.encode(data.toJson());

class DetailRejoinResponse {
  final int? id;
  final String? agentName;
  final String? agentCode;
  final String? agentPicture;
  final String? previousLevel;
  final String? proposedLevel;
  final String? previousLeaderCode;
  final String? terminationDate;
  final String? submitterCode;
  final String? submitterName;
  final String? approvalStatus;
  final BranchModels? branch;
  final String? remarks;
  final String? status;
  final ApprovalHeader? approvalHeader;
  final String? trxType;
  final String? createdAt;
  final String? updatedAt;
  final String? uploadedKtpPath;

  DetailRejoinResponse({
    this.id,
    this.agentName,
    this.agentCode,
    this.agentPicture,
    this.previousLevel,
    this.proposedLevel,
    this.previousLeaderCode,
    this.terminationDate,
    this.submitterCode,
    this.submitterName,
    this.approvalStatus,
    this.branch,
    this.remarks,
    this.status,
    this.approvalHeader,
    this.trxType,
    this.createdAt,
    this.updatedAt,
    this.uploadedKtpPath,
  });

  factory DetailRejoinResponse.fromJson(Map<String, dynamic> json) =>
      DetailRejoinResponse(
        id: json["id"],
        agentName: json["agentName"],
        agentCode: json["agentCode"],
        agentPicture: json["agentPicture"],
        previousLevel: json["previousLevel"],
        proposedLevel: json["proposedLevel"],
        previousLeaderCode: json["previousLeaderCode"],
        terminationDate: json["terminationDate"],
        submitterCode: json["submitterCode"],
        submitterName: json["submitterName"],
        approvalStatus: json["approvalStatus"],
        branch:
            json["branch"] == null
                ? null
                : BranchModels.fromJson(json["branch"]),
        remarks: json["remarks"],
        status: json["status"],
        approvalHeader:
            json["approvalHeader"] == null
                ? null
                : ApprovalHeader.fromJson(json["approvalHeader"]),
        trxType: json["trxType"],
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
        uploadedKtpPath: json["uploadedKtpPath"],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "agentName": agentName,
    "agentCode": agentCode,
    "agentPicture": agentPicture,
    "previousLevel": previousLevel,
    "proposedLevel": proposedLevel,
    "previousLeaderCode": previousLeaderCode,
    "terminationDate": terminationDate,
    "submitterCode": submitterCode,
    "submitterName": submitterName,
    "approvalStatus": approvalStatus,
    "branch": branch?.toJson(),
    "remarks": remarks,
    "status": status,
    "approvalHeader": approvalHeader?.toJson(),
    "trxType": trxType,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
    "uploadedKtpPath": uploadedKtpPath,
  };
}

class ApprovalHeader {
  final int? id;
  final String? requestId;
  final By? requestBy;
  final String? trxType;
  final int? trxId;
  final String? approvalStatus;
  final int? currentLevel;
  final String? approverRole;
  final int? maxLevel;
  final String? remarks;
  final List<ApprovalDetail>? approvalDetails;
  final String? detailApproval;
  final String? lastApproverRole;
  final DetailData? detailData;
  final int? lastLevel;
  final String? createdAt;
  final String? updatedAt;

  ApprovalHeader({
    this.id,
    this.requestId,
    this.requestBy,
    this.trxType,
    this.trxId,
    this.approvalStatus,
    this.currentLevel,
    this.approverRole,
    this.maxLevel,
    this.remarks,
    this.approvalDetails,
    this.detailApproval,
    this.lastApproverRole,
    this.detailData,
    this.lastLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory ApprovalHeader.fromJson(Map<String, dynamic> json) => ApprovalHeader(
    id: json["id"],
    requestId: json["requestId"],
    requestBy:
        json["requestBy"] == null ? null : By.fromJson(json["requestBy"]),
    trxType: json["trxType"],
    trxId: json["trxId"],
    approvalStatus: json["approvalStatus"],
    currentLevel: json["currentLevel"],
    approverRole: json["approverRole"],
    maxLevel: json["maxLevel"],
    remarks: json["remarks"],
    approvalDetails:
        json["approvalDetails"] == null
            ? []
            : List<ApprovalDetail>.from(
              json["approvalDetails"]!.map((x) => ApprovalDetail.fromJson(x)),
            ),
    detailApproval: json["detailApproval"],
    lastApproverRole: json["lastApproverRole"],
    detailData:
        json["detailData"] == null
            ? null
            : DetailData.fromJson(json["detailData"]),
    lastLevel: json["lastLevel"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "requestId": requestId,
    "requestBy": requestBy?.toJson(),
    "trxType": trxType,
    "trxId": trxId,
    "approvalStatus": approvalStatus,
    "currentLevel": currentLevel,
    "approverRole": approverRole,
    "maxLevel": maxLevel,
    "remarks": remarks,
    "approvalDetails":
        approvalDetails == null
            ? []
            : List<dynamic>.from(approvalDetails!.map((x) => x.toJson())),
    "detailApproval": detailApproval,
    "lastApproverRole": lastApproverRole,
    "detailData": detailData?.toJson(),
    "lastLevel": lastLevel,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class DetailData {
  DetailData();

  factory DetailData.fromJson(Map<String, dynamic> json) => DetailData();

  Map<String, dynamic> toJson() => {};
}
