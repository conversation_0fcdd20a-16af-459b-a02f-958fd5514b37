// To parse this JSON data, do
//
//     final countUnreadInboxResponse = countUnreadInboxResponseFromJson(jsonString);

import 'dart:convert';

List<CountUnreadInboxResponse> countUnreadInboxResponseFromJson(String str) =>
    List<CountUnreadInboxResponse>.from(
      json.decode(str).map((x) => CountUnreadInboxResponse.fromJson(x)),
    );

String countUnreadInboxResponseToJson(List<CountUnreadInboxResponse> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class CountUnreadInboxResponse {
  String? trxType;
  int? count;

  CountUnreadInboxResponse({this.trxType, this.count});

  factory CountUnreadInboxResponse.fromJson(Map<String, dynamic> json) =>
      CountUnreadInboxResponse(trxType: json["trxType"], count: json["count"]);

  Map<String, dynamic> toJson() => {"trxType": trxType, "count": count};
}
