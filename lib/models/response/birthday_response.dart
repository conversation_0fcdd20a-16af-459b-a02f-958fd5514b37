import 'dart:convert';

import 'package:pdl_superapp/models/birthday_customer_model.dart';
import 'package:pdl_superapp/models/pageable_model.dart';
import 'package:pdl_superapp/models/sort_model.dart';

BirthdayResponse inboxResponseFromJson(String str) =>
    BirthdayResponse.fromJson(json.decode(str));

String inboxResponseToJson(BirthdayResponse data) => json.encode(data.toJson());

class BirthdayResponse {
  List<BirthdayCustomerModel>? content;
  Pageable? pageable;
  bool? last;
  int? totalPages;
  int? totalElements;
  int? size;
  int? number;
  Sort? sort;
  bool? first;
  int? numberOfElements;
  bool? empty;

  BirthdayResponse({
    this.content,
    this.pageable,
    this.last,
    this.totalPages,
    this.totalElements,
    this.size,
    this.number,
    this.sort,
    this.first,
    this.numberOfElements,
    this.empty,
  });

  factory BirthdayResponse.fromJson(
    Map<String, dynamic> json,
  ) => BirthdayResponse(
    content:
        json["content"] == null
            ? []
            : List<BirthdayCustomerModel>.from(
              json["content"]!.map((x) => BirthdayCustomerModel.fromJson(x)),
            ),
    pageable:
        json["pageable"] == null ? null : Pageable.fromJson(json["pageable"]),
    last: json["last"],
    totalPages: json["totalPages"],
    totalElements: json["totalElements"],
    size: json["size"],
    number: json["number"],
    sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
    first: json["first"],
    numberOfElements: json["numberOfElements"],
    empty: json["empty"],
  );

  Map<String, dynamic> toJson() => {
    "content":
        content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "last": last,
    "totalPages": totalPages,
    "totalElements": totalElements,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "first": first,
    "numberOfElements": numberOfElements,
    "empty": empty,
  };
}
