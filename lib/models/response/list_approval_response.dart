import 'dart:convert';

import 'package:pdl_superapp/models/approval_detail.dart';
import 'package:pdl_superapp/models/pageable_model.dart';
import 'package:pdl_superapp/models/by_mode.dart';

ListApprovalResponse listApprovalResponseFromJson(String str) =>
    ListApprovalResponse.fromJson(json.decode(str));

String listApprovalResponseToJson(ListApprovalResponse data) =>
    json.encode(data.toJson());

class ListApprovalResponse {
  final int? totalElements;
  final int? totalPages;
  final Pageable? pageable;
  final int? size;
  final List<ApprovalDataModel>? content;
  final int? number;
  final bool? first;
  final bool? last;
  final int? numberOfElements;
  final bool? empty;

  ListApprovalResponse({
    this.totalElements,
    this.totalPages,
    this.pageable,
    this.size,
    this.content,
    this.number,
    this.first,
    this.last,
    this.numberOfElements,
    this.empty,
  });

  factory ListApprovalResponse.fromJson(Map<String, dynamic> json) =>
      ListApprovalResponse(
        totalElements: json["totalElements"],
        totalPages: json["totalPages"],
        pageable:
            json["pageable"] == null
                ? null
                : Pageable.fromJson(json["pageable"]),
        size: json["size"],
        content:
            json["content"] == null
                ? []
                : List<ApprovalDataModel>.from(
                  json["content"]!.map((x) => ApprovalDataModel.fromJson(x)),
                ),
        number: json["number"],
        first: json["first"],
        last: json["last"],
        numberOfElements: json["numberOfElements"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
    "totalElements": totalElements,
    "totalPages": totalPages,
    "pageable": pageable?.toJson(),
    "size": size,
    "content":
        content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
    "number": number,
    "first": first,
    "last": last,
    "numberOfElements": numberOfElements,
    "empty": empty,
  };
}

class ApprovalDataModel {
  final int? id;
  final String? requestId;
  final By? requestBy;
  final String? trxType;
  final int? trxId;
  final String? approvalStatus;
  final int? currentLevel;
  final String? approverRole;
  final int? maxLevel;
  final String? remarks;
  final List<ApprovalDetail>? approvalDetails;
  final String? detailApproval;
  final String? lastApproverRole;
  final dynamic detailData;
  final int? lastLevel;
  final String? createdAt;
  final String? updatedAt;

  ApprovalDataModel({
    this.id,
    this.requestId,
    this.requestBy,
    this.trxType,
    this.trxId,
    this.approvalStatus,
    this.currentLevel,
    this.approverRole,
    this.maxLevel,
    this.remarks,
    this.approvalDetails,
    this.detailApproval,
    this.lastApproverRole,
    this.detailData,
    this.lastLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory ApprovalDataModel.fromJson(Map<String, dynamic> json) =>
      ApprovalDataModel(
        id: json["id"],
        requestId: json["requestId"],
        requestBy:
            json["requestBy"] == null ? null : By.fromJson(json["requestBy"]),
        trxType: json["trxType"],
        trxId: json["trxId"],
        approvalStatus: json["approvalStatus"],
        currentLevel: json["currentLevel"],
        approverRole: json["approverRole"],
        maxLevel: json["maxLevel"],
        remarks: json["remarks"],
        approvalDetails:
            json["approvalDetails"] == null
                ? []
                : List<ApprovalDetail>.from(
                  json["approvalDetails"]!.map(
                    (x) => ApprovalDetail.fromJson(x),
                  ),
                ),
        detailApproval: json["detailApproval"],
        lastApproverRole: json["lastApproverRole"],
        detailData: json["detailData"],
        lastLevel: json["lastLevel"],
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "requestId": requestId,
    "requestBy": requestBy?.toJson(),
    "trxType": trxType,
    "trxId": trxId,
    "approvalStatus": approvalStatus,
    "currentLevel": currentLevel,
    "approverRole": approverRole,
    "maxLevel": maxLevel,
    "remarks": remarks,
    "approvalDetails":
        approvalDetails == null
            ? []
            : List<dynamic>.from(approvalDetails!.map((x) => x.toJson())),
    "detailApproval": detailApproval,
    "lastApproverRole": lastApproverRole,
    "detailData": detailData?.toJson(),
    "lastLevel": lastLevel,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}
