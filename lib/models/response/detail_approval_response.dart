import 'dart:convert';

import 'package:pdl_superapp/models/approval_detail.dart';
import 'package:pdl_superapp/models/by_mode.dart';

DetailApprovalResponse detailApprovalResponseFromJson(String str) =>
    DetailApprovalResponse.fromJson(json.decode(str));

String detailApprovalResponseToJson(DetailApprovalResponse data) =>
    json.encode(data.toJson());

class DetailApprovalResponse {
  final int? id;
  final String? requestId;
  final By? requestBy;
  final String? trxType;
  final int? trxId;
  final String? approvalStatus;
  final int? currentLevel;
  final String? approverRole;
  final int? maxLevel;
  final String? remarks;
  final List<ApprovalDetail>? approvalDetails;
  final String? detailApproval;
  final String? lastApproverRole;
  final DetailData? detailData;
  final int? lastLevel;
  final String? createdAt;
  final String? updatedAt;

  DetailApprovalResponse({
    this.id,
    this.requestId,
    this.requestBy,
    this.trxType,
    this.trxId,
    this.approvalStatus,
    this.currentLevel,
    this.approverRole,
    this.maxLevel,
    this.remarks,
    this.approvalDetails,
    this.detailApproval,
    this.lastApproverRole,
    this.detailData,
    this.lastLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory DetailApprovalResponse.fromJson(Map<String, dynamic> json) =>
      DetailApprovalResponse(
        id: json["id"],
        requestId: json["requestId"],
        requestBy:
            json["requestBy"] == null ? null : By.fromJson(json["requestBy"]),
        trxType: json["trxType"],
        trxId: json["trxId"],
        approvalStatus: json["approvalStatus"],
        currentLevel: json["currentLevel"],
        approverRole: json["approverRole"],
        maxLevel: json["maxLevel"],
        remarks: json["remarks"],
        approvalDetails:
            json["approvalDetails"] == null
                ? []
                : List<ApprovalDetail>.from(
                  json["approvalDetails"]!.map(
                    (x) => ApprovalDetail.fromJson(x),
                  ),
                ),
        detailApproval: json["detailApproval"],
        lastApproverRole: json["lastApproverRole"],
        detailData:
            json["detailData"] == null
                ? null
                : DetailData.fromJson(json["detailData"]),
        lastLevel: json["lastLevel"],
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "requestId": requestId,
    "requestBy": requestBy?.toJson(),
    "trxType": trxType,
    "trxId": trxId,
    "approvalStatus": approvalStatus,
    "currentLevel": currentLevel,
    "approverRole": approverRole,
    "maxLevel": maxLevel,
    "remarks": remarks,
    "approvalDetails":
        approvalDetails == null
            ? []
            : List<dynamic>.from(approvalDetails!.map((x) => x.toJson())),
    "detailApproval": detailApproval,
    "lastApproverRole": lastApproverRole,
    "detailData": detailData?.toJson(),
    "lastLevel": lastLevel,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class DetailData {
  DetailData();

  factory DetailData.fromJson(Map<String, dynamic> json) => DetailData();

  Map<String, dynamic> toJson() => {};
}
