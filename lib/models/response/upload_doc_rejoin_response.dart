import 'dart:convert';

UploadDocRejoinResponse uploadDocRejoinResponseFromJson(String str) =>
    UploadDocRejoinResponse.fromJson(json.decode(str));

String uploadDocRejoinResponseToJson(UploadDocRejoinResponse data) =>
    json.encode(data.toJson());

class UploadDocRejoinResponse {
  final String? error;
  final List<String>? initialPreview;
  final List<InitialPreviewConfig>? initialPreviewConfig;

  UploadDocRejoinResponse({
    this.error,
    this.initialPreview,
    this.initialPreviewConfig,
  });

  factory UploadDocRejoinResponse.fromJson(Map<String, dynamic> json) =>
      UploadDocRejoinResponse(
        error: json["error"],
        initialPreview:
            json["initialPreview"] == null
                ? []
                : List<String>.from(json["initialPreview"]!.map((x) => x)),
        initialPreviewConfig:
            json["initialPreviewConfig"] == null
                ? []
                : List<InitialPreviewConfig>.from(
                  json["initialPreviewConfig"]!.map(
                    (x) => InitialPreviewConfig.fromJson(x),
                  ),
                ),
      );

  Map<String, dynamic> toJson() => {
    "error": error,
    "initialPreview":
        initialPreview == null
            ? []
            : List<dynamic>.from(initialPreview!.map((x) => x)),
    "initialPreviewConfig":
        initialPreviewConfig == null
            ? []
            : List<dynamic>.from(initialPreviewConfig!.map((x) => x.toJson())),
  };
}

class InitialPreviewConfig {
  final Extra? extra;

  InitialPreviewConfig({this.extra});

  factory InitialPreviewConfig.fromJson(Map<String, dynamic> json) =>
      InitialPreviewConfig(
        extra: json["extra"] == null ? null : Extra.fromJson(json["extra"]),
      );

  Map<String, dynamic> toJson() => {"extra": extra?.toJson()};
}

class Extra {
  final AdditionalProp? additionalProp1;
  final AdditionalProp? additionalProp2;
  final AdditionalProp? additionalProp3;

  Extra({this.additionalProp1, this.additionalProp2, this.additionalProp3});

  factory Extra.fromJson(Map<String, dynamic> json) => Extra(
    additionalProp1:
        json["additionalProp1"] == null
            ? null
            : AdditionalProp.fromJson(json["additionalProp1"]),
    additionalProp2:
        json["additionalProp2"] == null
            ? null
            : AdditionalProp.fromJson(json["additionalProp2"]),
    additionalProp3:
        json["additionalProp3"] == null
            ? null
            : AdditionalProp.fromJson(json["additionalProp3"]),
  );

  Map<String, dynamic> toJson() => {
    "additionalProp1": additionalProp1?.toJson(),
    "additionalProp2": additionalProp2?.toJson(),
    "additionalProp3": additionalProp3?.toJson(),
  };
}

class AdditionalProp {
  AdditionalProp();

  factory AdditionalProp.fromJson(Map<String, dynamic> json) =>
      AdditionalProp();

  Map<String, dynamic> toJson() => {};
}
