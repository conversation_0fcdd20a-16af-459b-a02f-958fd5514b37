import 'dart:convert';

import 'package:pdl_superapp/models/pageable_model.dart';
import 'package:pdl_superapp/models/policy_lapsed_model.dart';
import 'package:pdl_superapp/models/sort_model.dart';

PolicyLapsedResponse inboxResponseFromJson(String str) =>
    PolicyLapsedResponse.fromJson(json.decode(str));

String inboxResponseToJson(PolicyLapsedResponse data) =>
    json.encode(data.toJson());

class PolicyLapsedResponse {
  List<PolicyLapsedModel>? content;
  Pageable? pageable;
  bool? last;
  int? totalPages;
  int? totalElements;
  int? size;
  int? number;
  Sort? sort;
  bool? first;
  int? numberOfElements;
  bool? empty;

  PolicyLapsedResponse({
    this.content,
    this.pageable,
    this.last,
    this.totalPages,
    this.totalElements,
    this.size,
    this.number,
    this.sort,
    this.first,
    this.numberOfElements,
    this.empty,
  });

  factory PolicyLapsedResponse.fromJson(Map<String, dynamic> json) =>
      PolicyLapsedResponse(
        content:
            json["content"] == null
                ? []
                : List<PolicyLapsedModel>.from(
                  json["content"]!.map((x) => PolicyLapsedModel.fromJson(x)),
                ),
        pageable:
            json["pageable"] == null
                ? null
                : Pageable.fromJson(json["pageable"]),
        last: json["last"],
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        size: json["size"],
        number: json["number"],
        sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
        first: json["first"],
        numberOfElements: json["numberOfElements"],
        empty: json["empty"],
      );

  Map<String, dynamic> toJson() => {
    "content":
        content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "last": last,
    "totalPages": totalPages,
    "totalElements": totalElements,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "first": first,
    "numberOfElements": numberOfElements,
    "empty": empty,
  };
}
