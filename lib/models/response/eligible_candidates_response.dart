import 'dart:convert';

import 'package:pdl_superapp/models/pageable_model.dart';

EligibleCandidatesResponse eligibleCandidatesResponseFromJson(String str) =>
    EligibleCandidatesResponse.fromJson(json.decode(str));

String eligibleCandidatesResponseToJson(EligibleCandidatesResponse data) =>
    json.encode(data.toJson());

class EligibleCandidatesResponse {
  final int? totalElements;
  final int? totalPages;
  final Pageable? pageable;
  final int? size;
  final List<EligibleCandidateModel>? content;
  final int? number;
  final bool? first;
  final bool? last;
  final int? numberOfElements;
  final bool? empty;

  EligibleCandidatesResponse({
    this.totalElements,
    this.totalPages,
    this.pageable,
    this.size,
    this.content,
    this.number,
    this.first,
    this.last,
    this.numberOfElements,
    this.empty,
  });

  factory EligibleCandidatesResponse.fromJson(
    Map<String, dynamic> json,
  ) => EligibleCandidatesResponse(
    totalElements: json["totalElements"],
    totalPages: json["totalPages"],
    pageable:
        json["pageable"] == null ? null : Pageable.fromJson(json["pageable"]),
    size: json["size"],
    content:
        json["content"] == null
            ? []
            : List<EligibleCandidateModel>.from(
              json["content"]!.map((x) => EligibleCandidateModel.fromJson(x)),
            ),
    number: json["number"],
    first: json["first"],
    last: json["last"],
    numberOfElements: json["numberOfElements"],
    empty: json["empty"],
  );

  Map<String, dynamic> toJson() => {
    "totalElements": totalElements,
    "totalPages": totalPages,
    "pageable": pageable?.toJson(),
    "size": size,
    "content":
        content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
    "number": number,
    "first": first,
    "last": last,
    "numberOfElements": numberOfElements,
    "empty": empty,
  };
}

class EligibleCandidateModel {
  final String? agentCode;
  final String? agentName;
  final String? previousLevel;
  final String? previousLeaderCode;
  final String? previousLeaderName;
  final String? branchCode;
  final String? branchName;
  final String? branchCity;
  final String? branchAddress;
  final String? agentPicture;

  EligibleCandidateModel({
    this.agentCode,
    this.agentName,
    this.previousLevel,
    this.previousLeaderCode,
    this.previousLeaderName,
    this.branchCode,
    this.branchName,
    this.branchCity,
    this.branchAddress,
    this.agentPicture,
  });

  factory EligibleCandidateModel.fromJson(Map<String, dynamic> json) =>
      EligibleCandidateModel(
        agentCode: json["agentCode"],
        agentName: json["agentName"],
        previousLevel: json["previousLevel"],
        previousLeaderCode: json["previousLeaderCode"],
        previousLeaderName: json["previousLeaderName"],
        branchCode: json["branchCode"],
        branchName: json["branchName"],
        branchCity: json["branchCity"],
        branchAddress: json["branchAddress"],
        agentPicture: json["agentPicture"],
      );

  Map<String, dynamic> toJson() => {
    "agentCode": agentCode,
    "agentName": agentName,
    "previousLevel": previousLevel,
    "previousLeaderCode": previousLeaderCode,
    "previousLeaderName": previousLeaderName,
    "branchCode": branchCode,
    "branchName": branchName,
    "branchCity": branchCity,
    "branchAddress": branchAddress,
    "agentPicture": agentPicture,
  };
}
