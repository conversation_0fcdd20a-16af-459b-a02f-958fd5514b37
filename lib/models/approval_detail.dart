import 'package:pdl_superapp/models/by_mode.dart';

class ApprovalDetail {
  final int? id;
  final By? actionBy;
  final String? remarks;
  final String? approvalStatus;
  final String? detailApproval;
  final int? levelNumber;
  final String? createdAt;

  ApprovalDetail({
    this.id,
    this.actionBy,
    this.remarks,
    this.approvalStatus,
    this.detailApproval,
    this.levelNumber,
    this.createdAt,
  });

  factory ApprovalDetail.fromJson(Map<String, dynamic> json) => ApprovalDetail(
    id: json["id"],
    actionBy: json["actionBy"] == null ? null : By.fromJson(json["actionBy"]),
    remarks: json["remarks"],
    approvalStatus: json["approvalStatus"],
    detailApproval: json["detailApproval"],
    levelNumber: json["levelNumber"],
    createdAt: json["createdAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "actionBy": actionBy?.to<PERSON>son(),
    "remarks": remarks,
    "approvalStatus": approvalStatus,
    "detailApproval": detailApproval,
    "levelNumber": levelNumber,
    "createdAt": createdAt,
  };
}
