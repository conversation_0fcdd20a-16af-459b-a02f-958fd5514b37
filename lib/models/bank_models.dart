class BankModels {
  int? id;
  String? bankCode;
  String? bankAdvanceAiCode;
  String? bankName;
  String? createdAt;
  String? updatedAt;

  BankModels({
    this.id,
    this.bankCode,
    this.bankAdvanceAiCode,
    this.bankName,
    this.createdAt,
    this.updatedAt,
  });

  BankModels.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bankCode = json['bankCode'];
    bankAdvanceAiCode = json['bankAdvanceAiCode'];
    bankName = json['bankName'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}
