import 'dart:convert';

class RequestEditModels {
  int? id;
  String? requestId;
  String? approvalStatus;
  List<FieldDataModels> fieldData = [];
  List<ApprovalDetail> detailApproval = [];

  RequestEditModels({this.id, this.requestId, this.approvalStatus});

  RequestEditModels.fromJson(Map json) {
    id = json['id'];
    requestId = json['approvalHeader']['requestId'];
    approvalStatus = json['approvalStatus'];
    if (json['detailApproval'] != null) {
      var approval = jsonDecode(json['detailApproval']);
      for (int i = 0; i < approval.length; i++) {
        ApprovalDetail data = ApprovalDetail.fromJson(approval[i]);
        detailApproval.add(data);
      }
    }
    if (json['data'] != null) {
      Map<String, dynamic> data = jsonDecode(json['data']);
      data.forEach((key, val) {
        FieldDataModels itemData = FieldDataModels(fieldName: key, value: val);
        fieldData.add(itemData);
      });
    }
  }
}

class FieldDataModels {
  String? fieldName;
  String? value;

  FieldDataModels({this.fieldName, this.value});

  FieldDataModels.fromJson(Map json) {
    fieldName = json[0];
    value = json[1];
  }
}

class ApprovalDetail {
  String? field;
  String? action;
  String? remark;

  ApprovalDetail({this.field, this.action, this.remark});

  ApprovalDetail.fromJson(Map json) {
    field = json['field'];
    action = json['action'];
    remark = json['remark'];
  }
}
