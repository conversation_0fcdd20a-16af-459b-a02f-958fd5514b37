class WidgetApiModel {
  final int id;
  final String widgetKey;
  final bool isActive;
  final int ordering;
  final String channel;
  final String menuType;
  final String createdAt;
  final String updatedAt;

  WidgetApiModel({
    required this.id,
    required this.widgetKey,
    required this.isActive,
    required this.ordering,
    required this.channel,
    required this.menuType,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WidgetApiModel.fromJson(Map<String, dynamic> json) {
    return WidgetApiModel(
      id: json['id'] ?? 0,
      widgetKey: json['widgetKey'] ?? '',
      isActive: json['isActive'] ?? false,
      ordering: json['ordering'] ?? 0,
      channel: json['channel'] ?? '',
      menuType: json['menuType'] ?? '',
      createdAt: json['createdAt'] ?? '',
      updatedAt: json['updatedAt'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'widgetKey': widgetKey,
      'isActive': isActive,
      'ordering': ordering,
      'channel': channel,
      'menuType': menuType,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }
}
