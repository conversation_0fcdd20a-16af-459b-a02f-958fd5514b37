import 'dart:convert';
import 'dart:developer';
import 'package:pdl_superapp/models/branch_models.dart';

class RecruitmentApiModel {
  int? id;
  String? uuid;
  Recruiter? recruiter;
  ApprovalHeader? approvalHeader;
  String? approvalStatus;
  String? trxStatus;
  String? recruiterCode;
  String? recruiterName;
  String? recruiterBranchCode;
  String? leaderCode;
  String? agentCode;
  String? ktpPhoto;
  String? selfiePhoto;
  String? passPhoto;
  String? positionLevel;
  BranchModels? branch;
  String? nik;
  String? fullName;
  String? birthPlace;
  String? birthDate;
  String? gender;
  String? ktpProvince;
  String? ktpCity;
  String? ktpDistrict;
  String? ktpSubDistrict;
  String? ktpRt;
  String? ktpRw;
  String? ktpAddress;
  bool? isDomicileSameAsKtp;
  String? domicileProvince;
  String? domicileCity;
  String? domicileDistrict;
  String? domicileSubDistrict;
  String? domicileRt;
  String? domicileRw;
  String? domicileAddress;
  String? phoneNumber;
  String? maritalStatus;
  String? occupation;
  String? occupationCode;
  String? email;
  String? emergencyContactName;
  String? emergencyContactRelation;
  String? emergencyContactPhone;
  String? bankAccountName;
  String? bankAccountNumber;
  Bank? bank;
  String? lastJob;
  List<Last5YearJobData>? last5YearJobData;
  List<Last2YearProductionData>? last2YearProductionData;
  LastCompanyManPowerData? lastCompanyManPowerData;
  List<RewardInfoData>? rewardInfoData;
  String? pkajFile;
  String? pmkajFile;
  String? kodeEtikFile;
  String? antiTwistingFile;
  String? signature;
  String? paraf;
  bool? isEmailVerified;
  String? emailVerifiedDate;
  String? validationBlacklistStatus;
  String? validationKtpStatus;
  String? resulValidationKtp;
  String? validationBankAccountStatus;
  String? resultValidationBankAccount;
  String? validationHirarkiStatus;
  String? resultValidationHirarki;
  String? validationAmlStatus;
  String? validationAdministrationAgentStatus;
  String? validationLicenseAajiStatus;
  String? validationLicenseAasiStatus;
  String? resultInterview;
  String? createdAt;
  String? updatedAt;

  RecruitmentApiModel();

  RecruitmentApiModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    uuid = json['uuid'];
    recruiter =
        json['recruiter'] != null
            ? Recruiter.fromJson(json['recruiter'])
            : null;
    approvalHeader =
        json['approvalHeader'] != null
            ? ApprovalHeader.fromJson(json['approvalHeader'])
            : null;
    approvalStatus = json['approvalStatus'];
    trxStatus = json['trxStatus'];
    recruiterCode = json['recruiterCode'];
    recruiterName = json['recruiterName'];
    recruiterBranchCode = json['recruiterBranchCode'];
    leaderCode = json['leaderCode'];
    agentCode = json['agentCode'];
    ktpPhoto = json['ktpPhoto'];
    selfiePhoto = json['selfiePhoto'];
    passPhoto = json['passPhoto'];
    positionLevel = json['positionLevel'];
    branch =
        json['branch'] != null ? BranchModels.fromJson(json['branch']) : null;
    nik = json['nik'];
    fullName = json['fullName'];
    birthPlace = json['birthPlace'];
    birthDate = json['birthDate'];
    gender = json['gender'];
    ktpProvince = json['ktpProvince'];
    ktpCity = json['ktpCity'];
    ktpDistrict = json['ktpDistrict'];
    ktpSubDistrict = json['ktpSubDistrict'];
    ktpRt = json['ktpRt'];
    ktpRw = json['ktpRw'];
    ktpAddress = json['ktpAddress'];
    isDomicileSameAsKtp = json['isDomicileSameAsKtp'];
    domicileProvince = json['domicileProvince'];
    domicileCity = json['domicileCity'];
    domicileDistrict = json['domicileDistrict'];
    domicileSubDistrict = json['domicileSubDistrict'];
    domicileRt = json['domicileRt'];
    domicileRw = json['domicileRw'];
    domicileAddress = json['domicileAddress'];
    phoneNumber = json['phoneNumber'];
    maritalStatus = json['maritalStatus'];
    occupation = json['occupation'];
    occupationCode = json['occupationCode'];
    email = json['email'];
    emergencyContactName = json['emergencyContactName'];
    emergencyContactRelation = json['emergencyContactRelation'];
    emergencyContactPhone = json['emergencyContactPhone'];
    bankAccountName = json['bankAccountName'];
    bankAccountNumber = json['bankAccountNumber'];
    bank = json['bank'] != null ? Bank.fromJson(json['bank']) : null;
    lastJob = json['lastJob'];

    // Parse last5YearJobData - handle both string and object formats
    // Check both 'last5YearJobData' and 'last5YearJob' field names
    var last5YearJobField = json['last5YearJobData'] ?? json['last5YearJob'];
    if (last5YearJobField != null) {
      if (last5YearJobField is String) {
        // Parse JSON string
        try {
          var parsedData = jsonDecode(last5YearJobField);
          if (parsedData is List) {
            last5YearJobData =
                parsedData.map((e) => Last5YearJobData.fromJson(e)).toList();
          }
        } catch (e) {
          log('Error parsing last5YearJobData: $e');
          last5YearJobData = [];
        }
      } else if (last5YearJobField is List) {
        // Already parsed object
        last5YearJobData =
            List.from(
              last5YearJobField,
            ).map((e) => Last5YearJobData.fromJson(e)).toList();
      }
    }

    // Parse last2YearProductionData - handle both string and object formats
    // Check both 'last2YearProductionData' and 'last2YearProduction' field names
    var last2YearProductionField =
        json['last2YearProductionData'] ?? json['last2YearProduction'];
    if (last2YearProductionField != null) {
      if (last2YearProductionField is String) {
        // Parse JSON string
        try {
          var parsedData = jsonDecode(last2YearProductionField);
          if (parsedData is List) {
            last2YearProductionData =
                parsedData
                    .map((e) => Last2YearProductionData.fromJson(e))
                    .toList();
          }
        } catch (e) {
          log('Error parsing last2YearProductionData: $e');
          last2YearProductionData = [];
        }
      } else if (last2YearProductionField is List) {
        // Already parsed object
        last2YearProductionData =
            List.from(
              last2YearProductionField,
            ).map((e) => Last2YearProductionData.fromJson(e)).toList();
      }
    }

    // Parse lastCompanyManPowerData - handle both string and object formats
    // Check both 'lastCompanyManPowerData' and 'lastCompanyManPower' field names
    var lastCompanyManPowerField =
        json['lastCompanyManPowerData'] ?? json['lastCompanyManPower'];
    if (lastCompanyManPowerField != null) {
      if (lastCompanyManPowerField is String) {
        // Parse JSON string
        try {
          var parsedData = jsonDecode(lastCompanyManPowerField);
          if (parsedData is Map<String, dynamic>) {
            lastCompanyManPowerData = LastCompanyManPowerData.fromJson(
              parsedData,
            );
          }
        } catch (e) {
          log('Error parsing lastCompanyManPowerData: $e');
          lastCompanyManPowerData = null;
        }
      } else if (lastCompanyManPowerField is Map<String, dynamic>) {
        // Already parsed object
        lastCompanyManPowerData = LastCompanyManPowerData.fromJson(
          lastCompanyManPowerField,
        );
      }
    }

    // Parse rewardInfoData - handle both string and object formats
    // Check both 'rewardInfoData' and 'rewardInfo' field names
    var rewardInfoField = json['rewardInfoData'] ?? json['rewardInfo'];
    if (rewardInfoField != null) {
      if (rewardInfoField is String) {
        // Parse JSON string
        try {
          var parsedData = jsonDecode(rewardInfoField);
          if (parsedData is List) {
            rewardInfoData =
                parsedData.map((e) => RewardInfoData.fromJson(e)).toList();
          }
        } catch (e) {
          log('Error parsing rewardInfoData: $e');
          rewardInfoData = [];
        }
      } else if (rewardInfoField is List) {
        // Already parsed object
        rewardInfoData =
            List.from(
              rewardInfoField,
            ).map((e) => RewardInfoData.fromJson(e)).toList();
      }
    }
    pkajFile = json['pkajFile'];
    pmkajFile = json['pmkajFile'];
    kodeEtikFile = json['kodeEtikFile'];
    antiTwistingFile = json['antiTwistingFile'];
    signature = json['signature'];
    paraf = json['paraf'];
    isEmailVerified = json['isEmailVerified'];
    emailVerifiedDate = json['emailVerifiedDate'];
    validationBlacklistStatus = json['validationBlacklistStatus'];
    validationKtpStatus = json['validationKtpStatus'];
    resulValidationKtp = json['resulValidationKtp'];
    validationBankAccountStatus = json['validationBankAccountStatus'];
    resultValidationBankAccount = json['resultValidationBankAccount'];
    validationHirarkiStatus = json['validationHirarkiStatus'];
    resultValidationHirarki = json['resultValidationHirarki'];
    validationAmlStatus = json['validationAmlStatus'];
    validationAdministrationAgentStatus =
        json['validationAdministrationAgentStatus'];
    validationLicenseAajiStatus = json['validationLicenseAajiStatus'];
    validationLicenseAasiStatus = json['validationLicenseAasiStatus'];
    resultInterview = json['resultInterview'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['uuid'] = uuid;
    if (recruiter != null) {
      data['recruiter'] = recruiter!.toJson();
    }
    if (approvalHeader != null) {
      data['approvalHeader'] = approvalHeader!.toJson();
    }
    data['approvalStatus'] = approvalStatus;
    data['trxStatus'] = trxStatus;
    data['recruiterCode'] = recruiterCode;
    data['recruiterName'] = recruiterName;
    data['recruiterBranchCode'] = recruiterBranchCode;
    data['leaderCode'] = leaderCode;
    data['agentCode'] = agentCode;
    data['ktpPhoto'] = ktpPhoto;
    data['selfiePhoto'] = selfiePhoto;
    data['passPhoto'] = passPhoto;
    data['positionLevel'] = positionLevel;
    if (branch != null) {
      data['branch'] = {
        'id': branch!.id,
        'branchCode': branch!.branchCode,
        'branchName': branch!.branchName,
        'parentBranchCode': branch!.parentBranchCode,
        'parentBranchName': branch!.parentBranchName,
        'phoneNumber': branch!.phoneNumber,
        'staffCount': branch!.staffCount,
        'city': branch!.city,
        'address': branch!.address,
        'secondAddress': branch!.secondAddress,
        'thirdAddress': branch!.thirdAddress,
        'googleMapsUrl': branch!.googleMapsUrl,
        'latitude': branch!.latitude,
        'longitude': branch!.longitude,
        'isActive': branch!.isActive,
        'channel': branch!.channel,
        'createdAt': branch!.createdAt,
        'updatedAt': branch!.updatedAt,
      };
    }
    data['nik'] = nik;
    data['fullName'] = fullName;
    data['birthPlace'] = birthPlace;
    data['birthDate'] = birthDate;
    data['gender'] = gender;
    data['ktpProvince'] = ktpProvince;
    data['ktpCity'] = ktpCity;
    data['ktpDistrict'] = ktpDistrict;
    data['ktpSubDistrict'] = ktpSubDistrict;
    data['ktpRt'] = ktpRt;
    data['ktpRw'] = ktpRw;
    data['ktpAddress'] = ktpAddress;
    data['isDomicileSameAsKtp'] = isDomicileSameAsKtp;
    data['domicileProvince'] = domicileProvince;
    data['domicileCity'] = domicileCity;
    data['domicileDistrict'] = domicileDistrict;
    data['domicileSubDistrict'] = domicileSubDistrict;
    data['domicileRt'] = domicileRt;
    data['domicileRw'] = domicileRw;
    data['domicileAddress'] = domicileAddress;
    data['phoneNumber'] = phoneNumber;
    data['maritalStatus'] = maritalStatus;
    data['occupation'] = occupation;
    data['occupationCode'] = occupationCode;
    data['email'] = email;
    data['emergencyContactName'] = emergencyContactName;
    data['emergencyContactRelation'] = emergencyContactRelation;
    data['emergencyContactPhone'] = emergencyContactPhone;
    data['bankAccountName'] = bankAccountName;
    data['bankAccountNumber'] = bankAccountNumber;
    if (bank != null) {
      data['bank'] = bank!.toJson();
    }
    data['lastJob'] = lastJob;
    if (last5YearJobData != null) {
      data['last5YearJobData'] =
          last5YearJobData!.map((v) => v.toJson()).toList();
    }
    if (last2YearProductionData != null) {
      data['last2YearProductionData'] =
          last2YearProductionData!.map((v) => v.toJson()).toList();
    }
    if (lastCompanyManPowerData != null) {
      data['lastCompanyManPowerData'] = lastCompanyManPowerData!.toJson();
    }
    if (rewardInfoData != null) {
      data['rewardInfoData'] = rewardInfoData!.map((v) => v.toJson()).toList();
    }
    data['pkajFile'] = pkajFile;
    data['pmkajFile'] = pmkajFile;
    data['kodeEtikFile'] = kodeEtikFile;
    data['antiTwistingFile'] = antiTwistingFile;
    data['signature'] = signature;
    data['paraf'] = paraf;
    data['isEmailVerified'] = isEmailVerified;
    data['emailVerifiedDate'] = emailVerifiedDate;
    data['validationBlacklistStatus'] = validationBlacklistStatus;
    data['validationKtpStatus'] = validationKtpStatus;
    data['resulValidationKtp'] = resulValidationKtp;
    data['validationBankAccountStatus'] = validationBankAccountStatus;
    data['resultValidationBankAccount'] = resultValidationBankAccount;
    data['validationHirarkiStatus'] = validationHirarkiStatus;
    data['resultValidationHirarki'] = resultValidationHirarki;
    data['validationAmlStatus'] = validationAmlStatus;
    data['validationAdministrationAgentStatus'] =
        validationAdministrationAgentStatus;
    data['validationLicenseAajiStatus'] = validationLicenseAajiStatus;
    data['validationLicenseAasiStatus'] = validationLicenseAasiStatus;
    data['resultInterview'] = resultInterview;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

// Supporting classes
class Recruiter {
  String? username;
  String? name;
  String? channel;
  String? agentCode;
  String? agentLevel;
  List<BranchModels>? branches;

  Recruiter();

  Recruiter.fromJson(Map<String, dynamic> json) {
    username = json['username'];
    name = json['name'];
    channel = json['channel'];
    agentCode = json['agentCode'];
    agentLevel = json['agentLevel'];
    if (json['branches'] != null) {
      branches =
          List.from(
            json['branches'],
          ).map((e) => BranchModels.fromJson(e)).toList();
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['username'] = username;
    data['name'] = name;
    data['channel'] = channel;
    data['agentCode'] = agentCode;
    data['agentLevel'] = agentLevel;
    if (branches != null) {
      data['branches'] =
          branches!
              .map(
                (v) => {
                  'id': v.id,
                  'branchCode': v.branchCode,
                  'branchName': v.branchName,
                  'parentBranchCode': v.parentBranchCode,
                  'parentBranchName': v.parentBranchName,
                  'phoneNumber': v.phoneNumber,
                  'staffCount': v.staffCount,
                  'city': v.city,
                  'address': v.address,
                  'secondAddress': v.secondAddress,
                  'thirdAddress': v.thirdAddress,
                  'googleMapsUrl': v.googleMapsUrl,
                  'latitude': v.latitude,
                  'longitude': v.longitude,
                  'isActive': v.isActive,
                  'channel': v.channel,
                  'createdAt': v.createdAt,
                  'updatedAt': v.updatedAt,
                },
              )
              .toList();
    }
    return data;
  }
}

class ApprovalHeader {
  int? id;
  String? requestId;
  Recruiter? requestBy;
  String? trxType;
  int? trxId;
  String? approvalStatus;
  int? currentLevel;
  String? approverRole;
  int? maxLevel;
  String? remarks;
  List<ApprovalDetail>? approvalDetails = [];
  dynamic detailApproval;
  String? lastApproverRole;
  dynamic detailData;
  int? lastLevel;
  String? createdAt;
  String? updatedAt;

  ApprovalHeader();

  ApprovalHeader.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    requestId = json['requestId'];
    requestBy =
        json['requestBy'] != null
            ? Recruiter.fromJson(json['requestBy'])
            : null;
    trxType = json['trxType'];
    trxId = json['trxId'];
    approvalStatus = json['approvalStatus'];
    currentLevel = json['currentLevel'];
    approverRole = json['approverRole'];
    maxLevel = json['maxLevel'];
    remarks = json['remarks'];
    approvalDetails =
        json['approvalDetails'] != null
            ? List.from(
              json['approvalDetails'],
            ).map((e) => ApprovalDetail.fromJson(e)).toList()
            : [];
    detailApproval = json['detailApproval'];
    lastApproverRole = json['lastApproverRole'];
    detailData = json['detailData'];
    lastLevel = json['lastLevel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['requestId'] = requestId;
    if (requestBy != null) {
      data['requestBy'] = requestBy!.toJson();
    }
    data['trxType'] = trxType;
    data['trxId'] = trxId;
    data['approvalStatus'] = approvalStatus;
    data['currentLevel'] = currentLevel;
    data['approverRole'] = approverRole;
    data['maxLevel'] = maxLevel;
    data['remarks'] = remarks;
    data['approvalDetails'] = approvalDetails;
    data['detailApproval'] = detailApproval;
    data['lastApproverRole'] = lastApproverRole;
    data['detailData'] = detailData;
    data['lastLevel'] = lastLevel;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

class Bank {
  int? id;
  String? bankCode;
  String? bankAdvanceAiCode;
  String? bankName;
  String? createdAt;
  String? updatedAt;

  Bank();

  Bank.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bankCode = json['bankCode'];
    bankAdvanceAiCode = json['bankAdvanceAiCode'];
    bankName = json['bankName'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['bankCode'] = bankCode;
    data['bankAdvanceAiCode'] = bankAdvanceAiCode;
    data['bankName'] = bankName;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}

// New model classes for structured data
class Last5YearJobData {
  int? year;
  int? startYear;
  int? endYear;
  String? company;
  String? position;

  Last5YearJobData({
    this.year,
    this.startYear,
    this.endYear,
    this.company,
    this.position,
  });

  Last5YearJobData.fromJson(Map<String, dynamic> json) {
    year = json['year'];
    startYear = json['startYear'];
    endYear = json['endYear'];
    company = json['company'];
    position = json['position'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['year'] = year;
    data['startYear'] = startYear;
    data['endYear'] = endYear;
    data['company'] = company;
    data['position'] = position;
    return data;
  }
}

class Last2YearProductionData {
  int? personalProduction;
  int? teamProduction;

  Last2YearProductionData({this.personalProduction, this.teamProduction});

  Last2YearProductionData.fromJson(Map<String, dynamic> json) {
    personalProduction = json['personalProduction'];
    teamProduction = json['teamProduction'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['personalProduction'] = personalProduction;
    data['teamProduction'] = teamProduction;
    return data;
  }
}

class LastCompanyManPowerData {
  int? agentCount;
  int? leaderCount;

  LastCompanyManPowerData({this.agentCount, this.leaderCount});

  LastCompanyManPowerData.fromJson(Map<String, dynamic> json) {
    agentCount = json['agentCount'];
    leaderCount = json['leaderCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['agentCount'] = agentCount;
    data['leaderCount'] = leaderCount;
    return data;
  }
}

class RewardInfoData {
  int? year;
  String? description;

  RewardInfoData({this.year, this.description});

  RewardInfoData.fromJson(Map<String, dynamic> json) {
    year = json['year'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['year'] = year;
    data['description'] = description;
    return data;
  }
}

class ApprovalDetail {
  int? id;
  Recruiter? actionBy;
  String? remarks;
  String? approvalStatus;
  String? detailApproval;
  int? levelNumber;
  String? createdAt;

  ApprovalDetail();

  ApprovalDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    actionBy =
        json['actionBy'] != null ? Recruiter.fromJson(json['actionBy']) : null;
    remarks = json['remarks'];
    approvalStatus = json['approvalStatus'];
    detailApproval = json['detailApproval'];
    levelNumber = json['levelNumber'];
    createdAt = json['createdAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (actionBy != null) {
      data['actionBy'] = actionBy!.toJson();
    }
    data['remarks'] = remarks;
    data['approvalStatus'] = approvalStatus;
    data['detailApproval'] = detailApproval;
    data['levelNumber'] = levelNumber;
    data['createdAt'] = createdAt;
    return data;
  }
}
