class DeviceModels {
  String? deviceId;
  String? deviceModel;
  String? osType;
  String? osVersion;
  String? appVersion;
  String? appBuildNumber;
  String? deviceLanguage;
  num? screenWidth;
  num? screenHeight;
  String? connectionType;
  String? timezone;
  String? firebaseToken;
  String? manufacturer;
  String? lastLogin;
  String? lastLogout;
  String? status;

  DeviceModels({
    this.deviceId,
    this.deviceModel,
    this.osType,
    this.osVersion,
    this.appVersion,
    this.appBuildNumber,
    this.deviceLanguage,
    this.screenWidth,
    this.screenHeight,
    this.connectionType,
    this.timezone,
    this.firebaseToken,
    this.manufacturer,
    this.lastLogin,
    this.lastLogout,
    this.status,
  });

  DeviceModels.fromJson(Map json) {
    deviceId = json['deviceId'];
    deviceModel = json['deviceModel'];
    osType = json['osType'];
    osVersion = json['osVersion'];
    appVersion = json['appVersion'];
    appBuildNumber = json['appBuildNumber'];
    deviceLanguage = json['deviceLanguage'];
    screenWidth = json['screenWidth'];
    screenHeight = json['screenHeight'];
    connectionType = json['connectionType'];
    timezone = json['timezone'];
    firebaseToken = json['firebaseToken'];
    manufacturer = json['manufacturer'];
    lastLogin = json['lastLogin'];
    lastLogout = json['lastLogout'];
    status = json['status'];
  }
}
