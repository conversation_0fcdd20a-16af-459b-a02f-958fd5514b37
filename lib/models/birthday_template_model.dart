class BirthdayTemplateModel {
  final int id;
  final String key;
  final String value;
  final String description;

  BirthdayTemplateModel({
    required this.id,
    required this.key,
    required this.value,
    required this.description,
  });

  factory BirthdayTemplateModel.fromJson(Map<String, dynamic> json) {
    return BirthdayTemplateModel(
      id: json['id'] ?? 0,
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      description: json['description'] ?? '',
    );
  }
}
