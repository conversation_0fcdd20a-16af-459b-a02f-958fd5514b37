class TerminationModel {
  TerminationModel({
    required this.id,
    required this.reason,
    required this.status,
    required this.approvalStatus,
    required this.requestedBy,
    required this.target,
    required this.createdAt,
    required this.updatedAt,
    required this.approvalDetails,
    required this.policyTransferInfo,
  });

  final int? id;
  final String? reason;
  final String? status;
  final String? approvalStatus;
  final RequestedBy? requestedBy;
  final RequestedBy? target;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<dynamic> approvalDetails;
  final PolicyTransferInfo? policyTransferInfo;

  factory TerminationModel.fromJson(Map<String, dynamic> json) {
    return TerminationModel(
      id: json["id"],
      reason: json["reason"],
      status: json["status"],
      approvalStatus: json["approvalStatus"],
      requestedBy:
          json["requestedBy"] == null
              ? null
              : RequestedBy.fromJson(json["requestedBy"]),
      target:
          json["target"] == null ? null : RequestedBy.fromJson(json["target"]),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      approvalDetails:
          json["approvalDetails"] == null
              ? []
              : List<dynamic>.from(json["approvalDetails"]!.map((x) => x)),
      policyTransferInfo:
          json["policyTransferInfo"] == null
              ? null
              : PolicyTransferInfo.fromJson(json["policyTransferInfo"]),
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "reason": reason,
    "status": status,
    "approvalStatus": approvalStatus,
    "requestedBy": requestedBy?.toJson(),
    "target": target?.toJson(),
    "createdAt": createdAt?.toIso8601String(),
    "updatedAt": updatedAt?.toIso8601String(),
    "approvalDetails": approvalDetails.map((x) => x).toList(),
    "policyTransferInfo": policyTransferInfo?.toJson(),
  };
}

class PolicyTransferInfo {
  PolicyTransferInfo({
    required this.assignedTo,
    required this.assignedAgentCode,
    required this.assignedAgentPicture,
    required this.level,
    required this.sourceAgentCode,
    required this.sourceAgentName,
    required this.sourceAgentPicture,
    required this.sourceAgentLevel,
    required this.branchCode,
    required this.branchName,
    required this.totalPolicies,
    required this.submittedBy,
    required this.submitterAgentCode,
    required this.status,
    required this.requestedAt,
  });

  final String? assignedTo;
  final String? assignedAgentCode;
  final String? assignedAgentPicture;
  final String? level;
  final String? sourceAgentCode;
  final String? sourceAgentName;
  final String? sourceAgentPicture;
  final String? sourceAgentLevel;
  final String? branchCode;
  final String? branchName;
  final String? totalPolicies;
  final String? submittedBy;
  final String? submitterAgentCode;
  final String? status;
  final DateTime? requestedAt;

  factory PolicyTransferInfo.fromJson(Map<String, dynamic> json) {
    return PolicyTransferInfo(
      assignedTo: json["assignedTo"],
      assignedAgentCode: json["assignedAgentCode"],
      assignedAgentPicture: json["assignedAgentPicture"],
      level: json["level"],
      sourceAgentCode: json["sourceAgentCode"],
      sourceAgentName: json["sourceAgentName"],
      sourceAgentPicture: json["sourceAgentPicture"],
      sourceAgentLevel: json["sourceAgentLevel"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      totalPolicies: json["totalPolicies"],
      submittedBy: json["submittedBy"],
      submitterAgentCode: json["submitterAgentCode"],
      status: json["status"],
      requestedAt: DateTime.tryParse(json["requestedAt"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
    "assignedTo": assignedTo,
    "assignedAgentCode": assignedAgentCode,
    "assignedAgentPicture": assignedAgentPicture,
    "level": level,
    "sourceAgentCode": sourceAgentCode,
    "sourceAgentName": sourceAgentName,
    "sourceAgentPicture": sourceAgentPicture,
    "sourceAgentLevel": sourceAgentLevel,
    "branchCode": branchCode,
    "branchName": branchName,
    "totalPolicies": totalPolicies,
    "submittedBy": submittedBy,
    "submitterAgentCode": submitterAgentCode,
    "status": status,
    "requestedAt": requestedAt?.toIso8601String(),
  };
}

class RequestedBy {
  RequestedBy({
    required this.username,
    required this.name,
    required this.channel,
    required this.agentCode,
    required this.agentLevel,
    required this.picture,
  });

  final String? username;
  final String? name;
  final String? channel;
  final String? agentCode;
  final String? agentLevel;
  final String? picture;

  factory RequestedBy.fromJson(Map<String, dynamic> json) {
    return RequestedBy(
      username: json["username"],
      name: json["name"],
      channel: json["channel"],
      agentCode: json["agentCode"],
      agentLevel: json["agentLevel"],
      picture: json["picture"],
    );
  }

  Map<String, dynamic> toJson() => {
    "username": username,
    "name": name,
    "channel": channel,
    "agentCode": agentCode,
    "agentLevel": agentLevel,
    "picture": picture,
  };
}
