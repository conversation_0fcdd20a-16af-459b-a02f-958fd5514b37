class PromosiAgentModel {
  final String agentCode;
  final String remark;
  final String toAchieveRemark;
  final MetricDataPribadi netApe;
  final MetricData netApeGroup;
  final MetricData agentCount;
  final MetricData newAgentCount;
  final MetricData? leaderCount; // Optional field
  final MetricData persistensi;
  final PelatihanData pelatihan;
  final String status;
  final String statusLisensiAAJI;
  final String statusLisensiAASI;
  final List<String> kekurangan;

  PromosiAgentModel({
    required this.agentCode,
    required this.remark,
    required this.toAchieveRemark,
    required this.netApe,
    required this.netApeGroup,
    required this.agentCount,
    required this.newAgentCount,
    this.leaderCount, // Optional field
    required this.persistensi,
    required this.pelatihan,
    required this.status,
    required this.statusLisensiAAJI,
    required this.statusLisensiAASI,
    required this.kekurangan,
  });

  factory PromosiAgentModel.fromJson(Map<String, dynamic> json) {
    return PromosiAgentModel(
      agentCode: json['agentCode'] ?? '',
      remark: json['remark'] ?? '',
      toAchieveRemark: json['toAchieveRemark'] ?? '',
      netApe: MetricDataPribadi.fromJson(json['netApe'] ?? {}),
      netApeGroup: MetricData.fromJson(json['netApeGroup'] ?? {}),
      agentCount: MetricData.fromJson(json['agentCount'] ?? {}),
      newAgentCount: MetricData.fromJson(json['newAgentCount'] ?? {}),
      leaderCount:
          json['leaderCount'] != null
              ? MetricData.fromJson(json['leaderCount'])
              : null, // Handle optional field
      persistensi: MetricData.fromJson(json['persistensi'] ?? {}),
      pelatihan: PelatihanData.fromJson(json['pelatihan'] ?? {}),
      status: json['status'] ?? '',
      statusLisensiAAJI: json['statusLisensiAAJI'] ?? '',
      statusLisensiAASI: json['statusLisensiAASI'] ?? '',
      kekurangan: List<String>.from(json['kekurangan'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'agentCode': agentCode,
      'remark': remark,
      'toAchieveRemark': toAchieveRemark,
      'netApe': netApe.toJson(),
      'netApeGroup': netApeGroup.toJson(),
      'agentCount': agentCount.toJson(),
      'newAgentCount': newAgentCount.toJson(),
      'persistensi': persistensi.toJson(),
      'pelatihan': pelatihan.toJson(),
      'status': status,
      'statusLisensiAAJI': statusLisensiAAJI,
      'statusLisensiAASI': statusLisensiAASI,
      'kekurangan': kekurangan,
    };

    // Add leaderCount only if it's not null
    if (leaderCount != null) {
      data['leaderCount'] = leaderCount!.toJson();
    }

    return data;
  }
}

class MetricData {
  final double target;
  final double aktual;
  final double kurang;

  MetricData({
    required this.target,
    required this.aktual,
    required this.kurang,
  });

  factory MetricData.fromJson(Map<String, dynamic> json) {
    return MetricData(
      target: (json['target'] ?? 0).toDouble(),
      aktual: (json['aktual'] ?? 0).toDouble(),
      kurang: (json['kurang'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'target': target, 'aktual': aktual, 'kurang': kurang};
  }
}

class MetricDataPribadi {
  final double target;
  final double aktual;
  final double kurang;

  MetricDataPribadi({
    required this.target,
    required this.aktual,
    required this.kurang,
  });

  factory MetricDataPribadi.fromJson(Map<String, dynamic> json) {
    return MetricDataPribadi(
      target: (json['targetMax'] ?? 0).toDouble(),
      aktual: (json['aktual'] ?? 0).toDouble(),
      kurang: (json['kurang'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'target': target, 'aktual': aktual, 'kurang': kurang};
  }
}

class PelatihanData {
  final int completed;
  final int total;

  PelatihanData({required this.completed, required this.total});

  factory PelatihanData.fromJson(Map<String, dynamic> json) {
    return PelatihanData(
      completed: json['completed'] ?? 0,
      total: json['total'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'completed': completed, 'total': total};
  }
}
