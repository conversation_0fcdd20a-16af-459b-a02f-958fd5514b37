class DetailCommissionModel {
  final int? month;
  final int? year;
  final String period;
  final List<CommissionDetailItem> details;
  final double total;

  DetailCommissionModel({
    required this.month,
    required this.year,
    required this.period,
    required this.details,
    required this.total,
  });

  factory DetailCommissionModel.fromJson(Map<String, dynamic> json) {
    return DetailCommissionModel(
      month: json['month'],
      year: json['year'],
      period: json['period'] ?? '',
      details:
          (json['details'] as List<dynamic>?)
              ?.map((item) => CommissionDetailItem.fromJson(item))
              .toList() ??
          [],
      total: (json['total'] ?? 0).toDouble(),
    );
  }
}

class CommissionDetailItem {
  final String year;
  final double amount;
  final double proportion;
  final double? commission;
  final double? overriding;
  final double? bonus;
  final double? referensiAgent;

  CommissionDetailItem({
    required this.year,
    required this.amount,
    required this.proportion,
    this.commission,
    this.overriding,
    this.bonus,
    this.referensiAgent,
  });

  factory CommissionDetailItem.fromJson(Map<String, dynamic> json) {
    return CommissionDetailItem(
      year: json['year'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      proportion: (json['proportion'] ?? 0).toDouble(),
      commission: json['commission']?.toDouble(),
      overriding: json['overriding']?.toDouble(),
      bonus: json['bonus']?.toDouble(),
      referensiAgent: json['referensiAgent']?.toDouble(),
    );
  }
}
