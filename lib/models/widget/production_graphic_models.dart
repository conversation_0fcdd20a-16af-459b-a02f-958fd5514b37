class ProductionGraphicModel {
  int? month;
  int? year;
  NetApeGraphicModel? netApe;

  ProductionGraphicModel({this.month, this.year, this.netApe});
  
  ProductionGraphicModel.fromJson(Map<String, dynamic> json) {
    month = json['month'];
    year = json['year'];
    netApe = json['netApe'] != null ? NetApeGraphicModel.fromJson(json['netApe']) : null;
  }
}

class NetApeGraphicModel {
  num? individu;
  num? team;
  num? group;
  num? branch;
  num? area;

  NetApeGraphicModel({
    this.individu,
    this.team,
    this.group,
    this.branch,
    this.area,
  });

  NetApeGraphicModel.fromJson(Map<String, dynamic> json) {
    individu = json['individu'];
    team = json['team'];
    group = json['group'];
    branch = json['branch'];
    area = json['area'];
  }
}
