class WidgetSpajModels {
  String? agentCode;
  String? agentName;
  String? spajNumber;
  String? entryDate;
  String? policyHolderName;
  String? insuredName;
  num? sumInsured;
  num? basicPremium;
  num? regularTopUp;
  num? singleTopUp;
  String? currency;
  String? frequency;
  String? paymentMethod;
  String? spajStatus;
  String? descriptionPending;
  String? letterNo;
  String? spbType;
  String? acceptanceDate;
  String? submitDate;
  String? flagEM;
  String? collCode;
  String? policyNumber;
  String? autogenDate;
  String? remarkPending;
  String? product;

  WidgetSpajModels({
    this.agentCode,
    this.agentName,
    this.spajNumber,
    this.entryDate,
    this.policyHolderName,
    this.insuredName,
    this.sumInsured,
    this.basicPremium,
    this.regularTopUp,
    this.singleTopUp,
    this.currency,
    this.frequency,
    this.paymentMethod,
    this.spajStatus,
    this.descriptionPending,
    this.letterNo,
    this.spbType,
    this.acceptanceDate,
    this.submitDate,
    this.flagEM,
    this.collCode,
    this.policyNumber,
    this.autogenDate,
    this.remarkPending,
    this.product,
  });
  WidgetSpajModels.fromJson(Map json) {
    agentCode = json['agentCode'];
    agentName = json['agentName'];
    spajNumber = json['spajNumber'];
    entryDate = json['entryDate'];
    policyHolderName = json['policyHolderName'];
    insuredName = json['insuredName'];
    sumInsured = json['sumInsured'];
    basicPremium = json['basicPremium'];
    regularTopUp = json['regularTopUp'];
    singleTopUp = json['singleTopUp'];
    currency = json['currency'];
    frequency = json['frequency'];
    paymentMethod = json['paymentMethod'];
    spajStatus = json['spajStatus'];
    descriptionPending = json['descriptionPending'];
    letterNo = json['letterNo'];
    spbType = json['spbType'];
    acceptanceDate = json['acceptanceDate'];
    submitDate = json['submitDate'];
    flagEM = json['flagEM'];
    collCode = json['collCode'];
    policyNumber = json['policyNumber'];
    autogenDate = json['autogenDate'];
    remarkPending = json['remarkPending'];
    product = json['product'];
  }
}
