import 'package:equatable/equatable.dart';

class KtpOcrModel extends Equatable {
  final String? nik;
  final String? name;
  final String? birthDay;
  final String? placeBirth;
  final String? gender;
  final String? address;
  final String? rt;
  final String? rw;
  final String? subDistrict;
  final String? district;
  final String? province;
  final String? city;
  final String? religion;
  final String? marital;
  final String? occupation;
  final String? nationality;
  final String? validUntil;
  final List<String>? qualityIssues;

  const KtpOcrModel({
    this.nik,
    this.name,
    this.birthDay,
    this.placeBirth,
    this.gender,
    this.address,
    this.rt,
    this.rw,
    this.subDistrict,
    this.district,
    this.province,
    this.city,
    this.religion,
    this.marital,
    this.occupation,
    this.nationality,
    this.validUntil,
    this.qualityIssues,
  });

  @override
  List<Object?> get props => [
    nik,
    name,
    birthDay,
    placeBirth,
    gender,
    address,
    rt,
    rw,
    subDistrict,
    district,
    province,
    city,
    religion,
    marital,
    occupation,
    nationality,
    validUntil,
    qualityIssues,
  ];

  /// Convert to JSON format compatible with existing best_ktp_ocr_flutter format
  Map<String, dynamic> toJson() {
    return {
      'nik': nik ?? '',
      'nama': name ?? '',
      'tempat_lahir': placeBirth ?? '',
      'tanggal_lahir': birthDay ?? '',
      'jenis_kelamin': gender ?? '',
      'alamat': address ?? '',
      'rt': rt ?? '',
      'rw': rw ?? '',
      'rtrw': rt != null && rw != null ? '$rt/$rw' : '',
      'kelurahan': subDistrict ?? '',
      'kecamatan': district ?? '',
      'provinsi': province ?? '',
      'kota': city ?? '',
      'agama': religion ?? '',
      'status_perkawinan': marital ?? '',
      'pekerjaan': occupation ?? '',
      'kewarganegaraan': nationality ?? '',
      'berlaku_hingga': validUntil ?? '',
    };
  }

  /// Create from JSON format compatible with existing best_ktp_ocr_flutter format
  factory KtpOcrModel.fromJson(Map<String, dynamic> json) {
    return KtpOcrModel(
      nik: json['nik']?.toString(),
      name: json['nama']?.toString(),
      placeBirth: json['tempat_lahir']?.toString(),
      birthDay: json['tanggal_lahir']?.toString(),
      gender: json['jenis_kelamin']?.toString(),
      address: json['alamat']?.toString(),
      rt: json['rt']?.toString(),
      rw: json['rw']?.toString(),
      subDistrict: json['kelurahan']?.toString(),
      district: json['kecamatan']?.toString(),
      province: json['provinsi']?.toString(),
      city: json['kota']?.toString(),
      religion: json['agama']?.toString(),
      marital: json['status_perkawinan']?.toString(),
      occupation: json['pekerjaan']?.toString(),
      nationality: json['kewarganegaraan']?.toString(),
      validUntil: json['berlaku_hingga']?.toString(),
    );
  }

  KtpOcrModel copyWith({
    String? nik,
    String? name,
    String? birthDay,
    String? placeBirth,
    String? gender,
    String? address,
    String? rt,
    String? rw,
    String? subDistrict,
    String? district,
    String? province,
    String? city,
    String? religion,
    String? marital,
    String? occupation,
    String? nationality,
    String? validUntil,
    List<String>? qualityIssues,
  }) {
    return KtpOcrModel(
      nik: nik ?? this.nik,
      name: name ?? this.name,
      birthDay: birthDay ?? this.birthDay,
      placeBirth: placeBirth ?? this.placeBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      rt: rt ?? this.rt,
      rw: rw ?? this.rw,
      subDistrict: subDistrict ?? this.subDistrict,
      district: district ?? this.district,
      province: province ?? this.province,
      city: city ?? this.city,
      religion: religion ?? this.religion,
      marital: marital ?? this.marital,
      occupation: occupation ?? this.occupation,
      nationality: nationality ?? this.nationality,
      validUntil: validUntil ?? this.validUntil,
      qualityIssues: qualityIssues ?? this.qualityIssues,
    );
  }

  /// Check if there are any quality issues with the image
  bool get hasQualityIssues =>
      qualityIssues != null && qualityIssues!.isNotEmpty;

  /// Get quality issues as a formatted string
  String get qualityIssuesMessage {
    if (!hasQualityIssues) return '';
    return qualityIssues!.join(', ');
  }
}
