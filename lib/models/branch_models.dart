class BranchModels {
  int? id;
  String? branchCode;
  String? branchName;
  String? parentBranchCode;
  String? parentBranchName;
  String? phoneNumber;
  int? staffCount;
  String? city;
  String? address;
  String? secondAddress;
  String? thirdAddress;
  String? googleMapsUrl;
  String? latitude;
  String? longitude;
  bool? isActive;
  String? channel;
  String? createdAt;
  String? updatedAt;

  BranchModels({
    this.id,
    this.branchCode,
    this.branchName,
    this.parentBranchCode,
    this.parentBranchName,
    this.phoneNumber,
    this.staffCount,
    this.city,
    this.address,
    this.secondAddress,
    this.thirdAddress,
    this.googleMapsUrl,
    this.latitude,
    this.longitude,
    this.isActive,
    this.channel,
    this.createdAt,
    this.updatedAt,
  });

  BranchModels.fromJson(Map json) {
    id = json['id'];
    branchCode = json['branchCode'];
    branchName = json['branchName'];
    parentBranchCode = json['parentBranchCode'];
    parentBranchName = json['parentBranchName'];
    phoneNumber = json['phoneNumber'];
    staffCount = json['staffCount'];
    city = json['city'];
    address = json['address'];
    secondAddress = json['secondAddress'];
    thirdAddress = json['thirdAddress'];
    googleMapsUrl = json['googleMapsUrl'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    isActive = json['isActive'];
    channel = json['channel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['branchCode'] = branchCode;
    data['branchName'] = branchName;
    data['parentBranchCode'] = parentBranchCode;
    data['parentBranchName'] = parentBranchName;
    data['phoneNumber'] = phoneNumber;
    data['staffCount'] = staffCount;
    data['city'] = city;
    data['address'] = address;
    data['secondAddress'] = secondAddress;
    data['thirdAddress'] = thirdAddress;
    data['googleMapsUrl'] = googleMapsUrl;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['isActive'] = isActive;
    data['channel'] = channel;
    data['createdAt'] = createdAt;
    data['updatedAt'] = updatedAt;
    return data;
  }
}
