class SimpleProfileModels {
  String? agentCode;
  String? agentName;
  String? agentPhoto;
  String? agentLevel;

  SimpleProfileModels({
    this.agentCode,
    this.agentName,
    this.agentPhoto,
    this.agentLevel,
  });

  SimpleProfileModels.fromJson(Map<String, dynamic> json) {
    agentCode = json['agentCode'];
    agentName = json['agentName'];
    agentPhoto = json['agentPhoto'];
    agentLevel = json['agentLevel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['agentCode'] = agentCode;
    data['agentName'] = agentName;
    data['agentPhoto'] = agentPhoto;
    data['agentLevel'] = agentLevel;
    return data;
  }
}
