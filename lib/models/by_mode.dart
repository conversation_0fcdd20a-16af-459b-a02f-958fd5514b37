import 'package:pdl_superapp/models/branch_models.dart';
import 'package:pdl_superapp/models/role_model.dart';

class By {
  final String? username;
  final String? name;
  final String? channel;
  final String? agentCode;
  final String? agentLevel;
  final String? picture;
  final List<BranchModels>? branches;
  final List<Role>? roles;

  By({
    this.username,
    this.name,
    this.channel,
    this.agentCode,
    this.agentLevel,
    this.branches,
    this.picture,
    this.roles,
  });

  factory By.fromJson(Map<String, dynamic> json) => By(
    username: json["username"],
    name: json["name"],
    channel: json["channel"],
    agentCode: json["agentCode"],
    agentLevel: json["agentLevel"],
    picture: json["picture"],
    branches:
        json["branches"] == null
            ? []
            : List<BranchModels>.from(
              json["branches"]!.map((x) => BranchModels.fromJson(x)),
            ),
    roles:
        json["roles"] == null
            ? []
            : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "username": username,
    "name": name,
    "channel": channel,
    "agentCode": agentCode,
    "agentLevel": agentLevel,
    "picture": picture,
    "branches":
        branches == null
            ? []
            : List<dynamic>.from(branches!.map((x) => x.toJson())),
    "roles":
        roles == null ? [] : List<dynamic>.from(roles!.map((x) => x.toJson())),
  };
}
