class ComboCategoryModels {
  String? key;
  String? value;
  String? value2;
  String? value3;

  ComboCategoryModels({
    this.key,
    this.value,
    this.value2,
    this.value3,
  });

  ComboCategoryModels.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
    value2 = json['value2'];
    value3 = json['value3'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['value'] = value;
    data['value2'] = value2;
    data['value3'] = value3;
    return data;
  }
}
