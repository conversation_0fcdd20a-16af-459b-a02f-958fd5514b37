class PolicyOverdueModel {
  final String agentCode;
  final String agentName;
  final String policyNumber;
  final String product;
  final String policyHolder;
  final String insuredName;
  final String lastPaymentDate;
  final String dueDate;
  final String mobileNumber;
  final String emailAddress;
  final String paymentFrequency;
  final String paymentMethod;
  final String currency;
  final double billingAmount;
  final String policyTerm;
  final String paymentTerm;
  final String backColor;
  final String flagNlg;
  final String nlgBackColor;

  PolicyOverdueModel({
    required this.agentCode,
    required this.agentName,
    required this.policyNumber,
    required this.product,
    required this.policyHolder,
    required this.insuredName,
    required this.lastPaymentDate,
    required this.dueDate,
    required this.mobileNumber,
    required this.emailAddress,
    required this.paymentFrequency,
    required this.paymentMethod,
    required this.currency,
    required this.billingAmount,
    required this.policyTerm,
    required this.paymentTerm,
    required this.backColor,
    required this.flagNlg,
    required this.nlgBackColor,
  });

  factory PolicyOverdueModel.fromJson(Map<String, dynamic> json) {
    return PolicyOverdueModel(
      agentCode: json['agentCode'] ?? '',
      agentName: json['agentName'] ?? '',
      policyNumber: json['policyNumber'] ?? '',
      product: json['product'] ?? '',
      policyHolder: json['policyHolder'] ?? '',
      insuredName: json['insuredName'] ?? '',
      lastPaymentDate: json['lastPaymentDate'] ?? '',
      dueDate: json['dueDate'] ?? '',
      mobileNumber: json['mobileNumber'] ?? '',
      emailAddress: json['emailAddress'] ?? '',
      paymentFrequency: json['paymentFrequency'] ?? '',
      paymentMethod: json['paymentMethod'] ?? '',
      currency: json['currency'] ?? '',
      billingAmount: (json['billingAmount'] ?? 0).toDouble(),
      policyTerm: json['policyTerm'] ?? '',
      paymentTerm: json['paymentTerm'] ?? '',
      backColor: json['backColor'] ?? '',
      flagNlg: json['flagNlg'] ?? '',
      nlgBackColor: json['nlgBackColor'] ?? '',
    );
  }
}
