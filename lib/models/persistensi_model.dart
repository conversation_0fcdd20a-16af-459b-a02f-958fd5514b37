class PersistensiModel {
  final String agentCode;
  final String agentLevel;
  final String agentPhoto;
  final String name;
  final String type;
  final int year;
  final double persistency13;
  final double persistency25;
  final double persistency37;
  final double persistency49;
  final double persistency61;

  PersistensiModel({
    required this.agentCode,
    required this.agentLevel,
    required this.agentPhoto,
    required this.name,
    required this.type,
    required this.year,
    required this.persistency13,
    required this.persistency25,
    required this.persistency37,
    required this.persistency49,
    required this.persistency61,
  });

  factory PersistensiModel.fromJson(Map<String, dynamic> json) {
    return PersistensiModel(
      agentCode: json['agentCode'] ?? '',
      agentLevel: json['agentLevel'] ?? '',
      agentPhoto: json['agentPhoto'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      year: json['year'] ?? 0,
      persistency13: (json['persistency13'] ?? 0).toDouble(),
      persistency25: (json['persistency25'] ?? 0).toDouble(),
      persistency37: (json['persistency37'] ?? 0).toDouble(),
      persistency49: (json['persistency49'] ?? 0).toDouble(),
      persistency61: (json['persistency61'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agentCode': agentCode,
      'agentLevel': agentLevel,
      'agentPhoto': agentPhoto,
      'name': name,
      'type': type,
      'year': year,
      'persistency13': persistency13,
      'persistency25': persistency25,
      'persistency37': persistency37,
      'persistency49': persistency49,
      'persistency61': persistency61,
    };
  }
}
