class ClaimTrackingModel {
  final String claimId;
  final String policyNumber;
  final String agentCode;
  final String agentName;
  final double amount;
  final String product;
  final String policyHolder;
  final String insuredName;
  final String claimStatus;
  final String claimType;
  final String dateOfDeath;
  final String admissionDate;
  final String dischargeDate;
  final String remark;
  final String remarkDate;
  final String requestedDate;
  final String documentCompleteDate;
  final String lastUpdateDate;
  final String currency;
  final String letter;
  final int rowSpan;
  final String diagnose;
  final double sumInsured;
  final String mainBranch;
  final String productName;
  final double bpa;

  ClaimTrackingModel({
    required this.claimId,
    required this.policyNumber,
    required this.agentCode,
    required this.agentName,
    required this.amount,
    required this.product,
    required this.policyHolder,
    required this.insuredName,
    required this.claimStatus,
    required this.claimType,
    required this.dateOfDeath,
    required this.admissionDate,
    required this.dischargeDate,
    required this.remark,
    required this.remarkDate,
    required this.requestedDate,
    required this.documentCompleteDate,
    required this.lastUpdateDate,
    required this.currency,
    required this.letter,
    required this.rowSpan,
    required this.diagnose,
    required this.sumInsured,
    required this.mainBranch,
    required this.productName,
    required this.bpa,
  });

  factory ClaimTrackingModel.fromJson(Map<String, dynamic> json) {
    return ClaimTrackingModel(
      claimId: json['claimId'] ?? '',
      policyNumber: json['policyNumber'] ?? '',
      agentCode: json['agentCode'] ?? '',
      agentName: json['agentName'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      product: json['product'] ?? '',
      policyHolder: json['policyHolder'] ?? '',
      insuredName: json['insuredName'] ?? '',
      claimStatus: json['claimStatus'] ?? '',
      claimType: json['claimType'] ?? '',
      dateOfDeath: json['dateOfDeath'] ?? '',
      admissionDate: json['admissionDate'] ?? '',
      dischargeDate: json['dischargeDate'] ?? '',
      remark: json['remark'] ?? '',
      remarkDate: json['remarkDate'] ?? '',
      requestedDate: json['requestedDate'] ?? '',
      documentCompleteDate: json['documentCompleteDate'] ?? '',
      lastUpdateDate: json['lastUpdateDate'] ?? '',
      currency: json['currency'] ?? '',
      letter: json['letter'] ?? '',
      rowSpan: json['rowSpan'] ?? 0,
      diagnose: json['diagnose'] ?? '',
      sumInsured: (json['sumInsured'] ?? 0).toDouble(),
      mainBranch: json['mainBranch'] ?? '',
      productName: json['productName'] ?? '',
      bpa: (json['bpa'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'claimId': claimId,
      'policyNumber': policyNumber,
      'agentCode': agentCode,
      'agentName': agentName,
      'amount': amount,
      'product': product,
      'policyHolder': policyHolder,
      'insuredName': insuredName,
      'claimStatus': claimStatus,
      'claimType': claimType,
      'dateOfDeath': dateOfDeath,
      'admissionDate': admissionDate,
      'dischargeDate': dischargeDate,
      'remark': remark,
      'remarkDate': remarkDate,
      'requestedDate': requestedDate,
      'documentCompleteDate': documentCompleteDate,
      'lastUpdateDate': lastUpdateDate,
      'currency': currency,
      'letter': letter,
      'rowSpan': rowSpan,
      'diagnose': diagnose,
      'sumInsured': sumInsured,
      'mainBranch': mainBranch,
      'productName': productName,
      'bpa': bpa,
    };
  }
}
