class PolicyTransferModel {
  PolicyTransferModel({
    required this.assignedTo,
    required this.assignedAgentCode,
    required this.assignedAgentPicture,
    required this.level,
    required this.sourceAgentCode,
    required this.sourceAgentName,
    required this.sourceAgentPicture,
    required this.sourceAgentLevel,
    required this.branchCode,
    required this.branchName,
    required this.totalPolicies,
    required this.submittedBy,
    required this.submitterAgentCode,
    required this.status,
    required this.requestedAt,
    required this.trxTerminationId,
  });

  final String? assignedTo;
  final String? assignedAgentCode;
  final String? assignedAgentPicture;
  final String? level;
  final String? sourceAgentCode;
  final String? sourceAgentName;
  final String? sourceAgentPicture;
  final String? sourceAgentLevel;
  final String? branchCode;
  final String? branchName;
  final int? totalPolicies;
  final String? submittedBy;
  final String? submitterAgentCode;
  final String? status;
  final DateTime? requestedAt;
  final int? trxTerminationId;

  factory PolicyTransferModel.fromJson(Map<String, dynamic> json) {
    return PolicyTransferModel(
      assignedTo: json["assignedTo"],
      assignedAgentCode: json["assignedAgentCode"],
      assignedAgentPicture: json["assignedAgentPicture"],
      level: json["level"],
      sourceAgentCode: json["sourceAgentCode"],
      sourceAgentName: json["sourceAgentName"],
      sourceAgentPicture: json["sourceAgentPicture"],
      sourceAgentLevel: json["sourceAgentLevel"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      totalPolicies: json["totalPolicies"],
      submittedBy: json["submittedBy"],
      submitterAgentCode: json["submitterAgentCode"],
      status: json["status"],
      requestedAt: DateTime.tryParse(json["requestedAt"] ?? ""),
      trxTerminationId: json["trxTerminationId"],
    );
  }

  Map<String, dynamic> toJson() => {
    "assignedTo": assignedTo,
    "assignedAgentCode": assignedAgentCode,
    "assignedAgentPicture": assignedAgentPicture,
    "level": level,
    "sourceAgentCode": sourceAgentCode,
    "sourceAgentName": sourceAgentName,
    "sourceAgentPicture": sourceAgentPicture,
    "sourceAgentLevel": sourceAgentLevel,
    "branchCode": branchCode,
    "branchName": branchName,
    "totalPolicies": totalPolicies,
    "submittedBy": submittedBy,
    "submitterAgentCode": submitterAgentCode,
    "status": status,
    "requestedAt": requestedAt?.toIso8601String(),
    "trxTerminationId": trxTerminationId,
  };
}
