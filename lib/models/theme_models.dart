class ThemeAssetModel {
  final int id;
  final String url;
  final String fileName;

  ThemeAssetModel({
    required this.id,
    required this.url,
    required this.fileName,
  });

  factory ThemeAssetModel.fromJson(Map<String, dynamic> json) {
    return ThemeAssetModel(
      id: json['id'] ?? 0,
      url: json['url'] ?? '',
      fileName: json['fileName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'fileName': fileName,
    };
  }
}

class ThemeModel {
  final int id;
  final String key;
  final String name;
  final String description;
  final DateTime? startDate;
  final DateTime? endDate;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ThemeAssetModel> assets;

  ThemeModel({
    required this.id,
    required this.key,
    required this.name,
    required this.description,
    this.startDate,
    this.endDate,
    required this.status,
    this.createdAt,
    this.updatedAt,
    required this.assets,
  });

  factory ThemeModel.fromJson(Map<String, dynamic> json) {
    List<ThemeAssetModel> assetsList = [];
    if (json['assets'] != null) {
      assetsList = List<ThemeAssetModel>.from(
        (json['assets'] as List).map(
          (asset) => ThemeAssetModel.fromJson(asset),
        ),
      );
    }

    return ThemeModel(
      id: json['id'] ?? 0,
      key: json['key'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'])
          : null,
      endDate:
          json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      status: json['status'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
      assets: assetsList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'key': key,
      'name': name,
      'description': description,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'status': status,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'assets': assets.map((asset) => asset.toJson()).toList(),
    };
  }

  // Create an empty theme model for initial state
  factory ThemeModel.empty() {
    return ThemeModel(
      id: 0,
      key: '',
      name: '',
      description: '',
      status: '',
      assets: [],
    );
  }
}
