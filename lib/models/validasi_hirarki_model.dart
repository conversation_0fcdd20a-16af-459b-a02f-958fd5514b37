class ValidasiHirarkiModel {
  final String agentCode;
  final String branchCode;
  final String mainBranchCode;
  final String bdmCode;
  final String bdmName;
  final String abddCode;
  final String abddName;
  final String bddCode;
  final String bddName;
  final String hosCode;
  final String hosName;
  final int year;
  final String type;
  final ValidasiData validasiPerHirarki;
  final ValidasiData? validasiPerG1;
  final String statusLisensiAAJI;
  final String statusLisensiAASI;
  final List<String> kekurangan;

  ValidasiHirarkiModel({
    required this.agentCode,
    required this.branchCode,
    required this.mainBranchCode,
    required this.bdmCode,
    required this.bdmName,
    required this.abddCode,
    required this.abddName,
    required this.bddCode,
    required this.bddName,
    required this.hosCode,
    required this.hosName,
    required this.year,
    required this.type,
    required this.validasiPerHirarki,
    this.validasiPerG1,
    required this.statusLisensiAAJI,
    required this.statusLisensiAASI,
    required this.kekurangan,
  });

  factory ValidasiHirarkiModel.fromJson(Map<String, dynamic> json) {
    return ValidasiHirarkiModel(
      agentCode: json['agentCode'] ?? '',
      branchCode: json['branchCode'] ?? '',
      mainBranchCode: json['mainBranchCode'] ?? '',
      bdmCode: json['bdmCode'] ?? '',
      bdmName: json['bdmName'] ?? '',
      abddCode: json['abddCode'] ?? '',
      abddName: json['abddName'] ?? '',
      bddCode: json['bddCode'] ?? '',
      bddName: json['bddName'] ?? '',
      hosCode: json['hosCode'] ?? '',
      hosName: json['hosName'] ?? '',
      year: json['year'] ?? 0,
      type: json['type'] ?? '',
      validasiPerHirarki: ValidasiData.fromJson(json['validasiPerHirarki'] ?? {}),
      validasiPerG1: json['validasiPerG1'] != null ? ValidasiData.fromJson(json['validasiPerG1']) : null,
      statusLisensiAAJI: json['statusLisensiAAJI'] ?? '',
      statusLisensiAASI: json['statusLisensiAASI'] ?? '',
      kekurangan: List<String>.from(json['kekurangan'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agentCode': agentCode,
      'branchCode': branchCode,
      'mainBranchCode': mainBranchCode,
      'bdmCode': bdmCode,
      'bdmName': bdmName,
      'abddCode': abddCode,
      'abddName': abddName,
      'bddCode': bddCode,
      'bddName': bddName,
      'hosCode': hosCode,
      'hosName': hosName,
      'year': year,
      'type': type,
      'validasiPerHirarki': validasiPerHirarki.toJson(),
      'validasiPerG1': validasiPerG1?.toJson(),
      'statusLisensiAAJI': statusLisensiAAJI,
      'statusLisensiAASI': statusLisensiAASI,
      'kekurangan': kekurangan,
    };
  }
}

class ValidasiData {
  final MetricData netPolis;
  final MetricData netApe;
  final MetricData agentCount;
  final MetricData leaderCount;
  final MetricData persistensi;
  final PelatihanData pelatihan;
  final String status;
  final MetricData? netApeG1; // Optional field for G1 validation
  final MetricData? g1Count; // Optional field for G1 validation
  final MetricData? gnetApe; // Optional field for all generations validation

  ValidasiData({
    required this.netPolis,
    required this.netApe,
    required this.agentCount,
    required this.leaderCount,
    required this.persistensi,
    required this.pelatihan,
    required this.status,
    this.netApeG1, // Optional field
    this.g1Count, // Optional field
    this.gnetApe, // Optional field
  });

  factory ValidasiData.fromJson(Map<String, dynamic> json) {
    return ValidasiData(
      netPolis: MetricData.fromJson(json['netPolis'] ?? {}),
      netApe: MetricData.fromJson(json['netApe'] ?? {}),
      agentCount: MetricData.fromJson(json['agentCount'] ?? {}),
      leaderCount: MetricData.fromJson(json['leaderCount'] ?? {}),
      persistensi: MetricData.fromJson(json['persistensi'] ?? {}),
      pelatihan: PelatihanData.fromJson(json['pelatihan'] ?? {}),
      status: json['status'] ?? '',
      netApeG1: json['netApeG1'] != null ? MetricData.fromJson(json['netApeG1']) : null,
      g1Count: json['g1Count'] != null ? MetricData.fromJson(json['g1Count']) : null,
      gnetApe: json['gnetApe'] != null ? MetricData.fromJson(json['gnetApe']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'netPolis': netPolis.toJson(),
      'netApe': netApe.toJson(),
      'agentCount': agentCount.toJson(),
      'leaderCount': leaderCount.toJson(),
      'persistensi': persistensi.toJson(),
      'pelatihan': pelatihan.toJson(),
      'status': status,
    };

    // Add optional fields if they exist
    if (netApeG1 != null) data['netApeG1'] = netApeG1!.toJson();
    if (g1Count != null) data['g1Count'] = g1Count!.toJson();
    if (gnetApe != null) data['gnetApe'] = gnetApe!.toJson();

    return data;
  }
}

class MetricData {
  final double target;
  final double aktual;
  final double kurang;

  MetricData({required this.target, required this.aktual, required this.kurang});

  factory MetricData.fromJson(Map<String, dynamic> json) {
    return MetricData(
      target: (json['target'] ?? 0).toDouble(),
      aktual: (json['aktual'] ?? 0).toDouble(),
      kurang: (json['kurang'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {'target': target, 'aktual': aktual, 'kurang': kurang};
  }
}

class PelatihanData {
  final int completed;
  final int total;

  PelatihanData({required this.completed, required this.total});

  factory PelatihanData.fromJson(Map<String, dynamic> json) {
    return PelatihanData(completed: json['completed'] ?? 0, total: json['total'] ?? 0);
  }

  Map<String, dynamic> toJson() {
    return {'completed': completed, 'total': total};
  }
}
