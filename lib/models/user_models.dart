class UserModels {
  int? id;
  String? leaderCode;
  String? agentCode;
  String? agentName;
  String? distributionCode;
  String? roleName;
  String? level;
  String? positionLevel;
  String? regionCode;
  String? regionName;
  String? subRegionCode;
  String? subRegionName;
  String? areaCode;
  String? areaName;
  String? branchCode;
  String? branchName;
  String? groupCode;
  String? groupName;
  String? licenseNumberAAJI;
  String? licenseExpiredDateAAJI;
  String? licenseNumberAASI;
  String? licenseExpiredDateAASI;
  String? leaderG2G;
  String? recruiterCode;
  String? dob;
  String? gender;
  String? education;
  String? status;
  String? channel;
  String? email;
  String? phoneNumber;
  String? address;
  String? bankAccountNumber;
  String? bank;
  String? maritalStatus;
  String? bankAttachment;
  String? ktpAttachment;
  String? kkAttachment;
  String? photo;
  String? createdAt;
  String? updatedAt;
  String? mbranchCode;
  String? mbranchName;
  String? sbranchCode;
  String? sbranchName;
  String? username;
  String? name;
  String? phone;
  String? picture;
  String? agentLevel;
  String? timezone;
  bool? isAgent;
  String? userType;
  String? lastLogin;
  String? lastChangePassword;
  List<RolesModels>? rolesList;
  List<BranchModels>? branches;
  RolesModels? roles;

  UserModels({
    id,
    this.leaderCode,
    this.agentCode,
    this.agentName,
    this.distributionCode,
    this.roleName,
    this.level,
    this.positionLevel,
    this.regionCode,
    this.regionName,
    this.subRegionCode,
    this.subRegionName,
    this.areaCode,
    this.areaName,
    this.branchCode,
    this.branchName,
    this.groupCode,
    this.groupName,
    this.licenseNumberAAJI,
    this.licenseExpiredDateAAJI,
    this.licenseNumberAASI,
    this.licenseExpiredDateAASI,
    this.leaderG2G,
    this.recruiterCode,
    this.dob,
    this.gender,
    this.education,
    this.status,
    this.channel,
    this.email,
    this.phoneNumber,
    this.address,
    this.bankAccountNumber,
    this.bank,
    this.maritalStatus,
    this.bankAttachment,
    this.ktpAttachment,
    this.kkAttachment,
    this.photo,
    this.createdAt,
    this.updatedAt,
    this.mbranchCode,
    this.mbranchName,
    this.sbranchCode,
    this.sbranchName,
    this.username,
    this.name,
    this.phone,
    this.picture,
    this.agentLevel,
    this.timezone,
    this.isAgent,
    this.userType,
    this.lastLogin,
    this.lastChangePassword,
    this.rolesList,
    this.branches,
    this.roles,
  });
  UserModels.fromJson(Map json) {
    id = json['id'];
    leaderCode = json['leaderCode'];
    agentCode = json['agentCode'];
    agentName = json['agentName'] ?? json['name'];
    distributionCode = json['distributionCode'];
    roleName = json['roleName'];
    level = json['level'];
    positionLevel = json['positionLevel'];
    regionCode = json['regionCode'];
    regionName = json['regionName'];
    subRegionCode = json['subRegionCode'];
    subRegionName = json['subRegionName'];
    areaCode = json['areaCode'];
    areaName = json['areaName'];
    branchCode = json['branchCode'];
    branchName = json['branchName'];
    groupCode = json['groupCode'];
    groupName = json['groupName'];
    licenseNumberAAJI = json['licenseNumberAAJI'];
    licenseExpiredDateAAJI = json['licenseExpiredDateAAJI'];
    licenseNumberAASI = json['licenseNumberAASI'];
    licenseExpiredDateAASI = json['licenseExpiredDateAASI'];
    leaderG2G = json['leaderG2G'];
    recruiterCode = json['recruiterCode'];
    dob = json['dob'];
    gender = json['gender'];
    education = json['education'];
    status = json['status'];
    channel = json['channel'];
    email = json['email'];
    phoneNumber = json['phoneNumber'];
    address = json['address'];
    bankAccountNumber = json['bankAccountNumber'];
    bank = json['bank'];
    maritalStatus = json['maritalStatus'];
    bankAttachment = json['bankAttachment'];
    ktpAttachment = json['ktpAttachment'];
    kkAttachment = json['kkAttachment'];
    photo = json['photo'] ?? json['picture'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    mbranchCode = json['mbranchCode'];
    mbranchName = json['mbranchName'];
    sbranchCode = json['sbranchCode'];
    sbranchName = json['sbranchName'];
    username = json['username'];
    name = json['name'];
    phone = json['phone'];
    picture = json['picture'];
    agentLevel = json['agentLevel'];
    timezone = json['timezone'];
    isAgent = json['isAgent'];
    userType = json['userType'];
    lastLogin = json['lastLogin'];
    lastChangePassword = json['lastChangePassword'];

    if (json['roles'] != null) {
      if (json['roles'].isNotEmpty) {
        // For backward compatibility
        RolesModels item = RolesModels.fromJSon(json['roles'][0]);
        roles = item;

        // For new response format
        rolesList = <RolesModels>[];
        json['roles'].forEach((v) {
          rolesList!.add(RolesModels.fromJSon(v));
        });
      }
    }

    if (json['branches'] != null) {
      branches = <BranchModels>[];
      json['branches'].forEach((v) {
        branches!.add(BranchModels.fromJson(v));
      });
    }
  }
}

class RolesModels {
  int? id;
  String? code;
  String? name;
  List<AccessModels>? accesses;

  RolesModels({this.id, this.code, this.name, this.accesses});

  RolesModels.fromJSon(Map json) {
    id = json['id'];
    code = json['code'];
    name = json['name'];

    if (json['accesses'] != null) {
      accesses = <AccessModels>[];
      json['accesses'].forEach((v) {
        accesses!.add(AccessModels.fromJson(v));
      });
    }
  }
}

class AccessModels {
  int? id;
  String? category;
  String? domain;
  String? action;

  AccessModels({this.id, this.category, this.domain, this.action});

  AccessModels.fromJson(Map json) {
    id = json['id'];
    category = json['category'];
    domain = json['domain'];
    action = json['action'];
  }
}

class BranchModels {
  int? id;
  String? branchCode;
  String? branchName;
  String? parentBranchCode;
  String? parentBranchName;
  String? phoneNumber;
  int? staffCount;
  String? city;
  String? address;
  String? secondAddress;
  String? thirdAddress;
  String? googleMapsUrl;
  double? latitude;
  double? longitude;
  bool? isActive;
  String? channel;
  String? createdAt;
  String? updatedAt;

  BranchModels({
    this.id,
    this.branchCode,
    this.branchName,
    this.parentBranchCode,
    this.parentBranchName,
    this.phoneNumber,
    this.staffCount,
    this.city,
    this.address,
    this.secondAddress,
    this.thirdAddress,
    this.googleMapsUrl,
    this.latitude,
    this.longitude,
    this.isActive,
    this.channel,
    this.createdAt,
    this.updatedAt,
  });

  BranchModels.fromJson(Map json) {
    id = json['id'];
    branchCode = json['branchCode'];
    branchName = json['branchName'];
    parentBranchCode = json['parentBranchCode'];
    parentBranchName = json['parentBranchName'];
    phoneNumber = json['phoneNumber'];
    staffCount = json['staffCount'];
    city = json['city'];
    address = json['address'];
    secondAddress = json['secondAddress'];
    thirdAddress = json['thirdAddress'];
    googleMapsUrl = json['googleMapsUrl'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    isActive = json['isActive'];
    channel = json['channel'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }
}
