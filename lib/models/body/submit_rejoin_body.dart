import 'dart:convert';

SubmitRejoinBody submitRejoinBodyFromJson(String str) =>
    SubmitRejoinBody.fromJson(json.decode(str));

String submitRejoinBodyToJson(SubmitRejoinBody data) =>
    json.encode(data.toJson());

class SubmitRejoinBody {
  final String? agentCode;
  final String? proposedLevel;
  final String? proposedLeaderCode;
  final String? branchCode;
  final String? remarks;
  final String? uploadedKtpPath;

  SubmitRejoinBody({
    this.agentCode,
    this.proposedLevel,
    this.proposedLeaderCode,
    this.branchCode,
    this.remarks,
    this.uploadedKtpPath,
  });

  factory SubmitRejoinBody.fromJson(Map<String, dynamic> json) =>
      SubmitRejoinBody(
        agentCode: json["agentCode"],
        proposedLevel: json["proposedLevel"],
        proposedLeaderCode: json["proposedLeaderCode"],
        branchCode: json["branchCode"],
        remarks: json["remarks"],
        uploadedKtpPath: json["uploadedKtpPath"],
      );

  Map<String, dynamic> toJson() => {
    "agentCode": agentCode,
    "proposedLevel": proposedLevel,
    "proposedLeaderCode": proposedLeaderCode,
    "branchCode": branchCode,
    "remarks": remarks,
    "uploadedKtpPath": uploadedKtpPath,
  };
}
