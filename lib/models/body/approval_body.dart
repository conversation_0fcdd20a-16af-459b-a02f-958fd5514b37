import 'dart:convert';

ApprovalBody approvalBodyFromJson(String str) =>
    ApprovalBody.fromJson(json.decode(str));

String approvalBodyToJson(ApprovalBody data) => json.encode(data.toJson());

class ApprovalBody {
  final int? approvalHeaderId;
  final String? action;
  final List<ApprovalDetailBody>? approvalDetails;
  final String? remarks;

  ApprovalBody({
    this.approvalHeaderId,
    this.action,
    this.approvalDetails,
    this.remarks,
  });

  factory ApprovalBody.fromJson(Map<String, dynamic> json) => ApprovalBody(
    approvalHeaderId: json["approvalHeaderId"],
    action: json["action"],
    approvalDetails:
        json["approvalDetails"] == null
            ? []
            : List<ApprovalDetailBody>.from(
              json["approvalDetails"]!.map(
                (x) => ApprovalDetailBody.fromJson(x),
              ),
            ),
    remarks: json["remarks"],
  );

  Map<String, dynamic> toJson() => {
    "approvalHeaderId": approvalHeaderId,
    "action": action,
    "approvalDetails":
        approvalDetails == null
            ? []
            : List<dynamic>.from(approvalDetails!.map((x) => x.toJson())),
    "remarks": remarks,
  };
}

class ApprovalDetailBody {
  final String? field;
  final String? action;
  final String? remark;

  ApprovalDetailBody({this.field, this.action, this.remark});

  factory ApprovalDetailBody.fromJson(Map<String, dynamic> json) =>
      ApprovalDetailBody(
        field: json["field"],
        action: json["action"],
        remark: json["remark"],
      );

  Map<String, dynamic> toJson() => {
    "field": field,
    "action": action,
    "remark": remark,
  };
}
