class Role {
  final String? code;
  final String? name;
  final String? channel;
  final String? platform;

  Role({this.code, this.name, this.channel, this.platform});

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      code: json['code'] as String?,
      name: json['name'] as String?,
      channel: json['channel'] as String?,
      platform: json['platform'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'channel': channel,
      'platform': platform,
    };
  }
}
