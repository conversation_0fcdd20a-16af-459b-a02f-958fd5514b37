import 'package:flutter/material.dart';

class WidgetItemModel {
  final String id;
  final String label;
  final String iconUrl;
  final Color color;
  final String widgetKey;

  WidgetItemModel({
    required this.id,
    required this.label,
    required this.iconUrl,
    required this.color,
    required this.widgetKey,
  });

  // Create a copy of the model with updated values
  WidgetItemModel copyWith({
    String? id,
    String? label,
    String? iconUrl,
    Color? color,
    String? widgetKey,
  }) {
    return WidgetItemModel(
      id: id ?? this.id,
      label: label ?? this.label,
      iconUrl: iconUrl ?? this.iconUrl,
      color: color ?? this.color,
      widgetKey: widgetKey ?? this.widgetKey,
    );
  }
}
