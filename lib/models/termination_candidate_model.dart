class TerminationCandidateModel {
  TerminationCandidateModel({
    required this.agentN<PERSON>,
    required this.agentCode,
    required this.level,
    required this.leaderCode,
    required this.leaderName,
    required this.branchCode,
    required this.branchName,
    required this.branchCity,
    required this.branchAddress,
    required this.status,
    required this.agentPicture,
  });

  final String? agentName;
  final String? agentCode;
  final String? level;
  final String? leaderCode;
  final String? leaderName;
  final String? branchCode;
  final String? branchName;
  final String? branchCity;
  final String? branchAddress;
  final String? status;
  final String? agentPicture;

  factory TerminationCandidateModel.fromJson(Map<String, dynamic> json) {
    return TerminationCandidateModel(
      agentName: json["agentName"],
      agentCode: json["agentCode"],
      level: json["level"],
      leaderCode: json["leaderCode"],
      leaderName: json["leaderName"],
      branchCode: json["branchCode"],
      branchName: json["branchName"],
      branchCity: json["branchCity"],
      branchAddress: json["branchAddress"],
      status: json["status"],
      agentPicture: json["agentPicture"],
    );
  }

  Map<String, dynamic> toJson() => {
    "agentName": agentName,
    "agentCode": agentCode,
    "level": level,
    "leaderCode": leaderCode,
    "leaderName": leaderName,
    "branchCode": branchCode,
    "branchName": branchName,
    "branchCity": branchCity,
    "branchAddress": branchAddress,
    "status": status,
    "agentPicture": agentPicture,
  };
}
