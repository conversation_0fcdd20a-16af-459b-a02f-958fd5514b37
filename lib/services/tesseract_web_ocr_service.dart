import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdl_superapp/models/ktp_ocr_model.dart';
import 'package:pdl_superapp/utils/ktp_ocr_extensions.dart';
import 'package:pdl_superapp/utils/import_helper_web/import_helper.dart';

/// Web-compatible OCR service using Tesseract.js with Indonesian KTP trained data
class TesseractWebOcrService {
  static const String _genderKey = 'gender';
  static const String _religionKey = 'religion';
  static const String _maritalKey = 'marital';
  static const String _nationalityKey = 'nationality';

  /// A map of expected words for certain fields to assist in data correction.
  static final Map<String, List<String>> _expectedWords = {
    _genderKey: ['LAKI-LAKI', 'PEREMPUAN'],
    _religionKey: [
      'ISLAM',
      'KRISTEN',
      'KATOLIK',
      'HINDU',
      'BUDDHA',
      'KHONGHUCU',
    ],
    _maritalKey: ['KAWIN', 'BELUM KAWIN', 'CERAI MATI', 'CERAI'],
    _nationalityKey: ['WNI', 'WNA'],
  };

  /// Extract KTP information using Tesseract.js with Indonesian trained data
  /// Accepts XFile for web platform compatibility
  static Future<KtpOcrModel> extractKtpWithTesseract(dynamic imageFile) async {
    try {
      if (kDebugMode) {
        print(
          '🔍 Using Tesseract.js for web OCR processing with Indonesian KTP trained data',
        );
      }

      // Handle XFile input for web
      Uint8List bytes;
      if (imageFile is XFile) {
        bytes = await imageFile.readAsBytes();
      } else {
        throw Exception(
          'Web platform requires XFile, got ${imageFile.runtimeType}',
        );
      }

      // Convert to base64 for JavaScript processing
      final base64Image = base64Encode(bytes);

      // Call Tesseract.js through JavaScript interop
      final ocrResult = await _callTesseractJS(base64Image);

      if (ocrResult != null && ocrResult.isNotEmpty) {
        if (kDebugMode) {
          print('✅ Tesseract OCR completed successfully');
        }
        final ktpModel = _parseKtpText(ocrResult);

        // Log extracted fields for debugging
        if (kDebugMode) {
          print('📋 Extracted KTP Data:');
          print('  NIK: ${ktpModel.nik ?? "Not found"}');
          print('  Nama: ${ktpModel.name ?? "Not found"}');
          print('  Tempat Lahir: ${ktpModel.placeBirth ?? "Not found"}');
          print('  Tanggal Lahir: ${ktpModel.birthDay ?? "Not found"}');
          print('  Jenis Kelamin: ${ktpModel.gender ?? "Not found"}');
          print('  Status Perkawinan: ${ktpModel.marital ?? "Not found"}');
          print('  Alamat: ${ktpModel.address ?? "Not found"}');
          print(
            '  RT/RW: ${ktpModel.rt ?? "Not found"}/${ktpModel.rw ?? "Not found"}',
          );
          print('  Provinsi: ${ktpModel.province ?? "Not found"}');
          print('  Kota: ${ktpModel.city ?? "Not found"}');
        }

        return ktpModel;
      } else {
        if (kDebugMode) {
          print('⚠️ No text detected by Tesseract');
        }
        return const KtpOcrModel();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in extractKtpWithTesseract: $e');
      }
      return const KtpOcrModel();
    }
  }

  /// Call Tesseract.js through JavaScript interop with Indonesian KTP trained data
  static Future<String?> _callTesseractJS(String base64Image) async {
    try {
      if (kDebugMode) {
        print(
          '🚀 Initializing Tesseract.js with Indonesian KTP trained data...',
        );
        print(
          '📊 Processing image with enhanced OCR settings for Indonesian text...',
        );
      }

      // Call actual Tesseract.js through web helper
      final ocrResult = await performTesseractOCR(base64Image);

      if (ocrResult != null && ocrResult.isNotEmpty) {
        if (kDebugMode) {
          print('✅ Tesseract.js processing completed successfully');
          print('📝 Extracted text length: ${ocrResult.length} characters');
        }
        return ocrResult;
      } else {
        if (kDebugMode) {
          print(
            '⚠️ Tesseract.js returned empty result, using fallback mock data',
          );
        }

        // Fallback to mock data if Tesseract fails
        final mockKtpText = '''

        ''';

        if (kDebugMode) {
          print(
            '📝 Using fallback mock data, length: ${mockKtpText.length} characters',
          );
        }

        return mockKtpText;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error calling Tesseract.js: $e');
      }
      return null;
    }
  }

  /// Parse extracted text to KTP model with enhanced Indonesian text processing
  static KtpOcrModel _parseKtpText(String fullText) {
    String? nik;
    String? name;
    String? placeBirth;
    String? birthDay;
    String? gender;
    String? address;
    String? rt;
    String? rw;
    String? subDistrict;
    String? district;
    String? province;
    String? city;
    String? religion;
    String? marital;
    String? occupation;
    String? nationality;
    String? validUntil;

    if (kDebugMode) {
      print('🔍 Parsing OCR text for KTP fields...');
    }

    final lines = fullText.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      final lowerLine = line.toLowerCase();

      // Extract Province with Indonesian-specific handling
      if (lowerLine.startsWith('provinsi')) {
        province = line.cleanse('provinsi').filterNumberToAlphabet();
        province = line.cleanse('PROVINSI');
        province = line.replaceAll('-', ' ');
        if (kDebugMode) print('Found Province: $province');
      }

      // Extract City with Indonesian-specific handling
      if (lowerLine.startsWith('kota') ||
          lowerLine.startsWith('kabupaten') ||
          lowerLine.startsWith('jakarta')) {
        city = line.filterNumberToAlphabet();
        if (kDebugMode) print('Found City: $city');
      }

      // Extract NIK (16 digits) - Indonesian ID number
      if (nik == null && line.filterNumbersOnly().length == 16) {
        nik = line.filterNumbersOnly();
        if (kDebugMode) print('Found NIK: $nik');
      }

      if (nik == null && lowerLine.startsWith('nik')) {
        final cleanedLine = line.cleanse('nik');
        nik = cleanedLine.filterNumbersOnly().removeAlphabet();
        if (kDebugMode) print('Found NIK (from label): $nik');
      }

      // Extract Name
      if (lowerLine.startsWith('nama')) {
        name = line.cleanse('nama').filterNumberToAlphabet();
        if (kDebugMode) print('Found Name: $name');
      }

      // Extract Place of Birth and Date
      if (lowerLine.startsWith('tempat/tgl lahir') ||
          lowerLine.startsWith('tempat/tgi lahir') ||
          lowerLine.startsWith('tempat/tgiLahir') ||
          lowerLine.startsWith('tempaitgilahir') ||
          lowerLine.startsWith('tempat lahir')) {
        final birthInfo = line
            .cleanse('tempat/tgl lahir')
            .cleanse('tempat lahir')
            .cleanse('tempat/tgi lahir');
        final parts = birthInfo.split(',');
        if (parts.isNotEmpty) {
          placeBirth = parts[0].trim();
          if (parts.length > 1) {
            birthDay = _parseBirthDate(parts[1].trim());
          }
        }
        if (kDebugMode) print('Found Birth Info: $placeBirth, $birthDay');
      }

      // Extract Gender with Indonesian terms
      if (lowerLine.contains('jenis kelamin')) {
        final genderLine = line.cleanse('jenis kelamin');
        gender = _findBestMatch(genderLine, _expectedWords[_genderKey]!);
        if (kDebugMode) print('Found Gender: $gender');
      }

      // Extract Address
      if (lowerLine.startsWith('alamat')) {
        address = line.cleanse('alamat');
        if (kDebugMode) print('Found Address: $address');
      }

      // Extract RT/RW (Indonesian neighborhood units)
      if (lowerLine.contains('rt/rw')) {
        final rtRwLine = line.cleanse('rt/rw');
        final rtRwParts = rtRwLine.split('/');
        if (rtRwParts.length >= 2) {
          rt = rtRwParts[0].filterNumbersOnly();
          rw = rtRwParts[1].filterNumbersOnly();
        }
        if (kDebugMode) print('Found RT/RW: $rt/$rw');
      }

      // Extract Sub District
      if (lowerLine.contains('kel/desa')) {
        subDistrict = line.cleanse('kel/desa');
        if (kDebugMode) print('Found Sub District: $subDistrict');
      }

      // Extract District
      if (lowerLine.contains('kecamatan')) {
        district = line.cleanse('kecamatan');
        if (kDebugMode) print('Found District: $district');
      }

      // Extract Religion with Indonesian terms
      if (lowerLine.contains('agama')) {
        final religionLine = line.cleanse('agama');
        religion = _findBestMatch(religionLine, _expectedWords[_religionKey]!);
        if (kDebugMode) print('Found Religion: $religion');
      }

      // Extract Marital Status with Indonesian terms
      if (lowerLine.contains('status perkawinan')) {
        final maritalLine = line.cleanse('status perkawinan');
        marital = _findBestMatch(maritalLine, _expectedWords[_maritalKey]!);
        if (kDebugMode) print('Found Marital Status: $marital');
      }

      // Extract Occupation
      if (lowerLine.contains('pekerjaan')) {
        occupation = line.cleanse('pekerjaan');
        if (kDebugMode) print('Found Occupation: $occupation');
      }

      // Extract Nationality with Indonesian terms
      if (lowerLine.contains('kewarganegaraan')) {
        final nationalityLine = line.cleanse('kewarganegaraan');
        nationality = _findBestMatch(
          nationalityLine,
          _expectedWords[_nationalityKey]!,
        );
        if (kDebugMode) print('Found Nationality: $nationality');
      }

      // Extract Valid Until
      if (lowerLine.contains('berlaku hingga')) {
        validUntil = line.cleanse('berlaku hingga');
        if (kDebugMode) print('Found Valid Until: $validUntil');
      }
    }

    if (kDebugMode) {
      print('✅ KTP parsing completed');
    }

    return KtpOcrModel(
      nik: nik,
      name: name,
      placeBirth: placeBirth,
      birthDay: birthDay,
      gender: gender,
      address: address,
      rt: rt,
      rw: rw,
      subDistrict: subDistrict,
      district: district,
      province: province,
      city: city,
      religion: religion,
      marital: marital,
      occupation: occupation,
      nationality: nationality,
      validUntil: validUntil,
    );
  }

  /// Parse birth date from various Indonesian date formats
  static String? _parseBirthDate(String dateStr) {
    try {
      // Handle various date formats commonly found in Indonesian KTP
      final cleanDate = dateStr.replaceAll(RegExp(r'[^\d\-\/\s]'), '');

      // Try to extract day, month, year
      final parts = cleanDate.split(RegExp(r'[\-\/\s]+'));
      if (parts.length >= 3) {
        final day = parts[0].padLeft(2, '0');
        final month = parts[1].padLeft(2, '0');
        final year = parts[2];
        return '$day-$month-$year';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing birth date: $e');
      }
    }
    return null;
  }

  /// Find the best matching word from expected Indonesian words list
  static String? _findBestMatch(String text, List<String> expectedWords) {
    final cleanText = text.toUpperCase().trim();

    for (final word in expectedWords) {
      if (cleanText.contains(word)) {
        return word;
      }
    }

    return cleanText.isNotEmpty ? cleanText : null;
  }
}
