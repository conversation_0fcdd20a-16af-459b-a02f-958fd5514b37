import 'package:flutter/foundation.dart';

/// Configuration service for OCR-related settings
class OcrConfigService {
  /// Check if web OCR is properly configured
  static bool get isWebOcrConfigured {
    // Tesseract.js doesn't require API keys, so it's always configured
    return true;
  }

  /// Get OCR configuration status message
  static String get configurationStatus {
    if (kIsWeb) {
      return 'Web OCR configured with Tesseract.js + Indonesian KTP trained data';
    } else {
      return 'Mobile OCR configured with Google ML Kit';
    }
  }

  /// Get OCR status for UI display
  static String getOcrStatusMessage() {
    if (kIsWeb) {
      return '✅ Web OCR Ready (Tesseract.js + Indonesian KTP)';
    } else {
      return '✅ Mobile OCR Ready (Google ML Kit)';
    }
  }

  /// Check if OCR is ready to use
  static bool get isOcrReady {
    return true; // Both web and mobile OCR are always available
  }
}
