import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/logger_service.dart';

/// Service untuk menangani Firebase Cloud Messaging (FCM)
/// Mendukung Android, iOS, dan Web platform
class NotificationService extends GetxController {
  static NotificationService get instance => Get.find<NotificationService>();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Observable untuk status permission
  final RxBool _isPermissionGranted = false.obs;
  bool get isPermissionGranted => _isPermissionGranted.value;

  // FCM Token
  final RxString _fcmToken = ''.obs;
  String get fcmToken => _fcmToken.value;

  // FCM Status
  final RxBool _isFCMEnabled = true.obs;
  bool get isFCMEnabled => _isFCMEnabled.value;

  // Stream controller untuk notification events
  final StreamController<RemoteMessage> _notificationStreamController =
      StreamController<RemoteMessage>.broadcast();
  Stream<RemoteMessage> get notificationStream =>
      _notificationStreamController.stream;

  @override
  void onInit() {
    super.onInit();
    _initializeNotifications();
  }

  @override
  void onClose() {
    _notificationStreamController.close();
    super.onClose();
  }

  /// Inisialisasi service notifikasi
  Future<void> _initializeNotifications() async {
    try {
      Get.find<LoggerService>().log(
        '🚀 Starting NotificationService initialization...',
      );

      await _requestPermissions();
      await _initializeLocalNotifications();
      await _setupFirebaseMessaging();

      // Cek apakah Firebase sudah diinisialisasi
      if (Firebase.apps.isEmpty) {
        Get.find<LoggerService>().log('Firebase not initialized, skipping FCM');
        return;
      }

      // Delay sebelum mendapatkan token untuk memastikan semua service siap
      await Future.delayed(const Duration(seconds: 3));
      await _getFCMToken();

      Get.find<LoggerService>().log(
        '✅ NotificationService initialized successfully',
      );
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Failed to initialize NotificationService: $e',
      );

      // Handle specific errors
      String errorString = e.toString().toLowerCase();

      if (errorString.contains('service_not_available') ||
          errorString.contains('play services')) {
        _isFCMEnabled.value = false;
        Get.find<LoggerService>().log(
          '❌ FCM Service not available - disabling FCM functionality',
        );
        Get.find<LoggerService>().log(
          '💡 Solutions: 1) Enable FCM in Firebase Console, 2) Update Google Play Services, 3) Check internet connection',
        );
        Get.find<LoggerService>().log(
          '📱 App will continue working without push notifications',
        );
        return;
      }

      // Handle WorkSource related errors (suppress them)
      if (errorString.contains('worksource') ||
          errorString.contains('reflection') ||
          errorString.contains('hidden method')) {
        Get.find<LoggerService>().log(
          '⚠️ Android system warning (can be ignored): ${e.toString().substring(0, 100)}...',
        );
        // Continue initialization despite warning
        Get.find<LoggerService>().log(
          '✅ NotificationService initialized with warnings (functionality not affected)',
        );
        return;
      }

      // Retry initialization setelah delay untuk error lain
      Future.delayed(const Duration(seconds: 10), () {
        Get.find<LoggerService>().log(
          '🔄 Retrying NotificationService initialization...',
        );
        _initializeNotifications();
      });
    }
  }

  /// Request permission untuk notifikasi
  Future<void> _requestPermissions() async {
    try {
      // Request FCM permission
      NotificationSettings settings = await _firebaseMessaging
          .requestPermission(
            alert: true,
            announcement: false,
            badge: true,
            carPlay: false,
            criticalAlert: false,
            provisional: false,
            sound: true,
          );

      _isPermissionGranted.value =
          settings.authorizationStatus == AuthorizationStatus.authorized;

      // Request local notification permission untuk Android 13+
      if (!kIsWeb) {
        if (!Platform.isIOS) {
          await _localNotifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >()
              ?.requestNotificationsPermission();
        }
      }

      Get.find<LoggerService>().log(
        'Notification permission status: ${settings.authorizationStatus}',
      );
    } catch (e) {
      Get.find<LoggerService>().log('Error requesting permissions: $e');
    }
  }

  /// Inisialisasi local notifications
  Future<void> _initializeLocalNotifications() async {
    if (kIsWeb) return; // Local notifications tidak diperlukan untuk web

    try {
      Get.find<LoggerService>().log('Initializing local notifications...');

      // Set foreground notification presentation options
      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );
      Get.find<LoggerService>().log(
        '✅ Foreground notification presentation options set',
      );

      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      const InitializationSettings initializationSettings =
          InitializationSettings(
            android: initializationSettingsAndroid,
            iOS: initializationSettingsIOS,
          );

      bool? initialized = await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onLocalNotificationTapped,
      );

      Get.find<LoggerService>().log(
        'Local notifications initialized: $initialized',
      );

      // Create notification channel untuk Android
      await _createNotificationChannel();

      // Test local notification functionality
      await _testLocalNotificationSetup();

      Get.find<LoggerService>().log('✅ Local notifications setup complete');
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Error initializing local notifications: $e',
      );
      rethrow; // Re-throw untuk debugging
    }
  }

  /// Buat notification channel untuk Android
  Future<void> _createNotificationChannel() async {
    if (kIsWeb) return;

    try {
      Get.find<LoggerService>().log(
        '🔧 Creating Android notification channel...',
      );

      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'pdl_superapp_channel', // Channel ID
        'PDL SuperApp Notifications', // Channel name
        description: 'Notification channel for PDL SuperApp',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
        showBadge: true,
      );

      final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
          _localNotifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >();

      if (androidPlugin != null) {
        await androidPlugin.createNotificationChannel(channel);
        Get.find<LoggerService>().log('✅ Android notification channel created');

        // Verify channel was created
        final List<AndroidNotificationChannel>? channels =
            await androidPlugin.getNotificationChannels();
        Get.find<LoggerService>().log(
          '📱 Available channels: ${channels?.length ?? 0}',
        );
      } else {
        Get.find<LoggerService>().log('❌ Android plugin not available');
      }
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Error creating notification channel: $e',
      );
    }
  }

  /// Test local notification setup
  Future<void> _testLocalNotificationSetup() async {
    if (kIsWeb) return;

    try {
      Get.find<LoggerService>().log('🧪 Testing local notification setup...');

      // Check if we can get Android plugin
      final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
          _localNotifications
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >();

      if (androidPlugin != null) {
        // Check notification channels
        final channels = await androidPlugin.getNotificationChannels();
        Get.find<LoggerService>().log(
          '📱 Notification channels: ${channels?.length ?? 0}',
        );

        // Check permissions
        final bool? areNotificationsEnabled =
            await androidPlugin.areNotificationsEnabled();
        Get.find<LoggerService>().log(
          '🔔 Notifications enabled: $areNotificationsEnabled',
        );

        Get.find<LoggerService>().log('✅ Local notification test completed');
      } else {
        Get.find<LoggerService>().log(
          '❌ Cannot test - Android plugin unavailable',
        );
      }
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error testing local notifications: $e');
    }
  }

  /// Setup Firebase Messaging listeners
  Future<void> _setupFirebaseMessaging() async {
    try {
      Get.find<LoggerService>().log(
        'Setting up Firebase Messaging listeners...',
      );

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        Get.find<LoggerService>().log('🔔 Foreground message received:');
        Get.find<LoggerService>().log(
          '  - Title: ${message.notification?.title}',
        );
        Get.find<LoggerService>().log(
          '  - Body: ${message.notification?.body}',
        );
        Get.find<LoggerService>().log('  - Data: ${message.data}');
        Get.find<LoggerService>().log('  - Message ID: ${message.messageId}');

        _handleForegroundMessage(message);
      });

      // Handle background messages (when app is in background but not terminated)
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        Get.find<LoggerService>().log('🔔 Background message opened app:');
        Get.find<LoggerService>().log(
          '  - Title: ${message.notification?.title}',
        );
        Get.find<LoggerService>().log(
          '  - Body: ${message.notification?.body}',
        );
        Get.find<LoggerService>().log('  - Data: ${message.data}');

        _handleBackgroundMessage(message);
      });

      // Handle terminated messages (when app is completely closed)
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        Get.find<LoggerService>().log('🔔 Terminated message found:');
        Get.find<LoggerService>().log(
          '  - Title: ${initialMessage.notification?.title}',
        );
        Get.find<LoggerService>().log(
          '  - Body: ${initialMessage.notification?.body}',
        );
        Get.find<LoggerService>().log('  - Data: ${initialMessage.data}');

        _handleTerminatedMessage(initialMessage);
      }

      Get.find<LoggerService>().log(
        '✅ Firebase Messaging listeners setup complete',
      );
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Error setting up Firebase Messaging: $e',
      );
    }
  }

  /// Dapatkan FCM token
  Future<void> _getFCMToken() async {
    try {
      // Tambah delay untuk memastikan Firebase sudah siap
      await Future.delayed(const Duration(seconds: 2));
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        _fcmToken.value = token;
        Get.find<LoggerService>().log('FCM Token: $token');

        // Listen untuk token refresh
        _firebaseMessaging.onTokenRefresh.listen((newToken) {
          _fcmToken.value = newToken;
          Get.find<LoggerService>().log('FCM Token refreshed: $newToken');
        });
      } else {
        Get.find<LoggerService>().log(
          'FCM Token is null - retrying in 5 seconds',
        );
        // Retry setelah 5 detik
        Future.delayed(const Duration(seconds: 5), () => _getFCMToken());
      }
    } catch (e) {
      Get.find<LoggerService>().log('Error getting FCM token: $e');

      // Retry dengan exponential backoff
      Future.delayed(const Duration(seconds: 10), () {
        Get.find<LoggerService>().log('Retrying FCM token generation...');
        _getFCMToken();
      });
    }
  }

  /// Handle notifikasi saat app di foreground
  void _handleForegroundMessage(RemoteMessage message) {
    try {
      Get.find<LoggerService>().log(
        '🔔 Processing foreground message: ${message.notification?.title}',
      );

      // Track analytics event
      _trackNotificationEvent('notification_received_foreground', message);

      // Add to stream untuk listeners
      _notificationStreamController.add(message);

      // PENTING: Tampilkan local notification untuk foreground
      // Android tidak menampilkan notification otomatis saat app di foreground
      if (Platform.isAndroid && message.notification != null) {
        Get.find<LoggerService>().log(
          '📱 Showing local notification for foreground message',
        );
        _showLocalNotification(message);
      } else if (message.notification == null) {
        Get.find<LoggerService>().log(
          '⚠️ Message has no notification payload - data only message',
        );
        // Handle data-only messages jika diperlukan
        _handleDataOnlyMessage(message);
      }
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error handling foreground message: $e');
    }
  }

  /// Handle notifikasi saat app di background
  void _handleBackgroundMessage(RemoteMessage message) {
    Get.find<LoggerService>().log(
      'Background message received: ${message.notification?.title}',
    );

    // Track analytics event
    _trackNotificationEvent('notification_received_background', message);

    _notificationStreamController.add(message);
    _navigateFromNotification(message);
  }

  /// Handle notifikasi saat app terminated
  void _handleTerminatedMessage(RemoteMessage message) {
    Get.find<LoggerService>().log(
      'Terminated message received: ${message.notification?.title}',
    );

    // Track analytics event
    _trackNotificationEvent('notification_received_terminated', message);

    _notificationStreamController.add(message);

    // Delay navigation untuk memastikan app sudah fully loaded
    Future.delayed(const Duration(seconds: 2), () {
      _navigateFromNotification(message);
    });
  }

  /// Tampilkan local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    if (kIsWeb) return;

    try {
      Get.find<LoggerService>().log(
        '📱 Preparing to show local notification...',
      );

      // Generate unique notification ID to avoid conflicts
      final int notificationId = DateTime.now().millisecondsSinceEpoch
          .remainder(100000);

      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'pdl_superapp_channel',
            'PDL SuperApp Notifications',
            channelDescription: 'Notification channel for PDL SuperApp',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            showWhen: true,
            when: null, // Let system set the time
            autoCancel: true,
            ongoing: false,
            silent: false,
            enableVibration: true,
            enableLights: true,
            ledOnMs: 1000,
            ledOffMs: 500,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        interruptionLevel: InterruptionLevel.active,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Prepare payload
      final Map<String, dynamic> payloadData = {
        ...message.data,
        'messageId': message.messageId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      await _localNotifications.show(
        notificationId,
        message.notification?.title ?? 'PDL SuperApp',
        message.notification?.body ?? 'You have a new notification',
        platformChannelSpecifics,
        payload: jsonEncode(payloadData),
      );

      Get.find<LoggerService>().log(
        '✅ Local notification shown with ID: $notificationId',
      );
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error showing local notification: $e');
      // Don't rethrow to prevent breaking the notification flow
    }
  }

  /// Handle data-only messages (tanpa notification payload)
  void _handleDataOnlyMessage(RemoteMessage message) {
    try {
      Get.find<LoggerService>().log('📊 Processing data-only message');
      Get.find<LoggerService>().log('📊 Data: ${message.data}');
      // Track analytics untuk data-only messages
      _trackNotificationEvent('data_only_message_received', message);

      // Handle berdasarkan data content
      if (message.data.containsKey('silent') &&
          message.data['silent'] == 'true') {
        Get.find<LoggerService>().log(
          '🔇 Silent data message - no notification shown',
        );
        return;
      }

      // Jika ada action yang perlu dilakukan berdasarkan data
      if (message.data.containsKey('action')) {
        _handleDataAction(message.data);
      }

      // Untuk data-only messages yang perlu ditampilkan sebagai notification
      if (message.data.containsKey('show_notification') &&
          message.data['show_notification'] == 'true') {
        _showDataOnlyNotification(message);
      }
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error handling data-only message: $e');
    }
  }

  /// Handle data actions dari data-only messages
  void _handleDataAction(Map<String, dynamic> data) {
    try {
      String action = data['action'];
      Get.find<LoggerService>().log('🎯 Handling data action: $action');

      switch (action.toLowerCase()) {
        case 'refresh':
          // Trigger app refresh
          Get.find<LoggerService>().log('🔄 Triggering app refresh');
          break;
        case 'sync':
          // Trigger data sync
          Get.find<LoggerService>().log('🔄 Triggering data sync');
          break;
        case 'update_badge':
          // Update app badge
          if (data.containsKey('badge_count')) {
            Get.find<LoggerService>().log(
              '🏷️ Updating badge count: ${data['badge_count']}',
            );
          }
          break;
        default:
          Get.find<LoggerService>().log('❓ Unknown action: $action');
      }
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error handling data action: $e');
    }
  }

  /// Show notification untuk data-only messages
  Future<void> _showDataOnlyNotification(RemoteMessage message) async {
    if (kIsWeb) return;

    try {
      Get.find<LoggerService>().log(
        '📊 Showing notification for data-only message',
      );

      final int notificationId = DateTime.now().millisecondsSinceEpoch
          .remainder(100000);

      // Extract title and body from data
      String title = message.data['title'] ?? 'PDL SuperApp';
      String body = message.data['body'] ?? 'You have a new update';

      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'pdl_superapp_channel',
            'PDL SuperApp Notifications',
            channelDescription: 'Notification channel for PDL SuperApp',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            showWhen: true,
            autoCancel: true,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: jsonEncode(message.data),
      );

      Get.find<LoggerService>().log('✅ Data-only notification shown');
    } catch (e) {
      Get.find<LoggerService>().log(
        '❌ Error showing data-only notification: $e',
      );
    }
  }

  /// Handle tap pada local notification
  void _onLocalNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        Get.find<LoggerService>().log('👆 Local notification tapped');

        Map<String, dynamic> data = jsonDecode(response.payload!);
        RemoteMessage message = RemoteMessage(
          data: data,
          notification: null, // Local notification sudah ditampilkan
        );

        // Track notification tap event
        _trackNotificationEvent('notification_tapped', message);
        _navigateFromNotification(message);
      } catch (e) {
        Get.find<LoggerService>().log(
          '❌ Error parsing notification payload: $e',
        );
      }
    }
  }

  /// Navigasi berdasarkan data notifikasi
  void _navigateFromNotification(RemoteMessage message) {
    try {
      Map<String, dynamic> data = message.data;

      // Log data untuk debugging
      Get.find<LoggerService>().log('Notification data: $data');

      // Handle navigasi berdasarkan data
      // {status: DISETUJUI, trxUuid: 2fc5e4f9-fbcf-4c58-a0c7-778a94848171, approvalId: 113, trxId: 59, trxType: RECRUITMENT_BP}
      if (data.containsKey('trxType')) {
        String trxType = data['trxType'];
        switch (trxType) {
          case 'RECRUITMENT_BP':
          case 'RECRUITMENT_BM':
          case 'RECRUITMENT_BD':
            Get.toNamed(Routes.APPROVAL, arguments: {'uuid': data['trxUuId']});
            break;
          case 'EDIT_PROFILE':
            Get.toNamed(Routes.PROFILE_EDIT);
            break;
          default:
        }
      } else if (data.containsKey('route')) {
        // Direct route navigation
        String route = data['route'];
        Map<String, dynamic>? arguments;

        if (data.containsKey('arguments')) {
          arguments = Map<String, dynamic>.from(data['arguments']);
        }

        Get.toNamed(route, arguments: arguments);
      }
    } catch (e) {
      Get.find<LoggerService>().log('Error navigating from notification: $e');
    }
  }

  /// Subscribe ke topic tertentu
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      Get.find<LoggerService>().log('Subscribed to topic: $topic');
    } catch (e) {
      Get.find<LoggerService>().log('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe dari topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      Get.find<LoggerService>().log('Unsubscribed from topic: $topic');
    } catch (e) {
      Get.find<LoggerService>().log(
        'Error unsubscribing from topic $topic: $e',
      );
    }
  }

  /// Clear semua notifikasi
  Future<void> clearAllNotifications() async {
    if (!kIsWeb) {
      await _localNotifications.cancelAll();
    }
  }

  /// Manual retry untuk mendapatkan FCM token
  Future<void> retryGetToken() async {
    Get.find<LoggerService>().log('Manual retry FCM token...');
    await _getFCMToken();
  }

  /// Reinitialize notification service
  Future<void> reinitialize() async {
    Get.find<LoggerService>().log('Reinitializing NotificationService...');
    _isFCMEnabled.value = true; // Reset FCM status
    await _initializeNotifications();
  }

  /// Disable FCM dan gunakan fallback mode
  void disableFCM() {
    _isFCMEnabled.value = false;
    _fcmToken.value = '';
    Get.find<LoggerService>().log('FCM disabled - using fallback mode');
  }

  /// Enable FCM kembali
  Future<void> enableFCM() async {
    _isFCMEnabled.value = true;
    Get.find<LoggerService>().log('FCM enabled - reinitializing...');
    await _initializeNotifications();
  }

  /// Check FCM availability
  Future<bool> checkFCMAvailability() async {
    try {
      if (Firebase.apps.isEmpty) {
        Get.find<LoggerService>().log('Firebase not initialized');
        return false;
      }

      // Test basic FCM functionality
      await _firebaseMessaging.requestPermission();
      Get.find<LoggerService>().log('FCM availability check passed');
      return true;
    } catch (e) {
      Get.find<LoggerService>().log('FCM availability check failed: $e');
      return false;
    }
  }

  /// Track notification events to Firebase Analytics
  void _trackNotificationEvent(String eventName, RemoteMessage message) {
    try {
      Map<String, Object> parameters = {
        'notification_title': message.notification?.title ?? 'unknown',
        'notification_body': message.notification?.body ?? 'unknown',
        'message_id': message.messageId ?? 'unknown',
        'from': message.from ?? 'unknown',
      };

      // Add custom data parameters
      if (message.data.isNotEmpty) {
        message.data.forEach((key, value) {
          // Firebase Analytics parameter names must be alphanumeric and underscores only
          String cleanKey = key.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_');
          if (cleanKey.length <= 40) {
            // Firebase Analytics parameter name limit
            parameters['data_$cleanKey'] = value.toString();
          }
        });
      }

      _analytics.logEvent(name: eventName, parameters: parameters);

      Get.find<LoggerService>().log('Analytics event tracked: $eventName');
    } catch (e) {
      Get.find<LoggerService>().log('Failed to track analytics event: $e');
    }
  }

  /// Public method untuk testing analytics tracking
  void testAnalyticsTracking(String eventName, RemoteMessage message) {
    _trackNotificationEvent(eventName, message);
  }

  /// Test method untuk mengirim test notification
  Future<void> sendTestNotification() async {
    if (kIsWeb) return;

    try {
      Get.find<LoggerService>().log('🧪 Sending test notification...');

      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'pdl_superapp_channel',
            'PDL SuperApp Notifications',
            channelDescription: 'Notification channel for PDL SuperApp',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            showWhen: true,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        'Test Notification',
        'This is a test notification to verify local notifications work',
        platformChannelSpecifics,
        payload: jsonEncode({
          'test': 'data',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      Get.find<LoggerService>().log('✅ Test notification sent');
    } catch (e) {
      Get.find<LoggerService>().log('❌ Error sending test notification: $e');
    }
  }

  /// Debug method untuk memeriksa status notification service
  void debugNotificationStatus() {
    Get.find<LoggerService>().log('🔍 Notification Service Debug Status:');
    Get.find<LoggerService>().log(
      '  - Permission granted: $_isPermissionGranted',
    );
    Get.find<LoggerService>().log('  - FCM enabled: $_isFCMEnabled');
    Get.find<LoggerService>().log(
      '  - FCM token: ${_fcmToken.value.isNotEmpty ? "Available" : "Not available"}',
    );
    Get.find<LoggerService>().log('  - Firebase apps: ${Firebase.apps.length}');
  }
}

/// Background message handler (harus top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Inisialisasi Firebase jika belum diinisialisasi
  try {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
      log('Firebase initialized in background handler');
    }
  } catch (e) {
    log('Failed to initialize Firebase in background handler: $e');
  }

  log('🔔 Background message received: ${message.notification?.title}');
  log('📱 Background message body: ${message.notification?.body}');
  log('📊 Background message data: ${message.data}');

  // Simpan data notifikasi jika diperlukan
  // Tidak bisa melakukan navigasi di sini karena app tidak aktif
}
