import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_object_detection/google_mlkit_object_detection.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:pdl_superapp/models/ktp_ocr_model.dart';
import 'package:pdl_superapp/services/tesseract_web_ocr_service.dart';
import 'package:pdl_superapp/utils/ktp_ocr_extensions.dart';
import 'package:pdl_superapp/utils/ktp_ocr_utils.dart';
import 'package:pdl_superapp/utils/image_quality_checker.dart';

/// A utility class for extracting information from KTP (Indonesian ID card) images.
class KtpOcrService {
  static const String _genderKey = 'gender';
  static const String _religionKey = 'religion';
  static const String _maritalKey = 'marital';
  static const String _nationalityKey = 'nationality';

  /// A map of expected words for certain fields to assist in data correction.
  static final Map<String, List<String>> _expectedWords = {
    _genderKey: ['LAKI-LAKI', 'PEREMPUAN'],
    _religionKey: [
      'ISLAM',
      'KRISTEN',
      'KATOLIK',
      'HINDU',
      'BUDDHA',
      'KHONGHUCU',
    ],
    _maritalKey: ['KAWIN', 'BELUM KAWIN', 'CERAI MATI', 'CERAI'],
    _nationalityKey: ['WNI', 'WNA'],
  };

  /// Crops the KTP area from the provided image using object detection.
  ///
  /// This method uses a custom TensorFlow Lite model to detect and crop the KTP
  /// (Indonesian ID card) area from the given image. It returns a [File] containing
  /// the cropped image or `null` if no KTP is detected.
  ///
  /// [imageFile]: The image file containing the KTP (File for mobile, XFile for web).
  static Future<dynamic> cropImageForKtp(dynamic imageFile) async {
    try {
      // For web platform, object detection is not supported
      // Return the original image for text recognition
      if (kIsWeb) {
        if (kDebugMode) {
          print(
            'Web platform detected: Skipping object detection, using original image',
          );
        }
        return imageFile;
      }

      // Ensure we have a File object for mobile object detection
      if (!_isValidFileForMobile(imageFile)) {
        throw Exception(
          'Mobile platform requires File object for object detection, got ${imageFile.runtimeType}',
        );
      }

      final modelPath = await getAssetPath(
        'assets/custom_models/object_labeler.tflite',
      );
      final options = LocalObjectDetectorOptions(
        mode: DetectionMode.single,
        modelPath: modelPath,
        classifyObjects: true,
        multipleObjects: true,
      );

      final ObjectDetector detector = ObjectDetector(options: options);
      final InputImage inputImage = InputImage.fromFile(imageFile);
      final result = await detector.processImage(inputImage);

      File? imageCropped;

      // Iterate over detected objects to find the KTP.
      for (final object in result) {
        if (kDebugMode) {
          print('Object found: ${object.labels.map((e) => e.text)}');
        }
        if (object.labels.firstOrNull?.text == "Driver's license") {
          // Crop the image to the detected KTP area.
          imageCropped = await cropImage(File(inputImage.filePath!), object);
          break;
        }
      }

      await detector.close();
      return imageCropped;
    } catch (e) {
      if (kDebugMode) {
        print('Error in cropImageForKtp: $e');
      }
      return null;
    }
  }

  /// Check image quality before OCR processing
  /// Returns [ImageQualityResult] with quality assessment
  static Future<ImageQualityResult> checkImageQuality(dynamic imageFile) async {
    return await ImageQualityChecker.checkImageQuality(imageFile);
  }

  /// Extracts KTP information from the provided image file.
  ///
  /// This method performs text recognition on the image to extract
  /// various fields from the KTP, such as NIK, name, birth date, etc.
  /// Uses Tesseract.js with Indonesian trained data for web platform and ML Kit for mobile.
  ///
  /// [imageFile]: The image file of the KTP (File for mobile, XFile for web).
  /// [skipQualityCheck]: Skip image quality validation (default: false).
  static Future<KtpOcrModel> extractKtp(
    dynamic imageFile, {
    bool skipQualityCheck = false,
  }) async {
    try {
      // Check image quality before processing (unless skipped)
      if (!skipQualityCheck) {
        final qualityResult = await checkImageQuality(imageFile);
        if (!qualityResult.isGoodQuality) {
          if (kDebugMode) {
            print(
              '⚠️ Image quality check failed: ${qualityResult.qualityMessage}',
            );
            print('Suggestions: ${qualityResult.improvementSuggestion}');
          }
          // Return empty model with quality issues
          return KtpOcrModel(qualityIssues: qualityResult.issues);
        }

        if (kDebugMode) {
          print('✅ Image quality check passed');
        }
      }

      // Use Tesseract.js for web platform with Indonesian KTP trained data
      if (kIsWeb) {
        if (kDebugMode) {
          print(
            '🌐 Using Tesseract.js web OCR service with Indonesian KTP trained data',
          );
        }
        return await TesseractWebOcrService.extractKtpWithTesseract(imageFile);
      }

      // Use ML Kit for mobile platforms
      // Check if we have a valid file object for mobile
      if (!_isValidFileForMobile(imageFile)) {
        throw Exception(
          'Mobile platform requires File object, got ${imageFile.runtimeType}',
        );
      }

      final TextRecognizer recognizer = TextRecognizer();
      final InputImage inputImage = InputImage.fromFile(imageFile);
      final RecognizedText recognizedText = await recognizer.processImage(
        inputImage,
      );

      await recognizer.close();

      return extractFromOcr(recognizedText);
    } catch (e) {
      if (kDebugMode) {
        print('Error in extractKtp: $e');
      }
      return const KtpOcrModel();
    }
  }

  /// Extracts KTP information from recognized text.
  ///
  /// This method parses the recognized text from OCR to extract
  /// KTP fields and returns a [KtpOcrModel] containing the extracted data.
  ///
  /// [recognizedText]: The recognized text from OCR.
  static KtpOcrModel extractFromOcr(RecognizedText recognizedText) {
    String? nik;
    String? name;
    String? placeBirth;
    String? birthDay;
    String? gender;
    String? address;
    String? rt;
    String? rw;
    String? subDistrict;
    String? district;
    String? province;
    String? city;
    String? religion;
    String? marital;
    String? occupation;
    String? nationality;
    String? validUntil;

    if (kDebugMode) {
      print('Result text: ${recognizedText.text}');
      print('========================================');
      print('========= MLKIT TEXT RECOGNITION =======');
      print('Total blocks detected: ${recognizedText.blocks.length}');

      // Calculate OCR statistics as alternative to confidence levels
      int totalLines = 0;
      int totalElements = 0;
      int totalSymbols = 0;
      int totalCharacters = recognizedText.text.length;

      for (final block in recognizedText.blocks) {
        totalLines += block.lines.length;
        for (final line in block.lines) {
          totalElements += line.elements.length;
          for (final element in line.elements) {
            totalSymbols += element.symbols.length;
          }
        }
      }

      print('OCR Statistics:');
      print('  Total characters: $totalCharacters');
      print('  Total lines: $totalLines');
      print('  Total elements: $totalElements');
      print('  Total symbols: $totalSymbols');
      print(
        '  Avg elements per line: ${totalLines > 0 ? (totalElements / totalLines).toStringAsFixed(2) : 'N/A'}',
      );
      print(
        '  Avg symbols per element: ${totalElements > 0 ? (totalSymbols / totalElements).toStringAsFixed(2) : 'N/A'}',
      );
      print(
        '  Text density score: ${totalCharacters > 0 && totalSymbols > 0 ? (totalCharacters / totalSymbols).toStringAsFixed(2) : 'N/A'}',
      );
    }

    // Iterate over text blocks and lines to extract information.
    for (
      int blockIndex = 0;
      blockIndex < recognizedText.blocks.length;
      blockIndex++
    ) {
      final block = recognizedText.blocks[blockIndex];

      if (kDebugMode) {
        print('Block $blockIndex: ${block.lines.length} lines');
        print('  Block bounding box: ${block.boundingBox}');
        print('  Block recognized languages: ${block.recognizedLanguages}');

        // Try to get confidence from recognized languages if available
        if (block.recognizedLanguages.isNotEmpty) {
          for (
            int langIndex = 0;
            langIndex < block.recognizedLanguages.length;
            langIndex++
          ) {
            final lang = block.recognizedLanguages[langIndex];
            print('    Language $langIndex: $lang');
            // Note: MLKIT v2 removed confidence property from text recognition
            // Only language detection and some other APIs still provide confidence
          }
        }
      }

      for (int lineIndex = 0; lineIndex < block.lines.length; lineIndex++) {
        final line = block.lines[lineIndex];
        final String text = line.text;

        if (kDebugMode) {
          print('  Line $lineIndex: "$text"');
          print('    Line bounding box: ${line.boundingBox}');
          print('    Line angle: ${line.angle?.toStringAsFixed(2) ?? 'N/A'}°');
          print('    Line recognized languages: ${line.recognizedLanguages}');

          // Calculate line quality metrics as confidence alternative
          final lineWidth = line.boundingBox.width;
          final lineHeight = line.boundingBox.height;
          final lineArea = lineWidth * lineHeight;
          final textLength = text.length;
          final textDensity = textLength > 0 ? lineArea / textLength : 0;

          print('    Line quality metrics:');
          print('      Width: ${lineWidth.toStringAsFixed(1)}px');
          print('      Height: ${lineHeight.toStringAsFixed(1)}px');
          print('      Area: ${lineArea.toStringAsFixed(1)}px²');
          print('      Text length: $textLength chars');
          print(
            '      Text density: ${textDensity.toStringAsFixed(2)} px²/char',
          );
          print('      Elements count: ${line.elements.length}');

          // Log each text element in the line with detailed information
          for (
            int elementIndex = 0;
            elementIndex < line.elements.length;
            elementIndex++
          ) {
            final element = line.elements[elementIndex];
            print('      Element $elementIndex: "${element.text}"');
            print('        Element bounding box: ${element.boundingBox}');
            print(
              '        Element angle: ${element.angle?.toStringAsFixed(2) ?? 'N/A'}°',
            );

            // Calculate element quality metrics
            final elementWidth = element.boundingBox.width;
            final elementHeight = element.boundingBox.height;
            final elementArea = elementWidth * elementHeight;
            final elementTextLength = element.text.length;

            print('        Element quality:');
            print(
              '          Size: ${elementWidth.toStringAsFixed(1)}x${elementHeight.toStringAsFixed(1)}px',
            );
            print('          Area: ${elementArea.toStringAsFixed(1)}px²');
            print('          Symbols: ${element.symbols.length}');
            print(
              '          Char/symbol ratio: ${element.symbols.isNotEmpty ? (elementTextLength / element.symbols.length).toStringAsFixed(2) : 'N/A'}',
            );

            // Log symbols within each element (limited to first 3 for brevity)
            final maxSymbolsToShow = 3;
            for (
              int symbolIndex = 0;
              symbolIndex < element.symbols.length &&
                  symbolIndex < maxSymbolsToShow;
              symbolIndex++
            ) {
              final symbol = element.symbols[symbolIndex];
              print('          Symbol $symbolIndex: "${symbol.text}"');
              print('            Symbol bounding box: ${symbol.boundingBox}');
              print(
                '            Symbol angle: ${symbol.angle?.toStringAsFixed(2) ?? 'N/A'}°',
              );
            }

            if (element.symbols.length > maxSymbolsToShow) {
              print(
                '          ... and ${element.symbols.length - maxSymbolsToShow} more symbols',
              );
            }
          }
        }

        // Extract Province.
        if (text.toLowerCase().startsWith('provinsi')) {
          final lineText = text.cleanse('provinsi').filterNumberToAlphabet();
          province = lineText;
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract City.
        if (text.toLowerCase().startsWith('kota') ||
            text.toLowerCase().startsWith('kabupaten') ||
            text.toLowerCase().startsWith('jakarta')) {
          final lineText = text.filterNumberToAlphabet();
          city = lineText;
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract NIK (Identity Number).
        if (nik == null && text.filterNumbersOnly().length == 16) {
          nik = text.filterNumbersOnly();
          if (kDebugMode) {
            print('NIK Found: ${line.text}');
            print('NIK Filtered: $nik');
          }
        }

        if (nik == null && text.toLowerCase().startsWith('nik')) {
          final lineText = recognizedText.findAndClean(line, 'NIK');
          nik = lineText?.filterNumbersOnly().removeAlphabet();
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
            print('Line Text Filtered: $nik');
          }
        }

        // Extract Name.
        if (text.toLowerCase().startsWith('nama')) {
          final lineText =
              recognizedText
                  .findAndClean(line, 'nama')
                  ?.filterNumberToAlphabet();
          name = lineText;
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Place and Date of Birth.
        if (text.toLowerCase().contains(RegExp('tempat')) &&
            text.toLowerCase().contains(RegExp('lahir'))) {
          if (kDebugMode) {
            print('Text: $text');
          }
          String? lineText = recognizedText.findAndClean(
            line,
            'tempat/tgl lahir',
          );
          if (lineText != null) {
            lineText = lineText.cleanse('tempat');
            lineText = lineText.cleanse('tgl lahir');
            if (lineText.split('/').isNotEmpty) {
              lineText = lineText.replaceAll('/', '');
            }
          }
          final List<String> splitBirth = lineText?.split(',') ?? [];
          if (kDebugMode) {
            print('Split Place of Birth: $splitBirth');
          }
          if (splitBirth.isNotEmpty) {
            placeBirth = splitBirth[0].filterNumberToAlphabet();
            if (splitBirth.length > 1) {
              birthDay = splitBirth[1].filterAlphabetToNumber();
            }
          }
          if (kDebugMode) {
            print('Line Text: $lineText');
          }
        }

        // Extract Gender.
        if (text.toLowerCase().startsWith('jenis kelamin')) {
          final lineText = recognizedText
              .findAndClean(line, 'jenis kelamin')
              ?.filterNumberToAlphabet()
              .correctWord(_expectedWords[_genderKey]!);
          gender = lineText;
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Address.
        if (text.toLowerCase().startsWith('alamat')) {
          final lineText = recognizedText.findAndClean(line, 'alamat');
          address = lineText;
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract RT/RW (Neighborhood and Hamlet numbers).
        if (text.toLowerCase().contains(RegExp('rt')) &&
            text.toLowerCase().contains(RegExp('rw'))) {
          if (kDebugMode) {
            print('Text: $text');
          }
          String? lineText = recognizedText.findAndClean(line, 'RTRW');
          if (lineText != null) {
            lineText = lineText.cleanse('rt');
            lineText = lineText.cleanse('rw');
            if (lineText.split('/').length == 2) {
              lineText = lineText.replaceFirst('/', '');
            }
          }
          final List<String> splitRtRw =
              lineText?.filterAlphabetToNumber().removeAlphabet().split('/') ??
              [];
          if (kDebugMode) {
            print('Split RT/RW: $splitRtRw');
          }
          if (splitRtRw.isNotEmpty) {
            rt = splitRtRw[0];
            if (splitRtRw.length > 1) {
              rw = splitRtRw[1];
            } else {
              if (rt.length > 3) {
                rw = rt.substring(3);
                rt = rt.substring(0, 3);
              }
            }
          }
          if (kDebugMode) {
            print('Line Text: $lineText');
          }
        }

        // Extract Sub-District.
        if (text.toLowerCase().contains(RegExp('desa'))) {
          final lineText = recognizedText.findAndClean(line, 'kel/desa');
          subDistrict = lineText?.filterNumberToAlphabet();
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract District.
        if (text.toLowerCase().startsWith('kecamatan')) {
          final lineText = recognizedText.findAndClean(line, 'kecamatan');
          district = lineText?.filterNumberToAlphabet();
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Religion.
        if (text.toLowerCase().startsWith('agama')) {
          final lineText = recognizedText.findAndClean(line, 'agama');
          religion = lineText?.filterNumberToAlphabet().correctWord(
            _expectedWords[_religionKey]!,
          );
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Marital Status.
        if (text.toLowerCase().startsWith('status perkawinan')) {
          final lineText = recognizedText.findAndClean(
            line,
            'status perkawinan',
          );
          marital = lineText?.filterNumberToAlphabet().correctWord(
            _expectedWords[_maritalKey]!,
          );
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Occupation.
        if (text.toLowerCase().startsWith('pekerjaan')) {
          final lineText = recognizedText.findAndClean(line, 'pekerjaan');
          occupation = lineText?.filterNumberToAlphabet();
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Nationality.
        if (text.toLowerCase().startsWith('kewarganegaraan')) {
          final lineText = recognizedText.findAndClean(line, 'kewarganegaraan');
          nationality = lineText?.filterNumberToAlphabet().correctWord(
            _expectedWords[_nationalityKey]!,
          );
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }

        // Extract Valid Until date.
        if (text.toLowerCase().startsWith('berlaku hingga')) {
          final lineText = recognizedText.findAndClean(line, 'berlaku hingga');
          validUntil = lineText?.filterNumberToAlphabet();
          if (kDebugMode) {
            print('Text: $text');
            print('Line Text: $lineText');
          }
        }
      }
    }

    if (kDebugMode) {
      print('========================================');
      print('========= OCR QUALITY SUMMARY ==========');

      // Calculate extraction success rate as confidence alternative
      final List<String?> extractedFields = [
        nik,
        name,
        birthDay,
        placeBirth,
        gender,
        address,
        rt,
        rw,
        subDistrict,
        district,
        province,
        city,
        religion,
        marital,
        occupation,
        nationality,
        validUntil,
      ];

      final int totalFields = extractedFields.length;
      final int extractedFieldsCount =
          extractedFields
              .where((field) => field != null && field.isNotEmpty)
              .length;
      final double extractionSuccessRate =
          (extractedFieldsCount / totalFields) * 100;

      print(
        'Extraction Success Rate: ${extractionSuccessRate.toStringAsFixed(1)}% ($extractedFieldsCount/$totalFields fields)',
      );
      print('Empty/Missing fields: ${totalFields - extractedFieldsCount}');

      // List missing fields
      final List<String> fieldNames = [
        'NIK',
        'Name',
        'Birth Day',
        'Place of Birth',
        'Gender',
        'Address',
        'RT',
        'RW',
        'Sub-District',
        'District',
        'Province',
        'City',
        'Religion',
        'Marital Status',
        'Occupation',
        'Nationality',
        'Valid Until',
      ];

      final List<String> missingFields = [];
      for (int i = 0; i < extractedFields.length; i++) {
        if (extractedFields[i] == null || extractedFields[i]!.isEmpty) {
          missingFields.add(fieldNames[i]);
        }
      }

      if (missingFields.isNotEmpty) {
        print('Missing fields: ${missingFields.join(', ')}');
      }

      print('========================================');
      print('=============== RESULT =================');
      print('NIK: $nik');
      print('Name: $name');
      print('Birth Day: $birthDay');
      print('Place of Birth: $placeBirth');
      print('Gender: $gender');
      print('Address: $address');
      print('RT/RW: $rt / $rw');
      print('Sub-District: $subDistrict');
      print('District: $district');
      print('Province: $province');
      print('City: $city');
      print('Religion: $religion');
      print('Marital Status: $marital');
      print('Occupation: $occupation');
      print('Nationality: $nationality');
      print('Valid Until: $validUntil');
      print('============= END RESULT ===============');
      print('========================================');
    }

    // Return a KtpOcrModel containing the extracted information.
    return KtpOcrModel(
      address: address,
      district: district,
      gender: gender,
      marital: marital,
      name: name,
      birthDay: birthDay,
      placeBirth: placeBirth,
      nationality: nationality,
      nik: nik,
      occupation: occupation,
      religion: religion,
      rt: rt,
      rw: rw,
      subDistrict: subDistrict,
      province: province,
      city: city,
      validUntil: validUntil,
    );
  }

  /// Helper method to check if the file is valid for mobile platforms
  /// Avoids direct File type checking to prevent web compilation issues
  static bool _isValidFileForMobile(dynamic imageFile) {
    if (kIsWeb) {
      return true; // On web, we don't need File objects
    }

    // Check if it has the properties/methods we expect from a File
    try {
      return imageFile?.path != null && imageFile?.readAsBytes != null;
    } catch (e) {
      return false;
    }
  }
}
