import 'package:pdl_superapp/utils/analytics_utils.dart';

/// Mixin for easy analytics integration in controllers and pages
/// Provides convenient methods for tracking common user interactions
mixin AnalyticsMixin {
  
  /// Track page view when page is opened
  Future<void> trackPageView({
    String? pageName,
    String? pageClass,
    Map<String, dynamic>? parameters,
  }) async {
    await AnalyticsUtils.trackPageView(
      pageName: pageName,
      pageClass: pageClass,
      parameters: parameters,
    );
  }
  
  /// Track button clicks with context
  Future<void> trackButtonClick({
    required String buttonName,
    String? buttonType,
    String? section,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackButtonClick(
      buttonName: buttonName,
      buttonType: buttonType,
      section: section,
      additionalData: additionalData,
    );
  }
  
  /// Track navigation between pages
  Future<void> trackNavigation({
    required String from,
    required String to,
    String? method,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackNavigation(
      from: from,
      to: to,
      method: method,
      additionalData: additionalData,
    );
  }
  
  /// Track form interactions
  Future<void> trackFormEvent({
    required String formName,
    required String action,
    String? fieldName,
    String? errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackFormEvent(
      formName: formName,
      action: action,
      fieldName: fieldName,
      errorMessage: errorMessage,
      additionalData: additionalData,
    );
  }
  
  /// Track search events
  Future<void> trackSearch({
    required String searchTerm,
    String? searchCategory,
    int? resultCount,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackSearch(
      searchTerm: searchTerm,
      searchCategory: searchCategory,
      resultCount: resultCount,
      additionalData: additionalData,
    );
  }
  
  /// Track API calls
  Future<void> trackApiCall({
    required String endpoint,
    required String method,
    required int statusCode,
    int? duration,
    String? errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackApiCall(
      endpoint: endpoint,
      method: method,
      statusCode: statusCode,
      duration: duration,
      errorMessage: errorMessage,
      additionalData: additionalData,
    );
  }
  
  /// Track user engagement
  Future<void> trackEngagement({
    required String engagementType,
    String? feature,
    int? value,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackEngagement(
      engagementType: engagementType,
      feature: feature,
      value: value,
      additionalData: additionalData,
    );
  }
  
  /// Track errors
  Future<void> trackError({
    required String errorType,
    required String errorMessage,
    String? stackTrace,
    bool fatal = false,
    Map<String, dynamic>? additionalData,
  }) async {
    await AnalyticsUtils.trackError(
      errorType: errorType,
      errorMessage: errorMessage,
      stackTrace: stackTrace,
      fatal: fatal,
      additionalData: additionalData,
    );
  }
  
  /// Track custom events
  Future<void> trackEvent({
    required String eventName,
    String? category,
    String? action,
    String? label,
    int? value,
    Map<String, dynamic>? parameters,
  }) async {
    await AnalyticsUtils.trackEvent(
      eventName: eventName,
      category: category,
      action: action,
      label: label,
      value: value,
      parameters: parameters,
    );
  }
}
