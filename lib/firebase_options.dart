// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCddT0WPwnGMKrIr7FfxToJkMd4VUvLidk',
    appId: '1:646424487625:web:6b6999c02c7c229b46cf18',
    messagingSenderId: '646424487625',
    projectId: 'pdl-superapps-uat',
    authDomain: 'pdl-superapps-uat.firebaseapp.com',
    storageBucket: 'pdl-superapps-uat.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCTQQkDh65tl3QM0NKrZENdft_o0WBl_L8',
    appId: '1:646424487625:android:e07909ab0e70efa546cf18',
    messagingSenderId: '646424487625',
    projectId: 'pdl-superapps-uat',
    storageBucket: 'pdl-superapps-uat.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCV8UvXtmIO0QZJ8aHxiH_jKhRFESxazEQ',
    appId: '1:646424487625:ios:3e72ec75ec39b7bf46cf18',
    messagingSenderId: '646424487625',
    projectId: 'pdl-superapps-uat',
    storageBucket: 'pdl-superapps-uat.firebasestorage.app',
    iosBundleId: 'id.co.panindaiichilife.quantumx360',
  );
}
