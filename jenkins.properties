# ECS Cluster name for deployment
ECS_CLUSTER=superapp-ecs
# ECS Task Definition name (container specs, resource limits)
TASK_CPU=2048
TASK_MEMORY=4096
# ========================
# 🔒 Security & Compliance
# ========================
# Comma-separated list of security scans to run (e.g., sonar for code, trivy for image)
REQUIRED_SECURITY_SCAN=sonar,trivy
# Scans that must pass before deployment (optional, currently empty)
MANDATORY_SECURITY_SCAN=
# ========================
# 🔐 Vault Configuration
# ========================
# Path where secrets will be injected or loaded
VAULT_ENV_PATH=.env
# ========================
# Docker Build Configuration
# ========================
# Build platform for Dockerfile
BUILD_PLATFORM=flutter
