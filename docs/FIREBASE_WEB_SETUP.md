# Firebase Analytics Web Setup Guide

This guide explains how to set up Firebase Analytics for Flutter Web in your project.

## 🔧 Setup Steps

### 1. Firebase Project Configuration

1. **Go to Firebase Console**: https://console.firebase.google.com/
2. **Select your project** or create a new one
3. **Add a Web App**:
   - Click "Add app" → Web (</>) icon
   - Register your app with a nickname
   - Copy the configuration object

### 2. Update Web Configuration

#### A. Update `web/firebase-config.js`

Replace the placeholder values in `web/firebase-config.js` with your actual Firebase configuration:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id",
  measurementId: "G-your-measurement-id" // For Analytics
};
```

#### B. Verify `web/index.html`

Make sure your `web/index.html` includes:

```html
<!-- Firebase SDK -->
<script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-messaging-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-analytics-compat.js"></script>

<!-- Firebase Configuration -->
<script src="firebase-config.js"></script>
```

### 3. Enable Analytics in Firebase Console

1. **Go to Analytics** in Firebase Console
2. **Enable Google Analytics** for your project
3. **Create or link** a Google Analytics property
4. **Note the Measurement ID** (starts with G-)

### 4. Test the Setup

#### A. Run in Debug Mode

```bash
flutter run -d chrome --debug
```

#### B. Check Browser Console

Open Developer Tools (F12) and look for:
```
Firebase Analytics initialized for web
Analytics: Basic initialization completed for web
```

#### C. Verify in Firebase Console

1. Go to **Firebase Console > Analytics > DebugView**
2. You should see your device/session appear
3. Navigate through your app to see events in real-time

## 🐛 Debug Mode

### Enable Debug Mode

Add `?debug_mode=1` to your URL:
```
http://localhost:12345/?debug_mode=1
```

Or run with debug flag:
```bash
flutter run -d chrome --debug
```

### Debug View in Firebase

1. **Open Firebase Console**
2. **Go to Analytics > DebugView**
3. **Select your app**
4. **See real-time events**

### Expected Events

You should see these events in DebugView:

- `debug_view_enabled` - When debug mode is activated
- `screen_view` - When pages are viewed
- `button_click` - When buttons are clicked
- `navigation` - When navigating between pages

## 📊 Analytics Features

### Automatic Tracking

The app automatically tracks:
- **Page views** with route-based names
- **User properties** after login
- **Button clicks** from controllers
- **Navigation events**
- **Platform information** (web/mobile)

### Manual Events

You can manually track events:

```dart
// Track custom event
await AnalyticsUtils.trackEvent(
  eventName: 'custom_action',
  category: 'user_interaction',
  parameters: {
    'action_type': 'button_click',
    'feature': 'export_data',
  },
);

// Track page view
await AnalyticsUtils.trackPageView(
  pageClass: 'CustomPage',
  parameters: {
    'page_type': 'feature',
    'user_level': 'premium',
  },
);
```

## 🔍 Troubleshooting

### Common Issues

#### 1. "Firebase not defined" Error

**Problem**: Firebase SDK not loaded
**Solution**: Check that firebase-config.js is loaded after Firebase SDK scripts

#### 2. No Events in DebugView

**Problem**: Debug mode not enabled or wrong measurement ID
**Solution**: 
- Add `?debug_mode=1` to URL
- Verify measurementId in firebase-config.js
- Check browser console for errors

#### 3. Analytics Not Working

**Problem**: Analytics not initialized
**Solution**:
- Verify Firebase configuration
- Check that Analytics is enabled in Firebase Console
- Ensure measurementId is correct

### Debug Checklist

- [ ] Firebase SDK scripts loaded in index.html
- [ ] firebase-config.js has correct configuration
- [ ] measurementId is set and correct
- [ ] Analytics enabled in Firebase Console
- [ ] Debug mode enabled (?debug_mode=1)
- [ ] Browser console shows no errors
- [ ] DebugView shows your session

## 🌐 Web-Specific Features

### URL-Based Analytics

The web version automatically includes:
- **Current URL** in event parameters
- **Route-based page names**
- **Browser information**
- **Debug mode detection**

### Real-Time Debug

Web analytics provides:
- **Instant event tracking** in browser console
- **Real-time DebugView** in Firebase Console
- **URL parameter debugging** (?debug_mode=1)
- **Browser-specific information**

## 📱 Platform Detection

The analytics automatically detects platform:

```dart
// Check if running on web
if (AnalyticsUtils.isWeb) {
  // Web-specific logic
}

// Get platform info
final platformInfo = AnalyticsUtils.getPlatformInfo();
// Returns: {'platform': 'web', 'is_debug': 'true', 'url': '...'}
```

## 🚀 Production Deployment

### Before Deploying

1. **Remove debug parameters** from URLs
2. **Verify production Firebase config**
3. **Test analytics in production environment**
4. **Check Firebase Console for live data**

### Security Notes

- **Never commit** real Firebase config to public repos
- **Use environment variables** for sensitive data
- **Restrict API keys** to specific domains in Firebase Console

## 📈 Analytics Reports

After setup, you can view:
- **Real-time users** in Firebase Console
- **Page views and events** in Analytics reports
- **User behavior flows**
- **Custom event data**

## 🔗 Useful Links

- [Firebase Web Setup](https://firebase.google.com/docs/web/setup)
- [Firebase Analytics Web](https://firebase.google.com/docs/analytics/get-started?platform=web)
- [FlutterFire Web Installation](https://firebase.flutter.dev/docs/manual-installation/web/)
- [Firebase Console](https://console.firebase.google.com/)
