# Implementasi Halaman Public (Tanpa Authentication)

## Overview

Implementasi ini memungkinkan aplikasi memiliki halaman yang dapat diakses tanpa token authentication, cocok untuk aplikasi web-based yang memerlukan halaman public seperti formulir rekrutmen, landing page, dll.

## Struktur Implementasi

### 1. Middleware System

#### `AuthMiddleware`
- **Fungsi**: Memastikan user sudah login sebelum mengakses halaman
- **Redirect**: Ke `/login` jika belum login
- **File**: `lib/middlewares/auth_middleware.dart`

#### `PublicMiddleware`
- **Fungsi**: Mengizinkan akses tanpa authentication
- **Redirect**: Tidak ada redirect
- **File**: `lib/middlewares/auth_middleware.dart`

#### `LoginGuardMiddleware`
- **Fungsi**: Redirect ke home jika sudah login (untuk halaman login)
- **Redirect**: Ke `/main` jika sudah login
- **File**: `lib/middlewares/auth_middleware.dart`

### 2. Page Wrapper System

#### `PageWrapper` (Existing)
- Untuk halaman yang memerlukan authentication
- Mengasumsikan user sudah login

#### `PublicPageWrapper` (New)
- Untuk halaman public yang tidak memerlukan authentication
- Tidak mengasumsikan user sudah login
- Support untuk AppBar optional
- File: `lib/pages/widget/public_page_wrapper.dart`

### 3. API System

#### `Api` (Existing)
- Mengirimkan Authorization header dengan token
- Untuk endpoint yang memerlukan authentication

#### `PublicApi` (New)
- Tidak mengirimkan Authorization header
- Untuk endpoint public yang tidak memerlukan authentication
- File: `lib/utils/public_api.dart`

## Penggunaan

### 1. Membuat Halaman Public

```dart
// Contoh halaman public
class MyPublicPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return PublicPageWrapper(
      showAppBar: true,
      title: 'Halaman Public',
      onBack: () => Get.back(),
      child: YourPageContent(),
    );
  }
}
```

### 2. Menambahkan Route Public

```dart
// Di app_routes.dart
static const MY_PUBLIC_PAGE = '/public/my-page';

// Di AppPages.routes
GetPage(
  name: Routes.MY_PUBLIC_PAGE,
  page: () => MyPublicPage(),
  transition: Transition.noTransition,
  middlewares: [PublicMiddleware()],
),
```

### 3. Menggunakan Public API

```dart
class MyPublicController extends BaseControllers {
  final PublicApi publicApi = Get.put(PublicApi());

  Future<void> submitPublicData() async {
    await publicApi.publicApiPost(
      url: '/public/my-endpoint',
      controller: this,
      data: myData,
      code: kReqSubmitData,
    );
  }
}
```

## Contoh Implementasi

### 1. Public Recruitment Form

**Route**: `/public/form-keagenan`
**File**: `lib/pages/menu/keagenan/recruitment/public_recruitment_form_page.dart`
**Controller**: `lib/controllers/menu/keagenan/recruitment/public_recruitment_form_controller.dart`

Fitur:
- Formulir rekrutmen yang dapat diakses tanpa login
- Menggunakan PublicApi untuk submit data
- Reuse existing form components

### 2. Public Landing Page

**Route**: `/public`
**File**: `lib/pages/public/public_landing_page.dart`

Fitur:
- Halaman landing untuk user yang belum login
- Link ke formulir rekrutmen public
- Link ke halaman login

## Konfigurasi Middleware

### Halaman yang Memerlukan Authentication
```dart
GetPage(
  name: Routes.SOME_PROTECTED_PAGE,
  page: () => PageWrapper(child: SomeProtectedPage()),
  middlewares: [AuthMiddleware()],
),
```

### Halaman Public
```dart
GetPage(
  name: Routes.SOME_PUBLIC_PAGE,
  page: () => SomePublicPage(),
  middlewares: [PublicMiddleware()],
),
```

### Halaman Login (Redirect jika sudah login)
```dart
GetPage(
  name: Routes.LOGIN,
  page: () => PageWrapper(child: LoginPage()),
  middlewares: [LoginGuardMiddleware()],
),
```

## Testing

### Test Akses Public
1. Buka aplikasi tanpa login
2. Navigate ke `/public` - harus bisa diakses
3. Navigate ke `/public/form-keagenan` - harus bisa diakses
4. Navigate ke `/main` - harus redirect ke login

### Test Akses Authenticated
1. Login ke aplikasi
2. Navigate ke `/login` - harus redirect ke main
3. Navigate ke `/main` - harus bisa diakses
4. Navigate ke `/public` - harus bisa diakses (public tetap accessible)

### Test Direct URL Access
1. **Buka `/url-test`** untuk halaman testing URL
2. **Copy URL** dari browser address bar
3. **Buka tab/window baru** dan paste URL
4. **Test URLs berikut:**

#### Public URLs (Selalu Accessible)
- `/` - Root URL → redirect ke public landing atau main
- `/public` - Public landing page
- `/public/form-keagenan` - Public recruitment form
- `/test-access` - Test access control page
- `/url-test` - URL testing page

#### Private URLs (Perlu Authentication)
- `/main` - Main dashboard → redirect ke login jika belum login
- `/form-keagenan` - Private recruitment form → redirect ke login
- `/profile` - Profile page → redirect ke login

### Expected Behavior
- **Tanpa Login**: Public URLs accessible, private URLs redirect ke login
- **Dengan Login**: Semua URLs accessible
- **Root URL**: Smart redirect berdasarkan status login

## Keamanan

1. **Public API**: Pastikan endpoint public benar-benar aman dan tidak expose data sensitif
2. **Validation**: Implementasikan validasi yang ketat di server untuk endpoint public
3. **Rate Limiting**: Implementasikan rate limiting untuk endpoint public
4. **CORS**: Konfigurasi CORS dengan benar untuk web access

## Troubleshooting

### Middleware tidak bekerja
- Pastikan SharedPreferences sudah di-inject ke GetX di main.dart
- Pastikan middleware ditambahkan ke route yang benar

### Public API tidak bekerja
- Pastikan endpoint server mendukung akses tanpa authentication
- Check network logs untuk memastikan Authorization header tidak dikirim

### Page tidak accessible
- Check middleware configuration di route
- Pastikan menggunakan wrapper yang benar (PublicPageWrapper vs PageWrapper)
