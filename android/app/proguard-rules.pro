# Google ML Kit Text Recognition (Latin only)
-keep class com.google.mlkit.vision.text.** { *; }
-keep class com.google.mlkit.common.** { *; }
-keep class com.google.android.gms.internal.mlkit_vision_text_common.** { *; }
-dontwarn com.google.mlkit.**

# Firebase and FCM rules
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Flutter Local Notifications
-keep class com.dexterous.** { *; }
-keep class androidx.core.app.NotificationCompat** { *; }

# Suppress WorkSource warnings - ini yang menyebabkan warning
-dontwarn android.os.WorkSource
-keep class android.os.WorkSource { *; }
-dontwarn android.os.PowerManager$WakeLock

# Keep notification related classes
-keep class * extends android.app.Service
-keep class * extends android.content.BroadcastReceiver
-keep class androidx.work.** { *; }

# Flutter rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-dontwarn io.flutter.embedding.**

# OkHttp and gRPC rules
-keep class com.squareup.okhttp.** { *; }
-keep class okhttp3.** { *; }
-keep class okio.** { *; }
-dontwarn com.squareup.okhttp.**
-dontwarn okhttp3.**
-dontwarn okio.**

# gRPC rules
-keep class io.grpc.** { *; }
-dontwarn io.grpc.**
-keep class com.google.protobuf.** { *; }
-dontwarn com.google.protobuf.**

# Additional networking rules
-keep class javax.annotation.** { *; }
-dontwarn javax.annotation.**
-keep class org.conscrypt.** { *; }
-dontwarn org.conscrypt.**

# Suppress reflection warnings
-dontwarn java.lang.reflect.**
-keep class * {
    public <methods>;
    public <fields>;
}
