#!/bin/bash

# Usage: ./deploy.sh [environment] 
# Example: ./deploy.sh dev → Creates tag dev-0.1.11 (after dev-0.1.10)

# --- Validate Input ---
if [ -z "$1" ]; then
  echo "Error: Please specify an environment (e.g., dev, staging, prod)"
  exit 1
fi

ENV=$1  # e.g., "dev"

# --- Fetch Latest Tags ---
git fetch --tags

# --- Get Latest Tag for the Environment (Sorted by Version) ---
# Use grep to filter exact pattern: env-number.number.number (e.g., dev-0.1.0, not dev-patch-0.1.0)
# echo "Looking for tags with exact pattern: $ENV-[0-9]*.[0-9]*.[0-9]*"
# echo "Available tags for this environment:"
AVAILABLE_TAGS=$(git tag --list "$ENV-*" --sort=-v:refname | grep -E "^$ENV-[0-9]+\.[0-9]+\.[0-9]+$")
# echo "$AVAILABLE_TAGS"
LATEST_TAG=$(echo "$AVAILABLE_TAGS" | head -n 1)
echo "Latest tag: $LATEST_TAG"

if [ -z "$LATEST_TAG" ]; then
  # No tag exists for this env → start with 0.1.0
  NEW_TAG="$ENV-0.1.0"
  echo "No existing tag for '$ENV'. Starting with: $NEW_TAG"
else
  # Extract version (e.g., dev-0.1.10 → 0.1.10)
  VERSION=${LATEST_TAG#$ENV-}

  # Split into parts (0, 1, 10)
  IFS='.' read -r major minor patch <<< "$VERSION"

  # Increment patch (0.1.10 → 0.1.11)
  NEW_PATCH=$((patch + 1))
  NEW_TAG="$ENV-$major.$minor.$NEW_PATCH"
  echo "Incremented tag: $LATEST_TAG → $NEW_TAG"
fi

# --- Validate Git Status (Optional) ---
if ! git diff-index --quiet HEAD --; then
  read -p "Warning: Uncommitted changes exist. Continue? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# --- Create and Push the New Tag ---
git tag -a "$NEW_TAG" -m "Release $NEW_TAG ($ENV environment)"
git push origin "$NEW_TAG"

echo "Successfully created and pushed: $NEW_TAG"

# --- Trigger Codemagic Build ---
# Get details apps id via api : https://api.codemagic.io/apps
CODEMAGIC_WEBHOOK_URL="https://api.codemagic.io/builds"
CODEMAGIC_APP_ID="67c6fd04a46735a7b802ae63"
CODEMAGIC_API_KEY="S4pg-yK57hPpOxMjCuUosrIn_Pwv0E2nZWGeGyWWAtE"

# Determine which workflow to trigger based on environment
echo "Environment detected: '$ENV'"

if [ "$ENV" == "dev" ]; then
  WORKFLOW_ID="67c70667885e07cbcfe6d6a7" #release-Development
  echo "Using dev workflow ID: $WORKFLOW_ID"
elif [ "$ENV" == "dev-patch" ]; then
  WORKFLOW_ID="68649f5a2cd01864357ec608" #patch-Development
  echo "Using dev-patch workflow ID: $WORKFLOW_ID"
else
  echo "Error: Unsupported environment '$ENV'. Supported environments: dev, dev-patch"
  exit 1
fi

# Trigger the build via webhook
echo "Triggering Codemagic build for $ENV environment with tag $NEW_TAG..."
curl -X POST "$CODEMAGIC_WEBHOOK_URL" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: $CODEMAGIC_API_KEY" \
  -d '{
    "appId": "'"$CODEMAGIC_APP_ID"'",
    "workflowId": "'"$WORKFLOW_ID"'",
    "tag": "'"$NEW_TAG"'"
  }'

echo "Build triggered successfully!"
