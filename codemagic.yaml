workflows:
  dev-workflow:
    name: Dev Build
    instance_type: mac_mini_m1
    environment:
      flutter: stable
      xcode: latest
      cocoapods: default
    triggering:
      events:
        - tag
      tag_patterns:
        - pattern: 'dev-*'
          include: true
    scripts:
      - name: Set up Flutter
        script: |
          flutter pub get
          flutter clean
      - name: Build for dev flavor
        script: |
          flutter build appbundle --flavor dev --target lib/main_dev.dart
          flutter build ipa --flavor dev --target lib/main_dev.dart
    artifacts:
      - build/app/outputs/bundle/devRelease/app-dev-release.aab
      - build/ios/ipa/*.ipa
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>

  production-workflow:
    name: Production Build
    instance_type: mac_mini_m1
    environment:
      flutter: stable
      xcode: latest
      cocoapods: default
    triggering:
      events:
        - tag
      tag_patterns:
        - pattern: 'prod-*'
          include: true
    scripts:
      - name: Set up Flutter
        script: |
          flutter pub get
          flutter clean
      - name: Build for production flavor
        script: |
          flutter build appbundle --flavor production --target lib/main_production.dart
          flutter build ipa --flavor production --target lib/main_production.dart
    artifacts:
      - build/app/outputs/bundle/productionRelease/app-production-release.aab
      - build/ios/ipa/*.ipa
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>