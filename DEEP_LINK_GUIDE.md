# Deep Link Implementation Guide

## Overview
Aplikasi QuantumX-360 telah dikonfigurasi untuk mendukung deep linking dengan dua jenis scheme:
1. **Web Scheme**: `https://sandbox-quantumx.panindai-ichilife.co.id`
2. **Custom Scheme**: 
   - Production: `quantumx360://`
   - Development: `quantumx360dev://`

## Supported Deep Link Routes

### Web Scheme (https://sandbox-quantumx.panindai-ichilife.co.id)
- `https://sandbox-quantumx.panindai-ichilife.co.id/home` → Home Page
- `https://sandbox-quantumx.panindai-ichilife.co.id/profile` → Profile Page
- `https://sandbox-quantumx.panindai-ichilife.co.id/keagenan` → Keagenan Menu
- `https://sandbox-quantumx.panindai-ichilife.co.id/inbox` → Inbox
- `https://sandbox-quantumx.panindai-ichilife.co.id/notification` → Notifications
- `https://sandbox-quantumx.panindai-ichilife.co.id/reset-password?token=TOKEN` → Reset Password
- `https://sandbox-quantumx.panindai-ichilife.co.id/public/recruitment?recruiter=ID&candidate=ID` → Public Recruitment Form
- `https://sandbox-quantumx.panindai-ichilife.co.id/public/signature-only` → Public Signature Only

### Custom Scheme
#### Production (quantumx360://)
- `quantumx360://home` → Home Page
- `quantumx360://profile` → Profile Page
- `quantumx360://keagenan` → Keagenan Menu
- `quantumx360://recruitment` → Recruitment List
- `quantumx360://inbox` → Inbox
- `quantumx360://notification` → Notifications
- `quantumx360://reset-password?token=TOKEN` → Reset Password
- `quantumx360://public/recruitment?recruiter=ID&candidate=ID` → Public Recruitment Form

#### Development (quantumx360dev://)
- `quantumx360dev://home` → Home Page
- `quantumx360dev://profile` → Profile Page
- `quantumx360dev://keagenan` → Keagenan Menu
- `quantumx360dev://recruitment` → Recruitment List
- `quantumx360dev://inbox` → Inbox
- `quantumx360dev://notification` → Notifications
- `quantumx360dev://reset-password?token=TOKEN` → Reset Password
- `quantumx360dev://public/recruitment?recruiter=ID&candidate=ID` → Public Recruitment Form

## Testing Deep Links

### Android Testing
1. **ADB Command**:
   ```bash
   # Web scheme
   adb shell am start -W -a android.intent.action.VIEW -d "https://sandbox-quantumx.panindai-ichilife.co.id/home" id.co.panindaiichilife.quantumx360
   
   # Custom scheme - Production
   adb shell am start -W -a android.intent.action.VIEW -d "quantumx360://home" id.co.panindaiichilife.quantumx360
   
   # Custom scheme - Development
   adb shell am start -W -a android.intent.action.VIEW -d "quantumx360dev://home" id.co.panindaiichilife.quantumx360
   ```

2. **Browser Testing**:
   - Buka browser di Android
   - Ketik URL: `https://sandbox-quantumx.panindai-ichilife.co.id/home`
   - Aplikasi akan terbuka jika sudah terinstall

### iOS Testing
1. **Safari Testing**:
   - Buka Safari di iOS
   - Ketik URL: `https://sandbox-quantumx.panindai-ichilife.co.id/home`
   - Aplikasi akan terbuka jika sudah terinstall

2. **Custom Scheme Testing**:
   - Buka Safari di iOS
   - Ketik: `quantumx360://home` (production) atau `quantumx360dev://home` (development)
   - Aplikasi akan terbuka jika sudah terinstall

### Testing dengan Parameter
```bash
# Reset password dengan token
adb shell am start -W -a android.intent.action.VIEW -d "quantumx360://reset-password?token=abc123" id.co.panindaiichilife.quantumx360

# Public recruitment dengan parameter
adb shell am start -W -a android.intent.action.VIEW -d "quantumx360://public/recruitment?recruiter=123&candidate=456" id.co.panindaiichilife.quantumx360
```

## Configuration Files Modified

### Android
- `android/app/src/main/AndroidManifest.xml` - Main manifest dengan web scheme
- `android/app/src/dev/AndroidManifest.xml` - Development flavor dengan custom scheme `quantumx360dev`
- `android/app/src/production/AndroidManifest.xml` - Production flavor dengan custom scheme `quantumx360`

### iOS
- `ios/Runner/Info.plist` - Main Info.plist dengan custom scheme `quantumx360`
- `ios/Runner/Runner.entitlements` - Associated domains untuk web scheme
- `ios/Runner/dev/Info.plist` - Development flavor dengan custom scheme `quantumx360dev`
- `ios/Runner/dev/Runner.entitlements` - Development associated domains

### Domain Verification
- `.well-known/assetlinks.json` - Android App Links verification
- `.well-known/apple-app-site-association.json` - iOS Universal Links verification

## Implementation Details

### DeepLinkParser Enhancement
File `lib/utils/deep_link_parser.dart` telah diperbarui dengan:
- Support untuk multiple schemes (web dan custom)
- Routing yang lebih komprehensif
- Logging untuk debugging
- Error handling yang lebih baik
- Support untuk parameter query

### Key Features
1. **Automatic Scheme Detection**: Parser otomatis mendeteksi jenis scheme
2. **Flavor-aware**: Mendukung scheme berbeda untuk development dan production
3. **Parameter Support**: Mendukung query parameters untuk passing data
4. **Fallback Routing**: Redirect ke home jika path tidak dikenali
5. **Comprehensive Logging**: Log detail untuk debugging

## Deployment Notes

### Domain Setup
Untuk production, pastikan file `.well-known/` di-deploy ke domain `https://sandbox-quantumx.panindai-ichilife.co.id/`:
- `https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/assetlinks.json`
- `https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association`

### App Store Configuration
- Pastikan bundle ID dan team ID sudah benar di file `.well-known/apple-app-site-association.json`
- Update `TEAMID` dengan Apple Developer Team ID yang sebenarnya

### Google Play Configuration
- Pastikan SHA-256 fingerprint sudah benar di file `.well-known/assetlinks.json`
- Gunakan fingerprint dari keystore yang digunakan untuk signing APK production
