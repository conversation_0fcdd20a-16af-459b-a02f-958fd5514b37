# Notification Troubleshooting Guide

## Masalah Foreground Notification Android

### <PERSON><PERSON>ala
- Foreground notification tidak muncul di Android
- Log menunjukkan "Foreground message received" tapi tidak ada notifikasi yang tampil
- Local notification tidak berfungsi

### Penyebab Umum

1. **Notification Channel tidak terbuat dengan benar**
2. **Permission tidak diberikan**
3. **Firebase initialization timing issues**
4. **Local notification plugin tidak diinisialisasi dengan benar**

### Solusi yang Telah Diimplementasikan

#### 1. Perbaikan Notification Service (`lib/services/notification_service.dart`)

**Perubahan utama:**
- ✅ Menambahkan logging detail untuk debugging
- ✅ Memperbaiki notification channel creation dengan verification
- ✅ Menambahkan test methods untuk local notification
- ✅ Memperbaiki background message handler dengan Firebase initialization
- ✅ Menambahkan debug status method

**Fitur baru:**
```dart
// Test local notification
await notificationService.sendTestNotification();

// Debug status
notificationService.debugNotificationStatus();

// Reinitialize service
await notificationService.reinitialize();
```

#### 2. Perbaikan AndroidManifest.xml

**Perubahan:**
- ✅ Menambahkan `android:directBootAware="true"` untuk messaging service
- ✅ Menambahkan permission `com.google.android.c2dm.permission.SEND`
- ✅ Menambahkan default notification metadata:
  - Default notification icon
  - Default notification color
  - Default notification channel ID

#### 3. Helper Classes untuk Testing

**NotificationTestHelper** (`lib/utils/notification_test_helper.dart`):
- Comprehensive testing untuk semua aspek notification
- FCM availability testing
- Local notification testing
- Permission testing

**NotificationDebugWidget** (`lib/widgets/notification_debug_widget.dart`):
- UI widget untuk debugging notification
- Real-time status monitoring
- Test buttons untuk berbagai scenarios

### Cara Menggunakan

#### 1. Tambahkan Debug Widget ke Halaman

```dart
import 'package:pdl_superapp/widgets/notification_debug_widget.dart';

// Di dalam build method
Column(
  children: [
    // Your existing widgets
    NotificationDebugWidget(),
  ],
)
```

#### 2. Jalankan Test dari Code

```dart
import 'package:pdl_superapp/utils/notification_test_helper.dart';

// Quick test
await NotificationTestHelper.instance.quickDebugTest();

// Full comprehensive test
await NotificationTestHelper.instance.runFullNotificationTest();
```

#### 3. Manual Testing

```dart
final notificationService = NotificationService.instance;

// Check status
notificationService.debugNotificationStatus();

// Send test notification
await notificationService.sendTestNotification();

// Reinitialize if needed
await notificationService.reinitialize();
```

### Debugging Steps

1. **Check Logs**
   - Look for "🔔 Foreground message received" logs
   - Check if notification channel is created successfully
   - Verify FCM token is available

2. **Test Local Notifications**
   ```dart
   await NotificationService.instance.sendTestNotification();
   ```

3. **Check Permissions**
   ```dart
   await NotificationTestHelper.instance.testNotificationPermissions();
   ```

4. **Verify Firebase Setup**
   ```dart
   bool isAvailable = await NotificationService.instance.checkFCMAvailability();
   ```

### Expected Log Output

Ketika notification service berfungsi dengan benar, Anda akan melihat:

```
🔧 Initializing local notifications...
✅ Foreground notification presentation options set
📱 Local notifications initialized: true
🔧 Creating Android notification channel...
✅ Android notification channel created
📱 Available channels: 1
🧪 Testing local notification setup...
📱 Notification channels: 1
🔔 Notifications enabled: true
✅ Local notification test completed
✅ Local notifications setup complete
Setting up Firebase Messaging listeners...
✅ Firebase Messaging listeners setup complete
FCM Token: [your-token-here]
✅ NotificationService initialized successfully
```

### Troubleshooting Checklist

- [ ] Firebase project configured correctly
- [ ] google-services.json in correct location
- [ ] All required permissions in AndroidManifest.xml
- [ ] Notification channel created successfully
- [ ] FCM token generated
- [ ] Local notification test passes
- [ ] App has notification permission

### Common Issues & Solutions

#### Issue: "Accessing hidden method Landroid/os/WorkSource;->add(I)Z"
**Status:** ⚠️ **WARNING - CAN BE IGNORED**
**Explanation:** This is an Android system warning that occurs when Firebase/Google Play Services uses internal Android APIs. It does NOT affect notification functionality.
**Solution:**
- This warning is automatically suppressed in our implementation
- Added ProGuard rules to reduce these warnings
- Functionality remains unaffected

#### Issue: "Android plugin not available"
**Solution:** Ensure flutter_local_notifications plugin is properly installed and initialized

#### Issue: "FCM Token is null"
**Solution:** Check internet connection and Firebase configuration

#### Issue: "Notifications enabled: false"
**Solution:** Request notification permission from user

#### Issue: "Available channels: 0"
**Solution:** Notification channel creation failed, check logs for errors

#### Issue: Foreground notifications not showing
**Solution:**
1. Check if local notification channel is created
2. Verify notification permissions are granted
3. Test with `sendTestNotification()` method
4. Check if `_showLocalNotification()` is being called

### Testing Commands

```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run

# Check logs
flutter logs
```

### WorkSource Warning Solution

The "Accessing hidden method Landroid/os/WorkSource;->add(I)Z" warning is now handled:

1. **ProGuard Rules Added** - Suppresses WorkSource warnings
2. **Error Handling** - Specific handling for WorkSource-related warnings
3. **Logging Improvement** - Clear indication that these warnings can be ignored

### Testing the Fix

1. **Add Debug Widget to your page:**
```dart
import 'package:pdl_superapp/widgets/notification_debug_widget.dart';

// In your build method
NotificationDebugWidget()
```

2. **Use the "Test Foreground" button** - This runs comprehensive tests specifically for foreground notification issues

3. **Check logs for:**
   - ✅ Local notifications setup complete
   - ✅ Android notification channel created
   - ✅ Firebase Messaging listeners setup complete
   - 📱 Showing local notification for foreground message

### Expected Behavior After Fix

- **Foreground notifications should now appear** when app is open
- **WorkSource warnings are suppressed** and marked as ignorable
- **Comprehensive logging** shows exactly what's happening
- **Test methods** verify each component works correctly

### Next Steps

1. Run the debug widget to check current status
2. Use "Test Foreground" button for comprehensive testing
3. Send a real FCM message to test actual functionality
4. Check logs for detailed error messages
5. If issues persist, check Firebase Console settings

### Final Notes

- WorkSource warnings are **normal Android system warnings** and do not affect functionality
- The notification service now has **better error handling** and **detailed logging**
- **Local notifications are properly configured** for Android foreground display
- All **ProGuard rules** are in place to minimize warnings
