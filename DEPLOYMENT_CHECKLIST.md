# Deep Link Deployment Checklist

## Pre-Deployment Checklist

### 1. Domain Configuration
- [ ] Domain `https://sandbox-quantumx.panindai-ichilife.co.id` sudah aktif dan accessible
- [ ] SSL certificate sudah terpasang dan valid
- [ ] Web server sudah dikonfigurasi untuk serve file `.well-known/`

### 2. File .well-known Deployment
- [ ] Upload `assetlinks.json` ke `https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/assetlinks.json`
- [ ] Upload `apple-app-site-association` ke `https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association`
- [ ] Pastikan file dapat diakses tanpa extension (.json)
- [ ] Set proper MIME types:
  - `assetlinks.json`: `application/json`
  - `apple-app-site-association`: `application/json`

### 3. Android Configuration
- [ ] Update SHA-256 fingerprint di `assetlinks.json` dengan fingerprint dari keystore production
- [ ] Pastikan package name `id.co.panindaiichilife.quantumx360` sudah benar
- [ ] Test dengan `adb shell am start -W -a android.intent.action.VIEW -d "https://sandbox-quantumx.panindai-ichilife.co.id/home" id.co.panindaiichilife.quantumx360`

### 4. iOS Configuration
- [ ] Update Team ID di `apple-app-site-association.json` dengan Apple Developer Team ID yang sebenarnya
- [ ] Pastikan bundle ID `id.co.panindaiichilife.quantumx360` sudah benar
- [ ] Pastikan Associated Domains capability sudah enabled di Xcode
- [ ] Test dengan Safari: `https://sandbox-quantumx.panindai-ichilife.co.id/home`

## Deployment Steps

### 1. Build dan Deploy Aplikasi
```bash
# Build untuk Android
flutter build apk --release --flavor production
flutter build appbundle --release --flavor production

# Build untuk iOS
flutter build ios --release --flavor production
```

### 2. Deploy File .well-known
```bash
# Copy files ke web server
scp .well-known/assetlinks.json user@server:/var/www/sandbox-quantumx.panindai-ichilife.co.id/.well-known/
scp .well-known/apple-app-site-association.json user@server:/var/www/sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association
```

### 3. Nginx Configuration (jika menggunakan Nginx)
```nginx
server {
    listen 443 ssl;
    server_name sandbox-quantumx.panindai-ichilife.co.id;
    
    # SSL configuration
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # .well-known files
    location /.well-known/ {
        root /var/www/sandbox-quantumx.panindai-ichilife.co.id;
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
    }
    
    # Main application
    location / {
        # Your main app configuration
    }
}
```

### 4. Apache Configuration (jika menggunakan Apache)
```apache
<VirtualHost *:443>
    ServerName sandbox-quantumx.panindai-ichilife.co.id
    DocumentRoot /var/www/sandbox-quantumx.panindai-ichilife.co.id
    
    # SSL configuration
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # .well-known files
    <Directory "/var/www/sandbox-quantumx.panindai-ichilife.co.id/.well-known">
        Header set Content-Type "application/json"
        Header set Access-Control-Allow-Origin "*"
    </Directory>
</VirtualHost>
```

## Post-Deployment Testing

### 1. Verify .well-known Files
```bash
# Test Android assetlinks
curl -I https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/assetlinks.json

# Test iOS apple-app-site-association
curl -I https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association
```

### 2. Test Deep Links
```bash
# Run validation script
./scripts/test_deeplinks.sh validate

# Test Android (dengan device terhubung)
./scripts/test_deeplinks.sh android prod

# Test iOS (dengan simulator running)
./scripts/test_deeplinks.sh ios prod
```

### 3. Manual Testing
- [ ] Install aplikasi dari Play Store/App Store
- [ ] Test web links di browser mobile
- [ ] Test custom scheme links
- [ ] Test dengan parameter (reset password, recruitment)
- [ ] Test dari aplikasi lain (email, WhatsApp, dll)

## Troubleshooting

### Android Issues
1. **App Links tidak berfungsi**:
   - Periksa SHA-256 fingerprint di assetlinks.json
   - Pastikan domain verification berhasil: `adb shell pm get-app-links id.co.panindaiichilife.quantumx360`
   - Reset app link preferences: Settings > Apps > Default apps > Opening links

2. **Custom scheme tidak berfungsi**:
   - Periksa AndroidManifest.xml untuk flavor yang benar
   - Pastikan intent-filter sudah benar

### iOS Issues
1. **Universal Links tidak berfungsi**:
   - Periksa Team ID di apple-app-site-association
   - Pastikan Associated Domains capability enabled
   - Test dengan Safari, bukan Chrome

2. **Custom scheme tidak berfungsi**:
   - Periksa CFBundleURLSchemes di Info.plist
   - Pastikan scheme unik dan tidak konflik dengan app lain

### General Issues
1. **Deep link tidak redirect ke halaman yang benar**:
   - Periksa DeepLinkParser.parseData() method
   - Check logs untuk debugging
   - Pastikan routes sudah terdaftar di app_routes.dart

2. **File .well-known tidak accessible**:
   - Periksa web server configuration
   - Pastikan CORS headers sudah benar
   - Test dengan curl atau browser

## Monitoring dan Maintenance

### 1. Analytics Tracking
- Monitor deep link usage melalui Firebase Analytics
- Track conversion rate dari deep links
- Monitor error rates dan failed deep links

### 2. Regular Testing
- Setup automated testing untuk deep links
- Test setelah setiap deployment
- Monitor app store reviews untuk deep link issues

### 3. Documentation Updates
- Update dokumentasi jika ada perubahan routes
- Maintain list of supported deep links
- Update testing scripts jika ada perubahan

## Security Considerations

### 1. Input Validation
- Validate semua parameter dari deep links
- Sanitize input sebelum processing
- Implement rate limiting untuk prevent abuse

### 2. Authentication
- Pastikan deep links yang memerlukan auth sudah protected
- Implement proper session management
- Handle expired tokens dengan graceful

### 3. Privacy
- Jangan expose sensitive data di URL
- Use encrypted parameters jika diperlukan
- Implement proper logging (jangan log sensitive data)
