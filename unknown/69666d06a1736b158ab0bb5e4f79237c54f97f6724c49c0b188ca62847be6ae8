import 'package:flutter/material.dart';

typedef OnWidgetSizeChange = void Function(Size size);

class SizeReportingWidget extends StatefulWidget {
  final Widget child;
  final OnWidgetSizeChange onSizeChange;

  const SizeReportingWidget({
    super.key,
    required this.child,
    required this.onSizeChange,
  });

  @override
  // ignore: library_private_types_in_public_api
  _SizeReportingWidgetState createState() => _SizeReportingWidgetState();
}

class _SizeReportingWidgetState extends State<SizeReportingWidget> {
  final _widgetKey = GlobalKey();
  Size? _oldSize;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _notifySize());
  }

  void _notifySize() {
    final context = _widgetKey.currentContext;
    if (context == null) return;

    final newSize = context.size;
    if (_oldSize != newSize && newSize != null) {
      _oldSize = newSize;
      widget.onSizeChange(newSize);
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((_) => _notifySize());
    return Container(key: _widgetKey, child: widget.child);
  }
}
