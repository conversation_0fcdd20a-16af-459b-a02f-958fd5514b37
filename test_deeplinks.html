<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuantumX-360 Deep Link Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .link-group {
            margin-bottom: 15px;
        }
        .link-group h3 {
            color: #666;
            margin-bottom: 10px;
        }
        .link-button {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .link-button:hover {
            background-color: #0056b3;
        }
        .custom-scheme {
            background-color: #28a745;
        }
        .custom-scheme:hover {
            background-color: #1e7e34;
        }
        .web-scheme {
            background-color: #17a2b8;
        }
        .web-scheme:hover {
            background-color: #117a8b;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .flavor-toggle {
            text-align: center;
            margin-bottom: 20px;
        }
        .flavor-button {
            padding: 10px 20px;
            margin: 0 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .flavor-button.active {
            background-color: #007bff;
            color: white;
        }
        .flavor-button:not(.active) {
            background-color: #e9ecef;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 QuantumX-360 Deep Link Testing</h1>
        
        <div class="flavor-toggle">
            <button class="flavor-button active" onclick="switchFlavor('production')">Production</button>
            <button class="flavor-button" onclick="switchFlavor('dev')">Development</button>
        </div>

        <!-- Basic Navigation Links -->
        <div class="section">
            <h2>📱 Basic Navigation</h2>
            
            <div class="link-group">
                <h3>Custom Scheme Links</h3>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://home" data-dev="quantumx360dev://home">🏠 Home</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://profile" data-dev="quantumx360dev://profile">👤 Profile</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://keagenan" data-dev="quantumx360dev://keagenan">🏢 Keagenan</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://recruitment" data-dev="quantumx360dev://recruitment">📋 Recruitment</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://inbox" data-dev="quantumx360dev://inbox">📬 Inbox</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://notification" data-dev="quantumx360dev://notification">🔔 Notification</a>
            </div>

            <div class="link-group">
                <h3>Web Scheme Links (HTTPS)</h3>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/home" class="link-button web-scheme">🏠 Home</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/profile" class="link-button web-scheme">👤 Profile</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/keagenan" class="link-button web-scheme">🏢 Keagenan</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/inbox" class="link-button web-scheme">📬 Inbox</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/notification" class="link-button web-scheme">🔔 Notification</a>
            </div>
        </div>

        <!-- Authentication Links -->
        <div class="section">
            <h2>🔐 Authentication</h2>
            
            <div class="link-group">
                <h3>Reset Password (Custom Scheme)</h3>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://reset-password?token=sample_token_123" data-dev="quantumx360dev://reset-password?token=sample_token_123">🔑 Reset Password</a>
            </div>

            <div class="link-group">
                <h3>Reset Password (Web Scheme)</h3>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/reset-password?token=sample_token_123" class="link-button web-scheme">🔑 Reset Password</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/forget-password?token=sample_token_123" class="link-button web-scheme">🔑 Forget Password (Legacy)</a>
            </div>
        </div>

        <!-- Public Forms -->
        <div class="section">
            <h2>📝 Public Forms</h2>
            
            <div class="link-group">
                <h3>Recruitment Forms (Custom Scheme)</h3>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://public/recruitment" data-dev="quantumx360dev://public/recruitment">📋 Public Recruitment</a>
                <a href="#" class="link-button custom-scheme" data-prod="quantumx360://public/recruitment?recruiter=REC123&candidate=CAND456" data-dev="quantumx360dev://public/recruitment?recruiter=REC123&candidate=CAND456">📋 Recruitment with Params</a>
            </div>

            <div class="link-group">
                <h3>Public Forms (Web Scheme)</h3>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/public/recruitment" class="link-button web-scheme">📋 Public Recruitment</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/public/recruitment?recruiter=REC123&candidate=CAND456" class="link-button web-scheme">📋 Recruitment with Params</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/public/form-keagenan" class="link-button web-scheme">🏢 Public Keagenan Form</a>
                <a href="https://sandbox-quantumx.panindai-ichilife.co.id/public/signature-only" class="link-button web-scheme">✍️ Signature Only</a>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="note">
            <h3>📋 Testing Instructions:</h3>
            <ol>
                <li><strong>Mobile Testing:</strong> Open this page on your mobile device and tap the links</li>
                <li><strong>Desktop Testing:</strong> Use browser developer tools to simulate mobile device</li>
                <li><strong>Custom Scheme:</strong> Will only work if the app is installed on the device</li>
                <li><strong>Web Scheme:</strong> Will work in browsers and should open the app if installed</li>
                <li><strong>Fallback:</strong> If app is not installed, web scheme links will open in browser</li>
            </ol>
            
            <h3>🔧 Development Testing:</h3>
            <p>For development testing, use the <strong>Development</strong> flavor toggle above to test with <code>quantumx360dev://</code> scheme.</p>
            
            <h3>⚠️ Important Notes:</h3>
            <ul>
                <li>Custom scheme links require the app to be installed</li>
                <li>Web scheme links require proper domain verification setup</li>
                <li>Test on both Android and iOS devices</li>
                <li>Verify that parameters are correctly passed to the app</li>
            </ul>
        </div>
    </div>

    <script>
        let currentFlavor = 'production';

        function switchFlavor(flavor) {
            currentFlavor = flavor;
            
            // Update button states
            document.querySelectorAll('.flavor-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update custom scheme links
            document.querySelectorAll('.custom-scheme').forEach(link => {
                const url = flavor === 'production' ? link.dataset.prod : link.dataset.dev;
                if (url) {
                    link.href = url;
                }
            });
        }

        // Initialize with production flavor
        document.addEventListener('DOMContentLoaded', function() {
            switchFlavor('production');
        });

        // Add click handlers for custom scheme links
        document.querySelectorAll('.custom-scheme').forEach(link => {
            link.addEventListener('click', function(e) {
                // Let the browser handle the custom scheme
                // If app is not installed, browser will show an error or do nothing
                console.log('Attempting to open:', this.href);
            });
        });
    </script>
</body>
</html>
