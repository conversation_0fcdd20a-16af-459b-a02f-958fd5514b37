<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS Deeplink Test - QuantumX360</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-link {
            display: inline-block;
            padding: 12px 20px;
            margin: 10px 5px;
            background-color: #007AFF;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .test-link:hover {
            background-color: #0056CC;
        }
        .custom-scheme {
            background-color: #34C759;
        }
        .custom-scheme:hover {
            background-color: #28A745;
        }
        .instructions {
            background-color: #FFF3CD;
            border: 1px solid #FFEAA7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>iOS Deeplink Test - QuantumX360</h1>
        
        <div class="instructions">
            <h3>📱 Testing Instructions:</h3>
            <ol>
                <li>Make sure QuantumX360 app is installed on your iOS device</li>
                <li>Open this page in <strong>Safari</strong> (not Chrome or other browsers)</li>
                <li>Tap the links below to test deeplinks</li>
                <li>The app should open automatically if deeplinks are working</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🌐 Universal Links (Web Scheme)</h3>
            <p>These links use the web domain and should work with iOS Universal Links:</p>
            
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/home" class="test-link">Home</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/profile" class="test-link">Profile</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/keagenan" class="test-link">Keagenan</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/inbox" class="test-link">Inbox</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/notification" class="test-link">Notification</a>
            
            <br><br>
            <strong>Password Reset Links:</strong><br>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/change-password/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDA1NDUxNSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc1MTkxMTI0MCwiaWF0IjoxNzUxOTEwMzQwfQ.Nvc5MMTomw_DDEKH2KDp2EPa-H4oum_Pt9ugWuZYmIj0eXkVQ6RbKEHRrotPTTrbVKzjO-WZOyD7girvFLispw" class="test-link">Change Password (Token in Path)</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/reset-password?token=sample-token-123" class="test-link">Reset Password (Token in Query)</a>
        </div>

        <div class="test-section">
            <h3>🔗 Custom Scheme Links</h3>
            <p>These links use custom schemes and should work directly:</p>
            
            <a href="quantumx360://home" class="test-link custom-scheme">Home (Custom)</a>
            <a href="quantumx360://profile" class="test-link custom-scheme">Profile (Custom)</a>
            <a href="quantumx360://keagenan" class="test-link custom-scheme">Keagenan (Custom)</a>
            <a href="quantumx360://reset-password?token=sample-token-123" class="test-link custom-scheme">Reset Password (Custom)</a>
            <a href="quantumx360://change-password/sample-token-456" class="test-link custom-scheme">Change Password (Custom)</a>
        </div>

        <div class="test-section">
            <h3>🔧 Troubleshooting</h3>
            <p>If deeplinks are not working:</p>
            <ul>
                <li>Make sure you're using <strong>Safari</strong> browser on iOS</li>
                <li>Check that the app is properly installed</li>
                <li>Try uninstalling and reinstalling the app</li>
                <li>Check iOS Settings > General > iPhone Storage > QuantumX360 > Offload App, then reinstall</li>
                <li>Verify the apple-app-site-association file is accessible at: <br>
                    <code>https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association</code>
                </li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📋 Technical Details</h3>
            <p><strong>Team ID:</strong> VM5X5NX6K3</p>
            <p><strong>Bundle ID:</strong> id.co.panindaiichilife.quantumx360</p>
            <p><strong>Domain:</strong> sandbox-quantumx.panindai-ichilife.co.id</p>
            <p><strong>Custom Schemes:</strong> quantumx360:// (production), quantumx360dev:// (development)</p>
        </div>
    </div>
</body>
</html>
