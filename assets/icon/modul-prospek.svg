<svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="52" height="52" rx="26" fill="url(#paint0_linear_4098_28280)"/>
<path d="M32.0254 32.0012C32.2588 31.8199 32.4708 31.6079 32.8934 31.1852L38.1694 25.9079C38.2974 25.7812 38.2388 25.5612 38.0694 25.5012C37.2432 25.2148 36.4934 24.7433 35.8774 24.1226C35.2567 23.5066 34.7851 22.7568 34.4988 21.9306C34.4388 21.7612 34.2188 21.7026 34.0921 21.8306L28.8134 27.1066C28.3908 27.5292 28.1788 27.7412 27.9974 27.9746C27.7814 28.251 27.5983 28.547 27.4481 28.8626C27.3214 29.1292 27.2268 29.4146 27.0374 29.9826L26.7921 30.7159L26.4028 31.8826L26.0388 32.9759C25.9934 33.1129 25.987 33.2599 26.0203 33.4003C26.0536 33.5408 26.1253 33.6692 26.2273 33.7713C26.3294 33.8734 26.4578 33.9451 26.5983 33.9784C26.7388 34.0117 26.8857 34.0053 27.0228 33.9599L28.1161 33.5959L29.2828 33.2066L30.0161 32.9612C30.5841 32.7719 30.8694 32.6786 31.1361 32.5506C31.4516 32.3994 31.749 32.2163 32.0254 32.0012ZM39.8228 24.2559C40.3636 23.7148 40.6674 22.9811 40.6673 22.2161C40.6672 21.4511 40.3631 20.7174 39.8221 20.1766C39.281 19.6357 38.5473 19.3319 37.7823 19.332C37.0173 19.3322 36.2836 19.6362 35.7428 20.1772L35.5748 20.3479C35.4937 20.4272 35.4332 20.5252 35.3988 20.6333C35.3644 20.7414 35.3571 20.8563 35.3774 20.9679C35.4041 21.1106 35.4508 21.3212 35.5374 21.5706C35.7108 22.0706 36.0388 22.7266 36.6561 23.3439C37.2734 23.9612 37.9294 24.2892 38.4294 24.4626C38.6801 24.5492 38.8894 24.5959 39.0321 24.6226C39.1437 24.6417 39.2583 24.6338 39.3661 24.5995C39.474 24.5651 39.5721 24.5054 39.6521 24.4252L39.8228 24.2559Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.5627 14.2292C14 15.7905 14 18.3052 14 23.3332V28.6665C14 33.6945 14 36.2092 15.5627 37.7705C17.1253 39.3318 19.6387 39.3332 24.6667 39.3332H27.3333C32.3613 39.3332 34.876 39.3332 36.4373 37.7705C37.9747 36.2345 38 33.7758 38 28.9065L34.2427 32.6638C33.8827 33.0238 33.588 33.3185 33.256 33.5785C32.8666 33.8835 32.4444 34.1442 31.9973 34.3558C31.5882 34.5399 31.1678 34.698 30.7387 34.8292L27.656 35.8572C27.1666 36.0204 26.6415 36.0441 26.1394 35.9256C25.6374 35.8071 25.1782 35.5511 24.8135 35.1864C24.4487 34.8216 24.1927 34.3625 24.0743 33.8604C23.9558 33.3583 23.9795 32.8332 24.1427 32.3438L24.508 31.2505L25.1413 29.3492L25.1693 29.2612C25.3307 28.7785 25.4627 28.3838 25.644 28.0025C25.8573 27.5545 26.1164 27.1354 26.4213 26.7452C26.6813 26.4118 26.976 26.1185 27.336 25.7585L32.6773 20.4158L34.16 18.9332L34.3293 18.7638C34.7824 18.3094 35.3209 17.949 35.9138 17.7035C36.5067 17.4579 37.1423 17.3321 37.784 17.3332C37.5827 15.9598 37.192 14.9825 36.4373 14.2292C34.876 12.6665 32.3613 12.6665 27.3333 12.6665H24.6667C19.6387 12.6665 17.124 12.6665 15.5627 14.2292ZM19.6667 21.9998C19.6667 21.7346 19.772 21.4803 19.9596 21.2927C20.1471 21.1052 20.4015 20.9998 20.6667 20.9998H29.3333C29.5985 20.9998 29.8529 21.1052 30.0404 21.2927C30.228 21.4803 30.3333 21.7346 30.3333 21.9998C30.3333 22.2651 30.228 22.5194 30.0404 22.7069C29.8529 22.8945 29.5985 22.9998 29.3333 22.9998H20.6667C20.4015 22.9998 20.1471 22.8945 19.9596 22.7069C19.772 22.5194 19.6667 22.2651 19.6667 21.9998ZM19.6667 27.3332C19.6667 27.068 19.772 26.8136 19.9596 26.6261C20.1471 26.4385 20.4015 26.3332 20.6667 26.3332H24C24.2652 26.3332 24.5196 26.4385 24.7071 26.6261C24.8946 26.8136 25 27.068 25 27.3332C25 27.5984 24.8946 27.8527 24.7071 28.0403C24.5196 28.2278 24.2652 28.3332 24 28.3332H20.6667C20.4015 28.3332 20.1471 28.2278 19.9596 28.0403C19.772 27.8527 19.6667 27.5984 19.6667 27.3332ZM19.6667 32.6665C19.6667 32.4013 19.772 32.1469 19.9596 31.9594C20.1471 31.7719 20.4015 31.6665 20.6667 31.6665H22.6667C22.9319 31.6665 23.1862 31.7719 23.3738 31.9594C23.5613 32.1469 23.6667 32.4013 23.6667 32.6665C23.6667 32.9317 23.5613 33.1861 23.3738 33.3736C23.1862 33.5611 22.9319 33.6665 22.6667 33.6665H20.6667C20.4015 33.6665 20.1471 33.5611 19.9596 33.3736C19.772 33.1861 19.6667 32.9317 19.6667 32.6665Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_4098_28280" x1="26" y1="0" x2="26" y2="52" gradientUnits="userSpaceOnUse">
<stop stop-color="#EEA849"/>
<stop offset="1" stop-color="#F46B45"/>
</linearGradient>
</defs>
</svg>
