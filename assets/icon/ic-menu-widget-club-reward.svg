<svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="42" height="42" rx="21" fill="url(#paint0_linear_4098_31691)"/>
<path d="M15.26 30.388H15C14.057 30.388 13.586 30.388 13.293 30.095C13 29.804 13 29.332 13 28.389V27.277C13 26.759 13 26.5 13.133 26.268C13.266 26.036 13.467 25.92 13.869 25.686C16.515 24.147 20.272 23.281 22.779 24.776C22.9477 24.8767 23.0977 24.9993 23.229 25.144C23.3636 25.2911 23.4673 25.4636 23.534 25.6515C23.6006 25.8393 23.6289 26.0387 23.6172 26.2376C23.6054 26.4366 23.5539 26.6312 23.4656 26.8099C23.3772 26.9886 23.254 27.1478 23.103 27.278C22.9828 27.3922 22.8361 27.4746 22.676 27.518C22.796 27.504 22.911 27.4883 23.021 27.471C23.932 27.326 24.697 26.838 25.397 26.309L27.205 24.944C27.5278 24.7098 27.9163 24.5837 28.315 24.5837C28.7138 24.5837 29.1023 24.7098 29.425 24.944C29.998 25.377 30.174 26.09 29.811 26.672C29.388 27.35 28.792 28.217 28.22 28.747C27.648 29.277 26.794 29.751 26.098 30.087C25.326 30.46 24.474 30.674 23.607 30.815C21.849 31.099 20.017 31.055 18.277 30.697C17.2844 30.4927 16.2735 30.3885 15.26 30.388ZM19.861 12.363C20.368 11.454 20.621 11 21 11C21.379 11 21.632 11.454 22.139 12.363L22.269 12.598C22.414 12.857 22.486 12.986 22.598 13.071C22.71 13.156 22.85 13.188 23.13 13.251L23.384 13.309C24.368 13.531 24.86 13.643 24.977 14.019C25.094 14.395 24.759 14.788 24.088 15.572L23.914 15.775C23.724 15.998 23.629 16.109 23.586 16.247C23.543 16.385 23.557 16.534 23.586 16.831L23.612 17.101C23.714 18.148 23.764 18.671 23.458 18.904C23.152 19.137 22.691 18.924 21.77 18.5L21.531 18.39C21.27 18.27 21.139 18.21 21 18.21C20.861 18.21 20.73 18.27 20.469 18.39L20.23 18.5C19.31 18.925 18.848 19.137 18.542 18.904C18.236 18.671 18.286 18.148 18.388 17.102L18.414 16.831C18.443 16.534 18.457 16.385 18.414 16.247C18.371 16.109 18.276 15.997 18.086 15.775L17.912 15.572C17.242 14.788 16.906 14.395 17.023 14.019C17.14 13.643 17.632 13.531 18.616 13.309L18.87 13.251C19.15 13.188 19.29 13.156 19.402 13.071C19.514 12.986 19.586 12.857 19.73 12.598L19.861 12.363ZM28.43 16.682C28.684 16.227 28.81 16 29 16C29.19 16 29.316 16.227 29.57 16.682L29.635 16.799C29.707 16.929 29.743 16.993 29.799 17.036C29.855 17.079 29.925 17.094 30.065 17.126L30.192 17.154C30.684 17.266 30.93 17.321 30.988 17.51C31.046 17.699 30.879 17.894 30.544 18.286L30.457 18.387C30.362 18.499 30.314 18.555 30.293 18.624C30.272 18.693 30.279 18.767 30.293 18.916L30.306 19.051C30.356 19.574 30.382 19.836 30.229 19.952C30.076 20.068 29.846 19.962 29.385 19.75L29.265 19.695C29.135 19.635 29.069 19.605 29 19.605C28.93 19.605 28.865 19.635 28.734 19.695L28.615 19.75C28.155 19.962 27.925 20.068 27.771 19.952C27.618 19.836 27.643 19.574 27.694 19.051L27.707 18.916C27.721 18.766 27.729 18.692 27.707 18.624C27.686 18.554 27.638 18.499 27.543 18.387L27.456 18.286C27.121 17.894 26.953 17.698 27.012 17.51C27.071 17.322 27.316 17.266 27.808 17.154L27.935 17.126C28.075 17.094 28.145 17.078 28.201 17.036C28.257 16.993 28.293 16.928 28.365 16.799L28.43 16.682ZM12.43 16.682C12.685 16.227 12.81 16 13 16C13.19 16 13.316 16.227 13.57 16.682L13.635 16.799C13.707 16.929 13.743 16.993 13.799 17.036C13.855 17.079 13.925 17.094 14.065 17.126L14.192 17.154C14.684 17.266 14.93 17.321 14.989 17.51C15.047 17.698 14.879 17.894 14.544 18.286L14.457 18.387C14.362 18.499 14.314 18.555 14.293 18.624C14.272 18.693 14.279 18.767 14.293 18.916L14.306 19.051C14.356 19.574 14.382 19.836 14.229 19.952C14.076 20.068 13.845 19.962 13.385 19.75L13.265 19.695C13.135 19.635 13.069 19.605 13 19.605C12.93 19.605 12.865 19.635 12.734 19.695L12.615 19.75C12.155 19.962 11.925 20.068 11.771 19.952C11.618 19.836 11.643 19.574 11.694 19.051L11.707 18.916C11.721 18.766 11.729 18.692 11.707 18.624C11.686 18.554 11.638 18.499 11.543 18.387L11.456 18.286C11.121 17.894 10.953 17.698 11.011 17.51C11.07 17.321 11.316 17.266 11.808 17.154L11.935 17.126C12.075 17.094 12.145 17.078 12.201 17.036C12.257 16.993 12.293 16.928 12.365 16.799L12.43 16.682Z" fill="white"/>
<defs>
<linearGradient id="paint0_linear_4098_31691" x1="21" y1="0" x2="21" y2="42" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF9ABA"/>
<stop offset="1" stop-color="#DD5E89"/>
</linearGradient>
</defs>
</svg>
