# Stage 1: Flutter build environment
FROM debian:latest AS build-env

# Install system dependencies
RUN apt-get update && apt-get install -y curl git unzip xz-utils libglu1-mesa

# Define arguments and environment variables
ARG FLUTTER_SDK=/usr/local/flutter
ARG FLUTTER_VERSION=3.32.1
ARG APP=/app
ENV PATH="$FLUTTER_SDK/bin:$FLUTTER_SDK/bin/cache/dart-sdk/bin:$PATH"

# Install Flutter SDK
RUN git clone https://github.com/flutter/flutter.git $FLUTTER_SDK && \
    cd $FLUTTER_SDK && git fetch && git checkout $FLUTTER_VERSION

# Run flutter doctor (optional)
RUN flutter doctor -v

# Create app directory
RUN mkdir $APP
WORKDIR $APP

# Copy only pubspec files first to leverage Docker layer caching
COPY pubspec.yaml pubspec.lock ./

COPY .env .env

# Run dependency resolution (uses pubspec.lock if present)
RUN flutter pub get

# Now copy the rest of the source code
COPY . .

# Run flutter clean and build
RUN flutter clean
RUN flutter build web

# Stage 2: Serve with Nginx
FROM nginx:1.25.2-alpine

# Copy build output to Nginx web root
COPY --from=build-env /app/build/web /usr/share/nginx/html

# Copy optional .well-known directory
COPY --from=build-env /app/.well-known /usr/share/nginx/html/.well-known

# Copy custom Nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port and run Nginx
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
