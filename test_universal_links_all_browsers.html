<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Links Test - All Browsers</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .subtitle {
            text-align: center;
            opacity: 0.8;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .correction-box {
            background: rgba(255, 0, 0, 0.2);
            border: 2px solid rgba(255, 0, 0, 0.5);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .correction-box h3 {
            margin-top: 0;
            color: #ffcccb;
        }
        .test-section {
            margin: 30px 0;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .test-link {
            display: inline-block;
            padding: 15px 25px;
            margin: 10px 5px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .custom-scheme {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }
        .browser-test {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .browser-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .browser-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-working { background-color: #2ecc71; }
        .status-should-work { background-color: #f39c12; }
        code {
            background: rgba(0, 0, 0, 0.3);
            padding: 3px 8px;
            border-radius: 5px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Universal Links Test</h1>
        <p class="subtitle">QuantumX360 - All Browsers Support</p>
        
        <div class="correction-box">
            <h3>🚨 Koreksi Pemahaman Sebelumnya</h3>
            <p><strong>❌ SALAH:</strong> "Universal Links hanya bekerja di Safari"</p>
            <p><strong>✅ BENAR:</strong> Universal Links bekerja di <span class="highlight">SEMUA browser iOS</span> karena diproses di system level!</p>
        </div>

        <div class="instructions">
            <h3>📱 Cara Testing:</h3>
            <ol>
                <li>Pastikan app QuantumX360 sudah terinstall di iOS device</li>
                <li>Buka halaman ini di <strong>browser apapun</strong> di iOS</li>
                <li>Tap link di bawah - app harus terbuka otomatis</li>
                <li>Jika app tidak terbuka, cek troubleshooting di bawah</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🌐 Universal Links - Test di Semua Browser</h3>
            <p>Link ini harus bekerja di <strong>semua browser iOS</strong>:</p>
            
            <div class="browser-grid">
                <div class="browser-card">
                    <h4>🔵 Safari</h4>
                    <span class="status-indicator status-working"></span>Should work
                </div>
                <div class="browser-card">
                    <h4>🟡 Chrome</h4>
                    <span class="status-indicator status-working"></span>Should work
                </div>
                <div class="browser-card">
                    <h4>🦊 Firefox</h4>
                    <span class="status-indicator status-working"></span>Should work
                </div>
                <div class="browser-card">
                    <h4>🔷 Edge</h4>
                    <span class="status-indicator status-working"></span>Should work
                </div>
            </div>
            
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/home" class="test-link browser-test">🏠 Test Home</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/profile" class="test-link browser-test">👤 Test Profile</a>
            <a href="https://sandbox-quantumx.panindai-ichilife.co.id/change-password/eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIwMDA1NDUxNSIsInJvbGVzIjpbIlJPTEVfQUdFX0JEIl0sImV4cCI6MTc1MTkxMTI0MCwiaWF0IjoxNzUxOTEwMzQwfQ.Nvc5MMTomw_DDEKH2KDp2EPa-H4oum_Pt9ugWuZYmIj0eXkVQ6RbKEHRrotPTTrbVKzjO-WZOyD7girvFLispw" class="test-link browser-test">🔑 Test Change Password</a>
        </div>

        <div class="test-section">
            <h3>📱 Test dari Aplikasi Lain</h3>
            <p>Universal Links juga harus bekerja dari:</p>
            <ul>
                <li>📧 Email apps (Mail, Gmail, Outlook)</li>
                <li>💬 Messaging apps (WhatsApp, Telegram, iMessage)</li>
                <li>📱 Social media apps (Twitter, Facebook)</li>
                <li>📝 Notes apps</li>
                <li>🔗 Any app dengan WebView</li>
            </ul>
            
            <p><strong>Cara test:</strong></p>
            <ol>
                <li>Copy link ini: <code>https://sandbox-quantumx.panindai-ichilife.co.id/change-password/test-token</code></li>
                <li>Paste ke WhatsApp/iMessage dan kirim ke diri sendiri</li>
                <li>Tap link dari dalam WhatsApp/iMessage</li>
                <li>App QuantumX360 harus terbuka langsung</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔗 Custom Scheme Links</h3>
            <p>Sebagai backup, custom scheme juga tersedia:</p>
            
            <a href="quantumx360://home" class="test-link custom-scheme">🏠 Custom Home</a>
            <a href="quantumx360://profile" class="test-link custom-scheme">👤 Custom Profile</a>
            <a href="quantumx360://change-password/test-token-123" class="test-link custom-scheme">🔑 Custom Change Password</a>
        </div>

        <div class="test-section">
            <h3>🔧 Troubleshooting</h3>
            
            <h4>Jika Universal Links tidak bekerja:</h4>
            <ol>
                <li><strong>Check Server:</strong>
                    <br>File harus accessible di: <code>https://sandbox-quantumx.panindai-ichilife.co.id/.well-known/apple-app-site-association</code>
                    <br><em>(tanpa .json extension!)</em>
                </li>
                
                <li><strong>Clear iOS Cache:</strong>
                    <br>Delete app → Reinstall app
                    <br>iOS cache Universal Links configuration
                </li>
                
                <li><strong>Check App Installation:</strong>
                    <br>Pastikan app benar-benar terinstall dan bisa dibuka manual
                </li>
                
                <li><strong>Test Fallback:</strong>
                    <br>Jika app tidak terinstall, link harus buka di browser
                </li>
            </ol>

            <h4>Debug Information:</h4>
            <ul>
                <li><strong>Domain:</strong> sandbox-quantumx.panindai-ichilife.co.id</li>
                <li><strong>Bundle ID:</strong> id.co.panindaiichilife.quantumx360</li>
                <li><strong>Team ID:</strong> VM5X5NX6K3</li>
                <li><strong>Current Browser:</strong> <span id="browser-info">Detecting...</span></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <p>Setelah testing, catat hasil di sini:</p>
            <div class="browser-grid">
                <div class="browser-card">
                    <h4>Safari</h4>
                    <span class="status-indicator status-should-work"></span>
                    <span id="safari-result">Not tested</span>
                </div>
                <div class="browser-card">
                    <h4>Chrome</h4>
                    <span class="status-indicator status-should-work"></span>
                    <span id="chrome-result">Not tested</span>
                </div>
                <div class="browser-card">
                    <h4>Firefox</h4>
                    <span class="status-indicator status-should-work"></span>
                    <span id="firefox-result">Not tested</span>
                </div>
                <div class="browser-card">
                    <h4>WhatsApp</h4>
                    <span class="status-indicator status-should-work"></span>
                    <span id="whatsapp-result">Not tested</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Detect browser
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('CriOS')) return 'Chrome iOS';
            if (ua.includes('FxiOS')) return 'Firefox iOS';
            if (ua.includes('EdgiOS')) return 'Edge iOS';
            if (ua.includes('Safari') && !ua.includes('Chrome')) return 'Safari iOS';
            if (ua.includes('WhatsApp')) return 'WhatsApp WebView';
            return 'Unknown iOS Browser';
        }

        document.getElementById('browser-info').textContent = getBrowserInfo();

        // Track link clicks for testing
        document.querySelectorAll('.test-link').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('🔗 Link clicked:', this.href);
                console.log('🌐 Browser:', getBrowserInfo());
                console.log('⏰ Time:', new Date().toISOString());
            });
        });
    </script>
</body>
</html>
