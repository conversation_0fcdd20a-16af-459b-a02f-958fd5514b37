{"description": "Contoh payload FCM untuk testing berbagai skenario navigasi", "server_key_note": "Ganti YOUR_SERVER_KEY dengan server key dari Firebase Console", "endpoint": "https://fcm.googleapis.com/fcm/send", "headers": {"Authorization": "key=YOUR_SERVER_KEY", "Content-Type": "application/json"}, "payloads": {"basic_notification": {"description": "Notifikasi dasar tanpa navigasi", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "PDL SuperApp", "body": "Selamat datang di aplikasi PDL SuperApp!"}}}, "home_navigation": {"description": "Navi<PERSON><PERSON> ke halaman home", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Kembali ke Home", "body": "Tap untuk kembali ke halaman utama"}, "data": {"screen": "home"}}}, "inbox_navigation": {"description": "Navi<PERSON>i ke halaman inbox", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "<PERSON><PERSON>", "body": "Anda memiliki pesan baru di inbox"}, "data": {"screen": "inbox"}}}, "profile_navigation": {"description": "Na<PERSON><PERSON><PERSON> ke halaman profile", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Update Profile", "body": "<PERSON><PERSON><PERSON>ui informasi profile <PERSON><PERSON>"}, "data": {"screen": "profile"}}}, "detail_with_id": {"description": "Navigasi ke halaman detail dengan ID", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Detail Information", "body": "Lihat detail informasi terbaru"}, "data": {"screen": "detail", "id": "12345"}}}, "route_based_navigation": {"description": "Navigasi menggunakan route langsung", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Notification Test", "body": "<PERSON>uka halaman test notifikasi"}, "data": {"route": "/notification-test"}}}, "route_with_arguments": {"description": "Navi<PERSON>i dengan arguments kompleks", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Detail Page", "body": "Buka halaman detail dengan parameter"}, "data": {"route": "/detail", "arguments": {"id": "67890", "type": "notification", "source": "fcm"}}}}, "topic_notification": {"description": "Notifikasi ke semua subscriber topic 'general'", "payload": {"to": "/topics/general", "notification": {"title": "<PERSON><PERSON><PERSON>", "body": "Ini adalah pengumuman untuk semua pengguna"}, "data": {"screen": "home", "announcement": "true"}}}, "silent_notification": {"description": "Notifikasi tanpa suara (data-only)", "payload": {"to": "FCM_TOKEN_HERE", "data": {"type": "silent", "action": "sync_data", "timestamp": "2024-01-01T00:00:00Z"}}}, "high_priority": {"description": "Notifikasi prioritas tinggi", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Urgent!", "body": "<PERSON><PERSON> penting yang memerlukan perhatian segera"}, "data": {"screen": "inbox", "priority": "high"}, "android": {"priority": "high", "notification": {"channel_id": "pdl_superapp_channel", "sound": "default", "color": "#FF0000"}}, "apns": {"headers": {"apns-priority": "10"}, "payload": {"aps": {"sound": "default", "badge": 1}}}}}, "scheduled_notification": {"description": "Notifikasi dengan waktu tertentu (gunakan Firebase Functions)", "payload": {"to": "FCM_TOKEN_HERE", "notification": {"title": "Reminder", "body": "<PERSON>an lupa untuk check aplikasi hari ini"}, "data": {"screen": "home", "scheduled": "true"}}}}, "testing_steps": ["1. Copy FCM token dari halaman test (/notification-test)", "2. G<PERSON> 'FCM_TOKEN_HERE' dengan token yang sudah di-copy", "3. <PERSON><PERSON> 'YOUR_SERVER_KEY' dengan server key dari Firebase Console", "4. <PERSON> den<PERSON> atau cURL:", "   curl -X POST https://fcm.googleapis.com/fcm/send \\", "   -H 'Authorization: key=YOUR_SERVER_KEY' \\", "   -H 'Content-Type: application/json' \\", "   -d 'PAYLOAD_JSON_HERE'", "5. Test berbagai kondisi app:", "   - Foreground: App terbuka dan aktif", "   - Background: App <PERSON> tapi masih running", "   - Terminated: <PERSON><PERSON> benar-benar ditutup", "6. <PERSON><PERSON><PERSON><PERSON><PERSON> navigasi bekerja dengan benar", "7. Check log di console untuk debugging"], "firebase_console_steps": ["1. Buka Firebase Console (https://console.firebase.google.com)", "2. Pilih project PDL SuperApp", "3. <PERSON><PERSON><PERSON> ke Cloud Messaging", "4. <PERSON><PERSON> 'Send your first message'", "5. Isi Notification title dan text", "6. Pilih target: Single device (paste FCM token)", "7. Advanced options > Custom data:", "   - Key: screen, Value: home", "   - Key: id, Value: 123", "8. Review dan Send message", "9. Test di berbagai kondisi app"], "troubleshooting": {"token_not_generated": ["Pastikan Firebase sudah diinisialisasi", "Check permission sudah granted", "<PERSON><PERSON>", "Check internet connection"], "notification_not_received": ["Verifikasi server key benar", "Check FCM token masih valid", "Pastikan payload format benar", "Check device notification settings"], "navigation_not_working": ["Verifikasi route sudah terdaftar di GetX", "Check payload data format", "Lihat log error di console", "Pastikan middleware tidak memblokir"], "web_notifications_not_working": ["Pastikan HTTPS (required untuk web push)", "Check service worker terdaftar", "Verifikasi browser support", "Check browser notification permission"]}}